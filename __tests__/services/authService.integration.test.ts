// __tests__/services/authService.integration.test.ts
import {
  login,
  logoutUser,
  refreshToken,
  sendEmailVerification,
  verifyEmail,
} from "../../src/services/auth.services";

describe("Auth Service Integration Tests", () => {
  let token: string;
  let refreshTokenValue: string;
  const testEmail = process.env.TEST_SUPER_ADMIN_EMAIL!;
  const testPassword = process.env.TEST_SUPER_ADMIN_PASS!;

  it("logs in a user with valid credentials", async () => {
    const res = await login({ email: testEmail, password: testPassword });

    expect(res).toHaveProperty("accessToken");
    expect(res).toHaveProperty("refreshToken");
    expect(res).toHaveProperty("user");
    expect(res.user.email).toBe(testEmail);

    // Save tokens for later tests
    token = res.accessToken;
    refreshTokenValue = res.refreshToken;
  });

  it("fails login with invalid credentials", async () => {
    await expect(
      login({ email: testEmail, password: "wrongpassword" }),
    ).rejects.toThrow();
  });

  it("refreshes token with valid refresh token", async () => {
    const res = await refreshToken(refreshTokenValue);
    expect(res).toHaveProperty("accessToken");
    expect(res).toHaveProperty("refreshToken");
  });

  it("sends email verification when authenticated", async () => {
    const res = await sendEmailVerification(token);
    expect(res.status).toBe("success");
    expect(res.message).toBe("Verification email sent successfully");
  });

  it("fails sending email verification with invalid token", async () => {
    await expect(sendEmailVerification("invalid-token")).rejects.toThrow();
  });

  it("verifies email with token", async () => {
    // This test requires a valid verification token; usually generated by the backend
    const verificationToken = process.env.TEST_EMAIL_VERIFICATION_TOKEN;
    if (verificationToken) {
      const res = await verifyEmail(verificationToken);
      expect(res.success).toBe(true);
    } else {
      console.warn(
        "Skipping verifyEmail test: TEST_EMAIL_VERIFICATION_TOKEN not set",
      );
    }
  });

  it("fails verifying email with invalid token", async () => {
    await expect(verifyEmail("invalid-token")).rejects.toThrow();
  });

  it("logs out a user with valid token", async () => {
    const res = await logoutUser(token);
    expect(res.status).toBe("success");
    expect(res.message).toBe("Logout successful");
  });

  it("fails logout with invalid token", async () => {
    await expect(logoutUser("invalid-token")).rejects.toThrow();
  });
});
