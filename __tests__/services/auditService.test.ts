// __tests__/services/auditService.integration.test.ts
import {
  getUserAuditLogs,
  getAuditLogsByAction,
  getRecentAuditLogs,
} from "../../src/services/audit.services";
import { login } from "../../src/services/auth.services";

describe("Audit Service Integration Tests", () => {
  let token: string;
  let userId: string;
  const testAction = "login";

  beforeAll(async () => {
    // Log in and get a valid token
    const res = await login({
      email: process.env.TEST_SUPER_ADMIN_EMAIL!,
      password: process.env.TEST_SUPER_ADMIN_PASS!,
    });

    token = res.accessToken;
    userId = res.user._id;
  });
  it("fetches audit logs for a specific user", async () => {
    const res = await getUserAuditLogs(userId, 1, 50, token);
    expect(res.status).toBe("success");
    expect(Array.isArray(res.data)).toBe(true);
    if (res.data.length > 0) {
      expect(res.data[0]).toHaveProperty("user");
      expect(res.data[0].user._id).toBe(userId);
    }
  });

  it("fetches audit logs by action type", async () => {
    const res = await getAuditLogsByAction(testAction, 1, 50, token);

    expect(res.status).toBe("success");
    expect(Array.isArray(res.data)).toBe(true);
    if (res.data.length > 0) {
      expect(res.data[0]).toHaveProperty("action");
      expect(res.data[0].action).toBe(testAction);
    }
  });

  it("fetches recent audit logs", async () => {
    const res = await getRecentAuditLogs(1, 50, token);

    expect(res.status).toBe("success");
    expect(Array.isArray(res.data)).toBe(true);
  });

  it("throws an error if token is invalid", async () => {
    await expect(getRecentAuditLogs(1, 50, "invalid-token")).rejects.toThrow();
  });
});
