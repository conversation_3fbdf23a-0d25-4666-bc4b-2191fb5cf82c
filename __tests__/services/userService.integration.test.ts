// __tests__/services/userService.integration.test.ts
import {
  createStaffMember,
  createNgoAccount,
  deactivateUsers,
  activateUsers,
  getUsers,
  searchUsers,
  getUserProfile,
  updateUserById,
  User,
} from "../../src/services/users.services";
import { login } from "../../src/services/auth.services";

describe("User Service Integration Tests", () => {
  let token: string;
  let testUserId: string;
  let testNgoUserId: string;

  beforeAll(async () => {
    const res = await login({
      email: process.env.TEST_SUPER_ADMIN_EMAIL!,
      password: process.env.TEST_SUPER_ADMIN_PASS!,
    });

    token = res.accessToken;
  });

  it("creates a new staff member", async () => {
    const res = await createStaffMember(
      {
        fullname: "Test Staff",
        email: "<EMAIL>",
        role: process.env.TEST_STAFF_ROLE!,
      },
      token,
    );

    expect(res.status).toBe("success");
    expect(res.user).toHaveProperty("_id");
    expect(res.user?.email).toBe("<EMAIL>");

    testUserId = res.user!._id;
  });

  it("creates a new NGO account", async () => {
    const res = await createNgoAccount(
      {
        fullname: "Test NGO",
        email: "<EMAIL>",
        password: "TestPassword123!",
        ngoId: "NGO12345",
      },
      token,
    );

    expect(res.status).toBe("success");
    expect(res.data).toHaveProperty("_id");
    expect(res.data?.email).toBe("<EMAIL>");

    testNgoUserId = res.data!._id;
  });

  it("deactivates users", async () => {
    const res = await deactivateUsers([testUserId], token);
    expect(res.status).toBe("success");
  });

  it("activates users", async () => {
    const res = await activateUsers([testUserId], token);
    expect(res.status).toBe("success");
  });

  it("fetches all users", async () => {
    const res = await getUsers({ page: 1, limit: 10 }, token);

    expect(res.status).toBe("success");
    expect(Array.isArray(res.data)).toBe(true);
  });

  it("searches users by query", async () => {
    const res = await searchUsers("Test Staff", token);
    // console.log(res);
    expect(res.status).toBe("success");
    expect(Array.isArray(res.data)).toBe(true);
    if (res.data!.length > 0) {
      expect(res.data![0]).toHaveProperty("fullname");
    }
  });

  it("retrieves user profile", async () => {
    const res = await getUserProfile(token);

    expect(res.status).toBe("success");
    expect(res.message).toBe("User profile fetched successfully");
  });

  it("updates user by ID", async () => {
    const res = await updateUserById(
      testUserId,
      { fullname: "Updated Test Staff", role: process.env.TEST_STAFF_REGISTRY },
      token,
    );

    expect(res.status).toBe("success");
    expect(res.data?.fullname).toBe("Updated Test Staff");
    expect(res.data?.role).toBe("admin");
  });

  it("throws an error with invalid token", async () => {
    await expect(getUsers({}, "invalid-token")).rejects.toThrow();
  });
});
