// jest.setup.js
const dotenv = require("dotenv");
dotenv.config({ path: ".env.test" });

// Mock localStorage for Node
if (typeof global.localStorage === "undefined") {
  let store = {};
  global.localStorage = {
    getItem: (key) => store[key] || null,
    setItem: (key, value) => {
      store[key] = String(value);
    },
    removeItem: (key) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
}
