# CONGOMA - Membership Portal

**CONGOMA Membership Portal** is a modern, comprehensive membership management system built with Next.js 15 and React 19. This application provides a complete solution for managing organizational memberships, financial tracking, compliance monitoring, and administrative operations.

## 🚀 Features

### Core Functionality
- **Member Management**: Complete user registration, authentication, and profile management
- **Financial Tracking**: Comprehensive financial modules for membership fees, payments, and reporting
- **Organization Management**: Multi-level organization structure with network management
- **Compliance Monitoring**: Built-in compliance tracking and reporting systems
- **Document Management**: Centralized document storage and management
- **Project Management**: Track and manage organizational projects and programs

### Technical Features
- **Modern UI/UX**: Responsive design with dark/light theme support
- **Data Visualization**: Interactive charts and graphs using ApexCharts
- **Real-time Updates**: Dynamic content updates and notifications
- **Mobile Responsive**: Fully optimized for mobile and tablet devices
- **Type Safety**: Built with TypeScript for enhanced code reliability
- **Performance Optimized**: Leveraging Next.js 15 App Router for optimal performance

## 🛠️ Tech Stack

- **Framework**: Next.js 15.4.4 with App Router
- **Frontend**: React 19, TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Custom components with Lucide React icons
- **Charts**: ApexCharts for data visualization
- **Maps**: JSVectorMap for geographical data
- **Animations**: Framer Motion for smooth interactions
- **Date Handling**: Flatpickr and date-fns
- **Theme**: next-themes for dark/light mode support

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd congoma-frontend
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Start the development server:
```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### Build for Production

```bash
npm run build
npm start
# or
yarn build
yarn start
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── (admin)/           # Admin dashboard routes
│   │   ├── overview/      # Dashboard overview
│   │   ├── financial/     # Financial management
│   │   ├── organizations/ # Organization management
│   │   ├── networks/      # Network management
│   │   ├── projects/      # Project management
│   │   ├── compliance/    # Compliance tracking
│   │   └── users/         # User management
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable UI components
├── hooks/                 # Custom React hooks
├── services/              # API services
├── types/                 # TypeScript type definitions
├── utils/                 # Utility functions
└── css/                   # Custom CSS files
```

## 🎯 Key Modules

### Admin Dashboard
- **Overview**: Comprehensive dashboard with key metrics and analytics
- **Financial Management**: Track membership fees, payments, and financial reports
- **Organization Management**: Manage organizational structure and hierarchies
- **Network Management**: Handle network connections and relationships
- **Project Management**: Track ongoing projects and programs
- **Compliance**: Monitor compliance status and generate reports
- **User Management**: Administer user accounts and permissions

### Public Features
- **Landing Page**: Modern, responsive homepage with organization information
- **Authentication**: Secure login and registration system
- **Newsletter**: Email subscription management
- **Mobile Navigation**: Optimized mobile menu system
- Styling facilitated by **Tailwind CSS** files.
- A design that resonates premium quality and high aesthetics.
- A handy UI kit with assets.
- Over ten web apps complete with examples.
- Support for both **dark mode** and **light mode**.
- Essential integrations including - Authentication (**NextAuth**), Database (**Postgres** with **Prisma**), and Search (**Algolia**).
- Detailed and user-friendly documentation.
- Customizable plugins and add-ons.
- **TypeScript** compatibility.
- Plus, much more!

All these features and more make **NextAdmin** a robust, well-rounded solution for all your dashboard development needs.

## Update Logs

### Version 1.2.1 - [Mar 20, 2025]
- Fix Peer dependency issues and NextConfig warning.
- Updated apexcharts and react-apexhcarts to the latest version.

### Version 1.2.0 - Major Upgrade and UI Improvements - [Jan 27, 2025]

- Upgraded to Next.js v15 and updated dependencies
- API integration with loading skeleton for tables and charts.
- Improved code structure for better readability.
- Rebuilt components like dropdown, sidebar, and all ui-elements using accessibility practices.
- Using search-params to store dropdown selection and refetch data.
- Semantic markups, better separation of concerns and more.

### Version 1.1.0
- Updated Dependencies
- Removed Unused Integrations
- Optimized App

### Version 1.0
- Initial Release - [May 13, 2024]
