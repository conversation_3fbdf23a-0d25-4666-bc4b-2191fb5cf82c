# CONGOMA Membership Portal - Codebase Documentation

## Overview

The CONGOMA Membership Portal is a comprehensive membership management system built with **Next.js 15** and **React 19**. It provides a complete solution for managing organizational memberships, financial tracking, compliance monitoring, and administrative operations for the Council of Non-Governmental Organizations in Malawi (CONGOMA).

## Technology Stack

- **Framework**: Next.js 15.4.4 with App Router
- **Frontend**: React 19, TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Custom components with Lucide React icons
- **Charts**: ApexCharts for data visualization
- **Maps**: JSVectorMap for geographical data
- **Animations**: Framer Motion for smooth interactions
- **Date Handling**: Flatpickr and date-fns
- **Theme**: next-themes for dark/light mode support
- **HTTP Client**: Custom fetch-based API client
- **State Management**: React hooks and localStorage

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── (admin)/           # Admin dashboard routes (role-based)
│   │   ├── overview/      # Dashboard overview
│   │   ├── financial/     # Financial management
│   │   ├── organizations/ # Organization management
│   │   ├── networks/      # Network management
│   │   ├── projects/      # Project management
│   │   ├── compliance/    # Compliance tracking
│   │   └── users/         # User management
│   ├── auth/              # Authentication pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable UI components
│   ├── Auth/              # Authentication components
│   ├── Charts/            # Chart components (ApexCharts)
│   ├── FormElements/      # Form input components
│   ├── Layouts/           # Layout components (Header, Sidebar)
│   ├── Tables/            # Table components
│   └── ui/                # Base UI components
├── services/              # API services layer
├── types/                 # TypeScript type definitions
├── utils/                 # Utility functions
├── hooks/                 # Custom React hooks
└── lib/                   # Shared libraries and utilities
```

## API Request Layer Architecture

### Base API Client (`src/services/api.ts`)

The application uses a custom HTTP client built on top of the Fetch API:

```typescript
// Base configuration
const API_BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}/api` || "http://localhost:3009/api";

// Generic request function
async function request<T>(
  endpoint: string,
  method: HttpMethod = "GET",
  body?: unknown,
  token?: string,
): Promise<T>
```

**Key Features:**
- Automatic JSON/FormData handling
- Bearer token authentication
- Error handling with structured responses
- TypeScript generics for type safety
- No caching by default (`cache: "no-store"`)

### Authentication System

**Location**: `src/services/auth.services.ts`

**User Roles:**
- `super_admin` - Full system access
- `ngo_admin` - NGO organization management
- `cso_chair` - CSO network leadership
- `staff_registry` - Registration management
- `staff_admin` - Administrative staff
- `finance_officer` - Financial operations
- `programmes_officer` - Program management

**Key Functions:**
- `login(credentials)` - User authentication
- `logout()` - Session termination
- `getCurrentUser()` - Get logged-in user data
- `hasRole(role)` - Role-based access control
- `verifyEmail(token)` - Email verification

**Token Management:**
- Access tokens stored in localStorage
- Automatic token inclusion in API requests
- Role-based navigation after login

### Service Layer Organization

Each domain has its own service file:

1. **Authentication** (`auth.services.ts`)
   - Login/logout functionality
   - User registration and verification
   - Role management

2. **User Management** (`users.services.ts`)
   - CRUD operations for users
   - Staff member creation
   - User activation/deactivation

3. **Organization Management** (`ngo.services.ts`)
   - NGO registration and management
   - Document upload handling
   - Organization approval workflows

4. **Location Services** (`location.services.ts`)
   - District and region data
   - Geographic information management

5. **CSO Networks** (`cso.services.ts`)
   - Network creation and management
   - Member approval workflows

6. **Financial Services** (`receipts.services.ts`)
   - Payment processing
   - Receipt generation
   - Financial reporting

7. **Statistics** (`statistics.services.ts`)
   - Role-based dashboard metrics
   - Performance analytics

### API Response Patterns

**Standard Response Format:**
```typescript
interface ApiResponse<T> {
  status: "success" | "error";
  message: string;
  data?: T;
}
```

**Paginated Responses:**
```typescript
interface PaginatedResponse<T> {
  status: "success" | "error";
  message: string;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}
```

## Component Architecture

### Layout System

**Main Layout** (`src/components/Layouts/`)
- **Header**: Navigation, search, theme toggle, user menu
- **Sidebar**: Role-based navigation menu
- **Responsive**: Mobile-first design with collapsible sidebar

**Layout Variants:**
- Admin layout with full sidebar
- Public layout for landing pages
- Specialized layouts for different sections

### Role-Based Navigation

**Navigation Configuration** (`src/components/Layouts/sidebar/data/`)
- Static navigation data (`index.ts`)
- Role-based navigation (`role-based-nav.ts`)
- Dynamic menu generation based on user role
- Conditional menu items based on organization status

### UI Components

**Base Components** (`src/components/ui/`)
- Card, Button, Input, Modal, Table
- Consistent design system
- Dark/light theme support

**Form Elements** (`src/components/FormElements/`)
- InputGroup, Select, Checkbox, Switch
- Date pickers and multi-select components
- Validation and error handling

**Charts** (`src/components/Charts/`)
- ApexCharts integration
- Responsive chart components
- Real-time data visualization

## Authentication & Authorization

### Authentication Flow

1. **Login Process:**
   - User submits credentials
   - API validates and returns tokens
   - Tokens stored in localStorage
   - User redirected based on role

2. **Protected Routes:**
   - `ProtectedRoute` component wraps secured pages
   - Automatic redirect to login if unauthenticated
   - Role-based access control

3. **Token Management:**
   - Access token for API requests
   - Refresh token for session renewal
   - Automatic logout on token expiration

### Role-Based Access Control

**Utility Functions** (`src/utils/auth.utils.ts`)
- `hasRole(role)` - Check specific role
- `hasAnyRole(roles)` - Check multiple roles
- `isSuperAdmin()` - Super admin check
- `isCongomaStaff()` - Staff role check

## Data Flow Patterns

### API Request Pattern

1. **Component Level:**
   ```typescript
   const [data, setData] = useState(null);
   const [loading, setLoading] = useState(true);
   
   useEffect(() => {
     const fetchData = async () => {
       try {
         const token = localStorage.getItem("accessToken");
         const response = await serviceFunction(params, token);
         setData(response.data);
       } catch (error) {
         console.error(error);
       } finally {
         setLoading(false);
       }
     };
     fetchData();
   }, []);
   ```

2. **Service Layer:**
   ```typescript
   export async function serviceFunction(params, token) {
     return request<ResponseType>("/endpoint", "GET", undefined, token);
   }
   ```

### Error Handling

- Service layer throws structured errors
- Components handle errors with try-catch
- User-friendly error messages
- Loading states for better UX

## Environment Configuration

**Required Environment Variables:**
- `NEXT_PUBLIC_API_URL` - Backend API base URL

**Default Configuration:**
- Development: `http://localhost:3009/api`
- Production: Set via environment variable

## Development Guidelines

### Adding New API Endpoints

1. Create service function in appropriate service file
2. Define TypeScript interfaces for request/response
3. Use the base `request` function with proper typing
4. Handle authentication tokens when required

### Creating New Components

1. Follow the established folder structure
2. Use TypeScript for all components
3. Implement responsive design with Tailwind CSS
4. Include proper accessibility attributes

### Role-Based Features

1. Check user roles using utility functions
2. Conditionally render UI elements
3. Protect API calls with role validation
4. Update navigation configuration as needed

## Testing

**Test Structure:**
- Integration tests for services (`__tests__/services/`)
- Jest configuration for TypeScript
- Mock API responses for testing

**Running Tests:**
```bash
npm test
```

## Build and Deployment

**Development:**
```bash
npm run dev
```

**Production Build:**
```bash
npm run build
npm start
```

**Linting:**
```bash
npm run lint
```

## Key Features by Role

### Super Admin Dashboard
- **Overview**: System-wide statistics and analytics
- **User Management**: Create and manage all user types
- **Organization Management**: Approve/reject NGO applications
- **Financial Oversight**: Revenue tracking and payment monitoring
- **System Configuration**: Settings and permissions management

### NGO Admin Dashboard
- **Organization Profile**: Manage NGO information and documents
- **Member Management**: Handle organization staff
- **Compliance Tracking**: Monitor compliance status
- **Financial Records**: View payments and receipts
- **Project Management**: Track organizational projects

### Staff Roles
- **Registry Staff**: NGO application processing and approval
- **Finance Officer**: Payment processing and financial reporting
- **Programs Officer**: Project and program management
- **Admin Staff**: General administrative functions

## API Endpoints Overview

### Authentication Endpoints
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/register` - User registration
- `POST /auth/verify-email` - Email verification
- `POST /auth/forgot-password` - Password reset request
- `POST /auth/reset-password` - Password reset

### User Management Endpoints
- `GET /users` - List users (with pagination and filters)
- `POST /users/staff` - Create staff member
- `POST /users/ngo` - Create NGO account
- `PUT /users/:id` - Update user
- `PATCH /users/activate` - Activate users
- `PATCH /users/deactivate` - Deactivate users
- `GET /users/profile` - Get user profile
- `GET /users/search` - Search users

### Organization Endpoints
- `POST /ngos/create` - Create NGO (with file uploads)
- `GET /ngos` - List NGOs (with filters)
- `GET /ngos/:id` - Get NGO by ID
- `PUT /ngos/:id` - Update NGO
- `PATCH /ngos/:id/approve` - Approve NGO
- `PATCH /ngos/:id/reject` - Reject NGO

### Location Endpoints
- `GET /locations/districts` - Get all districts
- `GET /locations/regions` - Get all regions

### CSO Network Endpoints
- `POST /cso-networks` - Create CSO network
- `GET /cso-networks` - List CSO networks
- `POST /cso-networks/:id/join` - Join network
- `PATCH /cso-networks/:id/approve-member` - Approve member

### Financial Endpoints
- `POST /receipts` - Create receipt
- `GET /receipts` - List receipts
- `GET /receipts/:id` - Get receipt details
- `GET /statistics/finance-officer` - Financial statistics

## File Upload Handling

The application handles file uploads for various documents:

**Supported File Types:**
- PDF documents
- Image files (JPG, PNG)
- Word documents (DOCX)

**Upload Process:**
1. Files are handled as FormData
2. Automatic content-type detection
3. Server-side validation and storage
4. URL references stored in database

**Example Upload Implementation:**
```typescript
const formData = new FormData();
formData.append('document', file);
formData.append('documentType', 'constitution');

const response = await createNgo(formData, token);
```

## State Management Patterns

### Local State Management
- React hooks (useState, useEffect) for component state
- Custom hooks for reusable logic
- Context API for theme and sidebar state

### Data Fetching Patterns
- Async/await with try-catch error handling
- Loading states for better UX
- Optimistic updates where appropriate

### Form State Management
- Controlled components with React hooks
- Form validation with custom logic
- Error state management

## Styling and Theming

### Tailwind CSS Configuration
- Custom color palette for CONGOMA branding
- Responsive breakpoints
- Dark/light theme support
- Custom font (Satoshi)

### Theme System
- `next-themes` for theme switching
- CSS custom properties for theme colors
- Automatic system theme detection

### Component Styling Patterns
- Utility-first approach with Tailwind
- Component variants using `class-variance-authority`
- Consistent spacing and typography scales

## Performance Optimizations

### Next.js Optimizations
- App Router for improved performance
- Automatic code splitting
- Image optimization with Next.js Image component
- Static generation where possible

### React Optimizations
- Lazy loading with React.lazy and Suspense
- Memoization with useMemo and useCallback
- Efficient re-rendering patterns

### API Optimizations
- Request deduplication
- Pagination for large datasets
- Caching strategies (disabled by default)

## Security Considerations

### Authentication Security
- JWT tokens for stateless authentication
- Secure token storage in localStorage
- Automatic token expiration handling

### Authorization Security
- Role-based access control (RBAC)
- Route-level protection
- API endpoint authorization

### Data Security
- Input validation and sanitization
- HTTPS enforcement in production
- Secure file upload handling

## Troubleshooting Common Issues

### Authentication Issues
- Check token expiration
- Verify API_BASE_URL configuration
- Ensure proper role assignments

### API Request Issues
- Verify network connectivity
- Check request headers and body format
- Validate authentication tokens

### UI/Styling Issues
- Check Tailwind CSS compilation
- Verify theme configuration
- Ensure responsive design breakpoints

## Development Best Practices

### Code Organization
- Feature-based folder structure
- Consistent naming conventions
- Separation of concerns

### TypeScript Usage
- Strict type checking enabled
- Interface definitions for all data structures
- Generic types for reusable components

### Error Handling
- Consistent error response format
- User-friendly error messages
- Proper error logging

### Testing Strategy
- Unit tests for utility functions
- Integration tests for API services
- Component testing for UI elements

This documentation provides a comprehensive overview of the CONGOMA Membership Portal codebase structure and API request layer. The application follows modern React/Next.js patterns with a clean separation of concerns between UI components, business logic, and API communication.
