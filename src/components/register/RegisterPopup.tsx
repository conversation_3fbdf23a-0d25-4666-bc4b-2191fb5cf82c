"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { signUp } from "@/services/auth.services";

interface RegisterPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onLoginClick: () => void;
}

export default function RegisterPopup({
  isOpen,
  onClose,
  onLoginClick,
}: RegisterPopupProps) {
  const router = useRouter();
  const [formData, setFormData] = useState({
    fullname: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [passwordError, setPasswordError] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const errors = [];

    if (password.length < minLength) {
      errors.push(`Password must be at least ${minLength} characters long`);
    }

    if (!hasUpperCase) {
      errors.push("Password must contain at least one uppercase letter");
    }

    if (!hasLowerCase) {
      errors.push("Password must contain at least one lowercase letter");
    }

    if (!hasNumbers) {
      errors.push("Password must contain at least one number");
    }

    if (!hasSpecialChar) {
      errors.push("Password must contain at least one special character");
    }

    return {
      isValid: errors.length === 0,
      message: errors.join(", "),
    };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccess("");
    setPasswordError("");

    if (formData.password !== formData.confirmPassword) {
      setError("Passwords don't match");
      setIsLoading(false);
      return;
    }

    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      setPasswordError(passwordValidation.message);
      setIsLoading(false);
      return;
    }

    try {
      const { fullname, email, password } = formData;
      const response = await signUp({ fullname, email, password });
      setSuccess(response.message);
      setTimeout(() => {
        onClose();
        router.push("/auth/verification-pending");
      }, 2000);
    } catch (err: any) {
      console.error("Registration error:", {
        status: err.response?.status,
        statusText: err.response?.statusText,
        data: err.response?.data,
        headers: err.response?.headers,
        request: err.request,
        config: err.config,
      });

      const errorData = err.response?.data;
      if (errorData?.errors?.length > 0) {
        setError(errorData.errors[0].msg || "Validation error occurred");
      } else {
        setError(
          errorData?.message || "Registration failed. Please try again.",
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <div className="w-full max-w-md rounded-lg bg-white p-4 shadow-xl sm:p-6">
        <div className="flex justify-between">
          <h2 className="text-xl font-bold text-gray-800 sm:text-2xl">
            Register with CONGOMA
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={isLoading}
          >
            &times;
          </button>
        </div>

        {error && (
          <div className="mt-3 rounded-md bg-red-50 p-2 text-sm text-red-600 sm:mt-4 sm:p-3">
            {error}
          </div>
        )}
        {passwordError && (
          <div className="mt-3 rounded-md bg-red-50 p-2 text-sm text-red-600 sm:mt-4 sm:p-3">
            {passwordError}
          </div>
        )}
        {success && (
          <div className="mt-3 rounded-md bg-green-50 p-2 text-sm text-green-600 sm:mt-4 sm:p-3">
            {success}
          </div>
        )}

        <form
          onSubmit={handleSubmit}
          className="mt-3 space-y-3 sm:mt-4 sm:space-y-4"
        >
          <div>
            <label
              htmlFor="register-fullname"
              className="block text-sm font-medium text-gray-700"
            >
              Full Name
            </label>
            <input
              type="text"
              id="register-fullname"
              name="fullname"
              value={formData.fullname}
              onChange={handleChange}
              className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-base"
              placeholder="John Doe"
              required
              disabled={isLoading}
            />
          </div>

          <div>
            <label
              htmlFor="register-email"
              className="block text-sm font-medium text-gray-700"
            >
              Email
            </label>
            <input
              type="email"
              id="register-email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-base"
              placeholder="<EMAIL>"
              required
              disabled={isLoading}
            />
          </div>

          <div>
            <label
              htmlFor="register-password"
              className="block text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <input
              type="password"
              id="register-password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-base"
              placeholder="••••••••"
              required
              disabled={isLoading}
            />
          </div>

          <div>
            <label
              htmlFor="register-confirmPassword"
              className="block text-sm font-medium text-gray-700"
            >
              Confirm Password
            </label>
            <input
              type="password"
              id="register-confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-base"
              placeholder="••••••••"
              required
              disabled={isLoading}
            />
          </div>

          <div className="flex items-start">
            <div className="flex h-5 items-center">
              <input
                id="register-terms"
                name="terms"
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                required
                disabled={isLoading}
              />
            </div>
            <label
              htmlFor="register-terms"
              className="ml-2 block text-xs text-gray-700 sm:text-sm"
            >
              I agree to the{" "}
              <a href="#" className="text-blue-600 hover:text-blue-500">
                Terms
              </a>{" "}
              and{" "}
              <a href="#" className="text-blue-600 hover:text-blue-500">
                Privacy Policy
              </a>
            </label>
          </div>

          <div>
            <button
              type="submit"
              className="flex w-full justify-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 sm:text-base"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg
                    className="mr-2 h-4 w-4 animate-spin"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Registering...
                </>
              ) : (
                "Register"
              )}
            </button>
          </div>
        </form>

        <div className="mt-3 text-center text-xs text-gray-600 sm:mt-4 sm:text-sm">
          Already have an account?{" "}
          <button
            onClick={onLoginClick}
            className="font-medium text-indigo-600 hover:text-indigo-500"
          >
            Login
          </button>
        </div>
      </div>
    </div>
  );
}
