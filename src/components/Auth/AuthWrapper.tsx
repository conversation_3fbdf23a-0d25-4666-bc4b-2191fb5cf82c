"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { isAuthenticated, getCurrentUser } from "@/utils/auth.utils";
import { UserRole } from "@/services/auth.services";

interface AuthWrapperProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requiredRoles?: UserRole[];
  redirectTo?: string;
}

export default function AuthWrapper({
  children,
  requiredRole,
  requiredRoles,
  redirectTo = "/auth/sign-in",
}: AuthWrapperProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      const authenticated = isAuthenticated();

      if (!authenticated) {
        router.push(redirectTo);
        return;
      }

      // If no role requirements, just check authentication
      if (!requiredRole && !requiredRoles) {
        setIsAuthorized(true);
        setIsLoading(false);
        return;
      }

      // Check role requirements
      const user = await getCurrentUser();

      if (!user) {
        router.push(redirectTo);
        return;
      }

      if (requiredRole && user.role.name !== requiredRole) {
        router.push(redirectTo);
        return;
      }

      if (
        requiredRoles &&
        !requiredRoles.includes(user.role.name as UserRole)
      ) {
        router.push(redirectTo);
        return;
      }

      setIsAuthorized(true);
      setIsLoading(false);
    };

    checkAuth();
  }, [router, requiredRole, requiredRoles, redirectTo]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}

// Convenience components for different role types
export function SuperAdminOnly({ children }: { children: React.ReactNode }) {
  return <AuthWrapper requiredRole="super_admin">{children}</AuthWrapper>;
}

export function NgoAdminOnly({ children }: { children: React.ReactNode }) {
  return <AuthWrapper requiredRole="ngo_admin">{children}</AuthWrapper>;
}

export function CongomaStaffOnly({ children }: { children: React.ReactNode }) {
  return (
    <AuthWrapper
      requiredRoles={[
        "staff_registry",
        "staff_admin",
        "finance_officer",
        "programmes_officer",
      ]}
    >
      {children}
    </AuthWrapper>
  );
}

export function FinanceOfficerOnly({
  children,
}: {
  children: React.ReactNode;
}) {
  return <AuthWrapper requiredRole="finance_officer">{children}</AuthWrapper>;
}

export function AuthenticatedOnly({ children }: { children: React.ReactNode }) {
  return <AuthWrapper>{children}</AuthWrapper>;
}
