"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { isAuthenticated } from "@/utils/auth.utils";
import { UserRole } from "@/services/auth.services";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  requiredRoles?: UserRole[];
}

export default function ProtectedRoute({
  children,
  requiredRole,
  requiredRoles,
}: ProtectedRouteProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    const checkAuth = () => {
      const authenticated = isAuthenticated();

      if (!authenticated) {
        router.push("/auth/sign-in");
        return;
      }

      // If no role requirements, just check authentication
      if (!requiredRole && !requiredRoles) {
        setIsAuthorized(true);
        setIsLoading(false);
        return;
      }

      // Check role requirements
      const user = JSON.parse(localStorage.getItem("user") || "{}");

      if (requiredRole && user.role !== requiredRole) {
        router.push("/auth/sign-in");
        return;
      }

      if (requiredRoles && !requiredRoles.includes(user.role)) {
        router.push("/auth/sign-in");
        return;
      }

      setIsAuthorized(true);
      setIsLoading(false);
    };

    checkAuth();
  }, [router, requiredRole, requiredRoles]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}
