"use client";

import { useEffect, useState } from "react";
import { PeriodPicker } from "@/components/period-picker";
import { cn } from "@/lib/utils";
import { getApplicationStatusData } from "@/services/charts.services";
import { ApplicationStatusChart } from "./chart";
import { getSuperAdminStatistics } from "@/services/statistics.services";
import { SuperAdminStatistics } from "@/types/super-admin-statistics.types";

type PropsType = {
  timeFrame?: string;
  className?: string;
};

export function ApplicationStatus({
  timeFrame = "monthly",
  className,
}: PropsType) {
  const [data, setData] = useState<any>(null);
  const [statsData, setStatsData] = useState<SuperAdminStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const statusData = await getApplicationStatusData(timeFrame);
        setData(statusData);

        const token = localStorage.getItem("accessToken")!;
        const statisticsData = await getSuperAdminStatistics(token);
        setStatsData(statisticsData.data);
      } catch (error) {
        console.error("Error fetching application status data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [timeFrame]);

  if (isLoading || !data) {
    return (
      <div
        className={cn(
          "grid grid-cols-1 grid-rows-[auto_1fr] gap-9 rounded-[10px] bg-white p-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            Application Status
          </h2>
        </div>
        <div className="grid place-items-center">
          <div className="h-64 w-64 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "grid grid-cols-1 grid-rows-[auto_1fr] gap-9 rounded-[10px] bg-white p-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="flex flex-wrap items-center justify-between gap-4">
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
          Application Status
        </h2>

        <PeriodPicker
          defaultValue={timeFrame}
          sectionKey="application_status"
        />
      </div>

      <div className="grid place-items-center">
        <ApplicationStatusChart data={data} />
      </div>
    </div>
  );
}
