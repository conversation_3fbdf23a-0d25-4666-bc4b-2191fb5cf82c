"use client";

import { useEffect, useState } from "react";
import { PeriodPicker } from "@/components/period-picker";
import { standardFormat } from "@/lib/format-number";
import { cn } from "@/lib/utils";
import { getNGOsPerformanceData } from "@/services/charts.services";
import { NGOsPerformanceChart } from "./chart";

type PropsType = {
  timeFrame?: string;
  className?: string;
};

export function NGOsPerformance({
  timeFrame = "monthly",
  className,
}: PropsType) {
  const [data, setData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const performanceData = await getNGOsPerformanceData(timeFrame);
        setData(performanceData);
      } catch (error) {
        console.error("Error fetching NGOs performance data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [timeFrame]);

  if (isLoading || !data) {
    return (
      <div
        className={cn(
          "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            NGOs Performance
          </h2>
        </div>
        <div className="h-64 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
        <dl className="grid divide-stroke text-center dark:divide-dark-3 sm:grid-cols-2 sm:divide-x [&>div]:flex [&>div]:flex-col-reverse [&>div]:gap-1">
          <div className="dark:border-dark-3 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
            <div className="h-6 w-20 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="mt-1 h-4 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-6 w-20 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="mt-1 h-4 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
          </div>
        </dl>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="flex flex-wrap items-center justify-between gap-4">
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
          NGOs Performance
        </h2>

        <PeriodPicker defaultValue={timeFrame} sectionKey="ngos_performance" />
      </div>

      <NGOsPerformanceChart data={data} />

      <dl className="grid divide-stroke text-center dark:divide-dark-3 sm:grid-cols-2 sm:divide-x [&>div]:flex [&>div]:flex-col-reverse [&>div]:gap-1">
        <div className="dark:border-dark-3 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(data.activities.reduce((acc: number, { y }: { y: number }) => acc + y, 0))}
          </dt>
          <dd className="font-medium dark:text-dark-6">Total Activities</dd>
        </div>

        <div>
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(data.impact.reduce((acc: number, { y }: { y: number }) => acc + y, 0))}
          </dt>
          <dd className="font-medium dark:text-dark-6">Impact Score</dd>
        </div>
      </dl>
    </div>
  );
}
