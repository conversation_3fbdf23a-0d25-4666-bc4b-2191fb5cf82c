"use client";

import { useEffect, useState } from "react";
import { PeriodPicker } from "@/components/period-picker";
import { cn } from "@/lib/utils";
import { getUserAlertsData } from "@/services/charts.services";
import { UserAlertsChart } from "./chart";

type PropsType = {
  timeFrame?: string;
  className?: string;
};

export function UserAlerts({ className, timeFrame }: PropsType) {
  const [data, setData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const alertsData = await getUserAlertsData(timeFrame);
        setData(alertsData);
      } catch (error) {
        console.error("Error fetching user alerts data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [timeFrame]);

  if (isLoading || !data) {
    return (
      <div
        className={cn(
          "rounded-[10px] bg-white px-7.5 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            User Alerts {timeFrame || "this week"}
          </h2>
        </div>
        <div className="h-64 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "rounded-[10px] bg-white px-7.5 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="flex flex-wrap items-center justify-between gap-4">
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
          User Alerts {timeFrame || "this week"}
        </h2>

        <PeriodPicker
          items={["this week", "last week"]}
          defaultValue={timeFrame || "this week"}
          sectionKey="user_alerts"
        />
      </div>

      <UserAlertsChart data={data} />
    </div>
  );
}
