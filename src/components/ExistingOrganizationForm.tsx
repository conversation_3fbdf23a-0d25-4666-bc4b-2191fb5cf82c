"use client";

import { useState, useEffect } from "react";
import { createOrganizationWithAdmin, INgo } from "@/services/ngo.services";
import { getAllSectors } from "@/services/sector.services";
import { LocationsOfOperation, NGOType, Sectors } from "@/types/ngo.types";
import {
  EmailIcon,
  UserIcon,
  CallIcon,
  FaxIcon,
  ArrowLeftIcon,
  UploadIcon,
  DocumentIcon,
  CertificateIcon,
  MinutesIcon,
  AffidavitIcon,
  PaymentIcon,
  ChevronDownIcon,
  CheckIcon,
} from "@/assets/icons";
import InputGroup from "./FormElements/InputGroup";
import { getAllDistricts } from "@/services/location.services";

interface OrganizationFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
  isExisting?: boolean;
  // Added to determine organization type
  type?: NGOType;
}

// NGO file uploads interface
interface NGOFiles {
  swornInAffidavit: File;
  constitution: File;
  minutesOfFirstMeeting: File;
  certificateFromRegistrarGeneral: File;
  registrationFee: File;
  processingFee: File;
  ngoLogo?: File;
}

// Form data interface
interface CreateNGOData {
  age: string;
  type: NGOType;
  name: string;
  initials: string;
  hq: string;
  dateFormed: string;
  chairpersonName: string;
  chairpersonPhone: string;
  chairpersonEmail: string;
  chairpersonAddress: string;
  chiefExecutiveName: string;
  chiefExecutivePhone: string;
  chiefExecutiveEmail: string;
  chiefExecutiveAddress: string;
  chiefExecutiveFax: string;
  dateApprovedByGOM: string;
  statementAgreement: boolean;
  physicalOfficesAddress: string;
  ngoBackground: string;
  missionStatement: string;
  visionStatement: string;
  valuesStatement: string;
  locationsOfOperation: string[];
  sectorsOfOperation: string[];
  goals: string;
  joinCommunity: boolean;
  sourceOfFunds: string;
  numberOfEmployees: number;
  selectedDistrictNames: string[];
  selectedSectorNames: string[];
  districts: string;
  sectors: string;
}

export function ExistingOrganizationForm({
  onSubmit,
  onCancel,
  isExisting = false,
  type = "local",
}: OrganizationFormProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDistrictDropdown, setShowDistrictDropdown] = useState(false);
  const [showSectorDropdown, setShowSectorDropdown] = useState(false);
  const [sectors, setSectors] = useState<Sectors[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [locationsOfOperation, setLocationsOfOperation] = useState<
    LocationsOfOperation[]
  >([]);

  useEffect(() => {
    const loadData = async () => {
      setLoadingData(true);
      try {
        const [districtsRes, sectorsRes] = await Promise.allSettled([
          getAllDistricts(),
          getAllSectors({ status: "active" }),
        ]);

        // Handle districts
        if (districtsRes.status === "fulfilled" && districtsRes.value.data) {
          setLocationsOfOperation(
            districtsRes.value.data.map((districtObj: any) => ({
              _id: districtObj._id,
              region: districtObj.region,
              district: districtObj.district,
            })),
          );
        } else {
          console.error(
            "Failed to load districts:",
            districtsRes.status === "rejected"
              ? districtsRes.reason
              : "Unknown error",
          );
          // TODO: Add fallback from MALAWI_LOCATIONS if available
        }

        // Handle sectors
        if (sectorsRes.status === "fulfilled" && sectorsRes.value.data) {
          setSectors(
            sectorsRes.value.data.map((sector: any) => ({
              _id: sector._id,
              name: sector.name,
              description: sector.description,
              organizationCount: sector.organizationCount,
              status: sector.status,
            })),
          );
        } else {
          console.error(
            "Failed to load sectors:",
            sectorsRes.status === "rejected"
              ? sectorsRes.reason
              : "Unknown error",
          );
          // TODO: Add fallback from MALAWI_SECTORS if available
        }
      } catch (err) {
        console.error("Unexpected error:", err);
        setError("Failed to load data");
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, []);

  const [formData, setFormData] = useState<CreateNGOData>({
    age: "",
    type: type as NGOType, // Use the type from props
    name: "",
    initials: "",
    hq: "",
    dateFormed: "",
    chairpersonName: "",
    chairpersonPhone: "",
    chairpersonEmail: "",
    chairpersonAddress: "",
    chiefExecutiveName: "",
    chiefExecutivePhone: "",
    chiefExecutiveEmail: "",
    chiefExecutiveAddress: "",
    chiefExecutiveFax: "",
    dateApprovedByGOM: "",
    statementAgreement: false,
    physicalOfficesAddress: "",
    ngoBackground: "",
    missionStatement: "",
    visionStatement: "",
    valuesStatement: "",
    locationsOfOperation: [],
    sectorsOfOperation: [],
    goals: "",
    joinCommunity: false,
    sourceOfFunds: "",
    numberOfEmployees: 0,
    districts: "",
    sectors: "",
    selectedDistrictNames: [],
    selectedSectorNames: [],
  });

  const [files, setFiles] = useState<Partial<NGOFiles>>({
    swornInAffidavit: undefined,
    constitution: undefined,
    minutesOfFirstMeeting: undefined,
    certificateFromRegistrarGeneral: undefined,
    registrationFee: undefined,
    processingFee: undefined,
    ngoLogo: undefined,
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      setFormData({
        ...formData,
        [name]: (e.target as HTMLInputElement).checked,
      });
    } else if (type === "number") {
      setFormData({
        ...formData,
        [name]: parseInt(value) || 0,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name } = e.target;
    const file = e.target.files?.[0];

    if (file) {
      setFiles({
        ...files,
        [name]: file,
      });
    }
  };

  const handleMultiSelectChange = (
    field: "locationsOfOperation" | "sectorsOfOperation",
    value: string,
  ) => {
    const currentValues = formData[field];
    const newValues = currentValues.includes(value)
      ? currentValues.filter((item) => item !== value)
      : [...currentValues, value];

    setFormData({
      ...formData,
      [field]: newValues,
    });

    if (field === "sectorsOfOperation") {
      console.log("Updated sectors of operation:", newValues);
    }
  };

  const handleDistrictChange = (districtId: string) => {
    // Find the district object by ID
    const districtObj = locationsOfOperation.find(
      (loc) => loc._id === districtId,
    );

    // Get current district IDs
    const currentDistrictIds = formData.districts
      ? formData.districts.split(",").filter((d: string) => d.trim())
      : [];

    // Check if district is already selected
    const isSelected = currentDistrictIds.includes(districtId);

    // Update district IDs
    const newDistrictIds = isSelected
      ? currentDistrictIds.filter((d: string) => d !== districtId)
      : [...currentDistrictIds, districtId];

    // Update district names for display
    const newDistrictNames = isSelected
      ? formData.selectedDistrictNames.filter(
          (name) => name !== districtObj?.district,
        )
      : districtObj?.district
        ? [...formData.selectedDistrictNames, districtObj.district]
        : formData.selectedDistrictNames;

    setFormData({
      ...formData,
      districts: newDistrictIds.join(","),
      selectedDistrictNames: newDistrictNames,
    });
  };

  // const handleSectorChange = (sectorId: string) => {
  //   // Find the sector object by ID
  //   const sectorObj = sectors.find((loc) => loc._id === sectorId);

  //   // Get current sector IDs
  //   const currentSectorIds = formData.sectorsOfOperation
  //     ? formData.sectors.split(",").filter((s: string) => s.trim())
  //     : [];

  //   // Check if sector is already selected
  //   const isSelected = currentSectorIds.includes(sectorId);

  //   // Update sector IDs
  //   const newSectorIds = isSelected
  //     ? currentSectorIds.filter((s: string) => s !== sectorId)
  //     : [...currentSectorIds, sectorId];

  //   // Update sector names for display
  //   const newSectorNames = isSelected
  //     ? formData.selectedSectorNames.filter((name) => name !== sectorObj?.name)
  //     : sectorObj?.name
  //       ? [...formData.selectedSectorNames, sectorObj.name]
  //       : formData.selectedSectorNames;

  //   setFormData({
  //     ...formData,
  //     sectorsOfOperation: newSectorIds.join(","),
  //     selectedSectorNames: newSectorNames,
  //   });
  // };
  const handleSectorChange = (sectorId: string) => {
    const sectorObj = sectors.find((s) => s._id === sectorId);

    const isSelected = formData.sectorsOfOperation.includes(sectorId);

    const newSectorIds = isSelected
      ? formData.sectorsOfOperation.filter((s) => s !== sectorId)
      : [...formData.sectorsOfOperation, sectorId];

    const newSectorNames = isSelected
      ? formData.selectedSectorNames.filter((n) => n !== sectorObj?.name)
      : sectorObj
        ? [...formData.selectedSectorNames, sectorObj.name]
        : formData.selectedSectorNames;

    setFormData({
      ...formData,
      sectorsOfOperation: newSectorIds,
      selectedSectorNames: newSectorNames,
    });
  };
  const handleNext = () => {
    if (currentStep < 6) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate required files
      const requiredFiles: (keyof NGOFiles)[] = [
        "swornInAffidavit",
        "constitution",
        "minutesOfFirstMeeting",
        "certificateFromRegistrarGeneral",
        "registrationFee",
        "processingFee",
      ];

      for (const fileKey of requiredFiles) {
        if (!files[fileKey]) {
          throw new Error(
            `${fileKey.replace(/([A-Z])/g, " $1").toLowerCase()} is required`,
          );
        }
      }

      // Create FormData object for API
      const apiFormData = new FormData();

      if (isExisting == false) {
        apiFormData.append("age", "new");
      } else {
        apiFormData.append("age", "old");
      }

      if (type === "local") {
        apiFormData.append("type", "local");
      } else if (type === "international") {
        apiFormData.append("type", "international");
      }

      // Basic NGO information - map to backend expected field names
      apiFormData.append("ngoName", formData.name);
      apiFormData.append("ngoInitials", formData.initials);
      apiFormData.append("hqPostalAddress", formData.hq);
      apiFormData.append("dateFormed", formData.dateFormed);

      // Chairperson information - make sure to include the address
      apiFormData.append("chairpersonName", formData.chairpersonName);
      apiFormData.append("chairpersonEmail", formData.chairpersonEmail);
      apiFormData.append("chairpersonPhone", formData.chairpersonPhone);
      apiFormData.append("chairpersonAddress", formData.chairpersonAddress);

      // CEO information - map to backend expected field names
      apiFormData.append("ceoName", formData.chiefExecutiveName);
      apiFormData.append("ceoEmail", formData.chiefExecutiveEmail);
      apiFormData.append("ceoPhone", formData.chiefExecutivePhone);
      apiFormData.append("ceoAddress", formData.chiefExecutiveAddress);

      // Map remaining fields to backend expected names
      apiFormData.append("hqPhysicalAddress", formData.physicalOfficesAddress);
      apiFormData.append("dateApprovedByGOM", formData.dateApprovedByGOM);
      apiFormData.append("missionStatement", formData.missionStatement);
      apiFormData.append("vision", formData.visionStatement);
      apiFormData.append("values", formData.valuesStatement);
      apiFormData.append("activities", formData.ngoBackground);

      // Handle districts - send as comma-separated string
      if (formData.districts) {
        apiFormData.append("districts", formData.districts);
      }

      // Handle funding sources and employee count for international NGOs
      if (formData.sourceOfFunds) {
        apiFormData.append("fundingSources", formData.sourceOfFunds);
      }
      if (formData.numberOfEmployees) {
        apiFormData.append(
          "employeeCount",
          formData.numberOfEmployees.toString(),
        );
      }

      // Add files to FormData
      for (const [key, file] of Object.entries(files)) {
        if (file) {
          apiFormData.append(key, file);
        }
      }

      // Log the FormData for debugging
      console.log("Form data entries:");
      for (const pair of apiFormData.entries()) {
        console.log(pair[0], pair[1]);
      }

      // Get token from localStorage (or your auth method)
      const token = localStorage.getItem("accessToken") || "";

      // Create organization with admin using the correct API endpoint
      const response = await createOrganizationWithAdmin(apiFormData, token);

      // Update localStorage with new tokens and user data
      localStorage.setItem("accessToken", response.accessToken);
      localStorage.setItem("refreshToken", response.refreshToken);
      localStorage.setItem("user", JSON.stringify(response.user));

      // Pass the response to the parent component
      onSubmit(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create NGO");
      console.error("Error creating NGO:", err);
    } finally {
      setLoading(false);
    }
  };

  // Validation helpers
  const isStep1Valid =
    formData.name && formData.initials && formData.hq && formData.dateFormed;
  const isStep2Valid =
    formData.chairpersonName &&
    formData.chairpersonPhone &&
    formData.chairpersonEmail &&
    formData.chairpersonAddress;
  const isStep3Valid =
    formData.chiefExecutiveName &&
    formData.chiefExecutivePhone &&
    formData.chiefExecutiveEmail &&
    formData.chiefExecutiveAddress;
  const isStep4Valid =
    formData.ngoBackground &&
    formData.missionStatement &&
    formData.visionStatement &&
    formData.valuesStatement;

  const isStep5Valid =
    formData.sectorsOfOperation &&
    formData.sectorsOfOperation.length > 0 &&
    formData.physicalOfficesAddress &&
    // Only check these fields for international NGOs
    (type === "international"
      ? formData.sourceOfFunds && formData.numberOfEmployees > 0
      : true) &&
    formData.goals &&
    formData.statementAgreement &&
    formData.joinCommunity;

  const isStep6Valid =
    files.swornInAffidavit &&
    files.constitution &&
    files.minutesOfFirstMeeting &&
    files.certificateFromRegistrarGeneral &&
    files.registrationFee &&
    files.processingFee;

  return (
    <div className="mx-auto w-full max-w-2xl rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
      {/* Step Indicator */}
      <div className="mb-6 flex items-center justify-center space-x-2">
        {[1, 2, 3, 4, 5, 6].map((step) => (
          <div key={step} className="flex items-center">
            <div
              className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
                currentStep >= step
                  ? "bg-primary text-white"
                  : "bg-gray-200 text-gray-500"
              }`}
            >
              {step}
            </div>
            {step < 6 && (
              <div
                className={`mx-2 h-1 w-8 rounded ${
                  currentStep > step ? "bg-primary" : "bg-gray-200"
                }`}
              ></div>
            )}
          </div>
        ))}
      </div>

      {error && (
        <div className="mb-6 rounded-lg bg-red-100 p-4 text-red-800">
          <p>{error}</p>
        </div>
      )}

      {loadingData && (
        <div className="flex items-center justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
          <span className="ml-2 text-gray-600 dark:text-gray-300">
            Loading data...
          </span>
        </div>
      )}

      {/* Form Content */}
      <div className="max-h-[calc(100vh-200px)] overflow-y-auto">
        <form
          onSubmit={handleSubmit}
          className="space-y-6"
          style={{ display: loadingData ? "none" : "block" }}
        >
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                Basic Information
              </h3>

              <InputGroup
                type="text"
                label="NGO Name"
                placeholder="Enter NGO name"
                name="name"
                value={formData.name}
                handleChange={handleInputChange}
                icon={<UserIcon />}
                required
              />

              <div className="grid grid-cols-2 gap-4">
                <InputGroup
                  type="text"
                  label="NGO Initials (Optional)"
                  placeholder="Enter NGO initials"
                  name="initials"
                  value={formData.initials}
                  handleChange={handleInputChange}
                  icon={<UserIcon />}
                />

                <div>
                  <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                    NGO Logo
                  </label>
                  <input
                    type="file"
                    name="ngoLogo"
                    onChange={handleFileChange}
                    accept="image/*"
                    className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition file:mr-4 file:rounded-full file:border-0 file:bg-primary file:px-4 file:py-2 file:text-sm file:font-semibold file:text-white hover:file:bg-primary/80 focus:border-primary active:border-primary"
                  />
                </div>
              </div>

              <InputGroup
                type="text"
                label="NGO HQ Postal Address"
                placeholder="Enter headquarters postal address"
                name="hq"
                value={formData.hq}
                handleChange={handleInputChange}
                icon={<UserIcon />}
                required
              />

              <div className="grid grid-cols-2 gap-4">
                <InputGroup
                  type="date"
                  label="Date Formed"
                  placeholder="Select date formed"
                  name="dateFormed"
                  value={formData.dateFormed}
                  handleChange={handleInputChange}
                  required
                />

                <InputGroup
                  type="date"
                  label="Date Approved by GOM"
                  placeholder="Select approval date"
                  name="dateApprovedByGOM"
                  value={formData.dateApprovedByGOM}
                  handleChange={handleInputChange}
                  required
                />
              </div>
            </div>
          )}

          {/* Step 2: Chairperson Information */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                Chairperson Information
              </h3>

              <InputGroup
                type="text"
                label="Chairperson Name"
                placeholder="Enter chairperson name"
                name="chairpersonName"
                value={formData.chairpersonName}
                handleChange={handleInputChange}
                icon={<UserIcon />}
                required
              />

              <InputGroup
                type="tel"
                label="Chairperson Phone"
                placeholder="Enter chairperson phone"
                name="chairpersonPhone"
                value={formData.chairpersonPhone}
                handleChange={handleInputChange}
                icon={<CallIcon />}
                required
              />

              <InputGroup
                type="email"
                label="Chairperson Email"
                placeholder="Enter chairperson email"
                name="chairpersonEmail"
                value={formData.chairpersonEmail}
                handleChange={handleInputChange}
                icon={<EmailIcon />}
                required
              />

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Chairperson Address
                </label>
                <textarea
                  name="chairpersonAddress"
                  value={formData.chairpersonAddress}
                  onChange={handleInputChange}
                  placeholder="Enter chairperson address"
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary"
                  rows={2}
                  required
                />
              </div>
            </div>
          )}

          {/* Step 3: CEO Information */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                Chief Executive Officer Information
              </h3>

              <InputGroup
                type="text"
                label="CEO Name"
                placeholder="Enter CEO name"
                name="chiefExecutiveName"
                value={formData.chiefExecutiveName}
                handleChange={handleInputChange}
                icon={<UserIcon />}
                required
              />

              <div className="grid grid-cols-2 gap-4">
                <InputGroup
                  type="tel"
                  label="CEO Phone"
                  placeholder="Enter CEO phone"
                  name="chiefExecutivePhone"
                  value={formData.chiefExecutivePhone}
                  handleChange={handleInputChange}
                  icon={<CallIcon />}
                  required
                />
                <InputGroup
                  type="text"
                  label="CEO Fax (Optional)"
                  placeholder="Enter CEO fax"
                  name="chiefExecutiveFax"
                  value={formData.chiefExecutiveFax}
                  handleChange={handleInputChange}
                  icon={<FaxIcon />}
                />
              </div>

              <InputGroup
                type="email"
                label="CEO Email"
                placeholder="Enter CEO email"
                name="chiefExecutiveEmail"
                value={formData.chiefExecutiveEmail}
                handleChange={handleInputChange}
                icon={<EmailIcon />}
                required
              />

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  CEO Address
                </label>
                <textarea
                  name="chiefExecutiveAddress"
                  value={formData.chiefExecutiveAddress}
                  onChange={handleInputChange}
                  placeholder="Enter CEO address"
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary"
                  rows={2}
                  required
                />
              </div>
            </div>
          )}

          {/* Step 4: NGO Background */}
          {currentStep === 4 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                NGO Background
              </h3>

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  NGO Background
                </label>
                <textarea
                  name="ngoBackground"
                  value={formData.ngoBackground}
                  onChange={handleInputChange}
                  placeholder="Describe the background of your organization"
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary"
                  rows={4}
                  required
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Mission Statement
                </label>
                <textarea
                  name="missionStatement"
                  value={formData.missionStatement}
                  onChange={handleInputChange}
                  placeholder="Enter your organization's mission statement"
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary"
                  rows={3}
                  required
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Vision Statement
                </label>
                <textarea
                  name="visionStatement"
                  value={formData.visionStatement}
                  onChange={handleInputChange}
                  placeholder="Enter your organization's vision"
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary"
                  rows={3}
                  required
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Values Statement
                </label>
                <textarea
                  name="valuesStatement"
                  value={formData.valuesStatement}
                  onChange={handleInputChange}
                  placeholder="Enter your organization's core values"
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary"
                  rows={3}
                  required
                />
              </div>
            </div>
          )}

          {/* Step 5: Additional Information */}
          {currentStep === 5 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                Additional Information
              </h3>

              {/* District(s) of Operation - Multi-select Dropdown */}
              <div className="relative">
                <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  District(s) of Operation *
                </label>
                <div className="relative">
                  <button
                    type="button"
                    className="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-left focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary dark:border-gray-600 dark:bg-gray-700"
                    onClick={() =>
                      setShowDistrictDropdown(!showDistrictDropdown)
                    }
                  >
                    {formData.selectedDistrictNames.length > 0
                      ? formData.selectedDistrictNames.join(", ")
                      : locationsOfOperation.length > 0
                        ? "Select districts..."
                        : "No districts available"}
                    <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                      <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                    </span>
                  </button>

                  {showDistrictDropdown && (
                    <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700">
                      {locationsOfOperation.length > 0 ? (
                        locationsOfOperation.map((location) => (
                          <div
                            key={location._id}
                            className="flex cursor-pointer items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600"
                            onClick={() => handleDistrictChange(location._id)}
                          >
                            <input
                              type="checkbox"
                              checked={formData.districts
                                .split(",")
                                .includes(location._id)}
                              readOnly
                              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            />
                            <span className="ml-3 block truncate">
                              {location.district}
                            </span>
                            {formData.locationsOfOperation.includes(
                              location._id,
                            ) && (
                              <span className="ml-auto text-primary">
                                <CheckIcon className="h-5 w-5" />
                              </span>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                          No districts available
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Sectors of Operation - Multi-select Checkboxes */}
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Sectors of Operation *
                </label>{" "}
                <div className="relative">
                  <button
                    type="button"
                    className="w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-left focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary dark:border-gray-600 dark:bg-gray-700"
                    onClick={() => setShowSectorDropdown(!showSectorDropdown)}
                  >
                    {formData.sectorsOfOperation.length > 0
                      ? formData.selectedSectorNames.join(", ")
                      : locationsOfOperation.length > 0
                        ? "Select sectors..."
                        : "No sectors available"}
                    <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                      <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                    </span>
                  </button>

                  {showSectorDropdown && (
                    <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700">
                      {sectors.length > 0 ? (
                        sectors.map((sector) => (
                          <div
                            key={sector._id}
                            className="flex cursor-pointer items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600"
                            onClick={() => handleSectorChange(sector._id)}
                          >
                            <input
                              id={`sector-${sector._id}`}
                              checked={formData.sectorsOfOperation.includes(
                                sector._id,
                              )}
                              onChange={() =>
                                handleMultiSelectChange(
                                  "sectorsOfOperation",
                                  sector._id,
                                )
                              }
                              readOnly
                              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            />
                            <span className="ml-3 block truncate">
                              {sector.name}
                            </span>
                            {formData.sectorsOfOperation.includes(
                              sector._id,
                            ) && (
                              <span className="ml-auto text-primary">
                                <CheckIcon className="h-5 w-5" />
                              </span>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                          No sectors available
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  NGO HQ Physical Address
                </label>
                <textarea
                  name="physicalOfficesAddress"
                  value={formData.physicalOfficesAddress}
                  onChange={handleInputChange}
                  placeholder="Enter physical address of headquarters"
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary"
                  rows={2}
                  required
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Organization Goals *
                </label>
                <textarea
                  name="goals"
                  value={formData.goals}
                  onChange={handleInputChange}
                  placeholder="Enter your organization's goals and objectives"
                  className="w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary"
                  rows={3}
                  required
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Describe what your organization aims to achieve in the short
                  and long term.
                </p>
              </div>

              {/* Only show these fields for international NGOs */}
              {type === "international" && (
                <>
                  <InputGroup
                    type="text"
                    label="Source of Funding"
                    placeholder="Enter funding sources"
                    name="sourceOfFunds"
                    value={formData.sourceOfFunds}
                    handleChange={handleInputChange}
                    required
                  />
                  <InputGroup
                    type="number"
                    label="Number of Employees/Volunteers"
                    placeholder="Enter approximate number"
                    name="numberOfEmployees"
                    value={formData.numberOfEmployees.toString()}
                    handleChange={handleInputChange}
                    required
                  />
                </>
              )}

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name="statementAgreement"
                  checked={formData.statementAgreement}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-primary focus:ring-primary"
                  required
                />
                <label className="text-sm font-medium text-black dark:text-white">
                  I certify that the information provided is accurate and agree
                  to the terms
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name="joinCommunity"
                  checked={formData.joinCommunity}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Join CONGOMA community
                </span>
              </div>
            </div>
          )}

          {/* Step 6: Document Uploads */}
          {currentStep === 6 && (
            <div className="space-y-6">
              <div className="mb-6 text-center">
                <h3 className="text-2xl font-bold text-gray-800 dark:text-white">
                  Upload Required Documents
                </h3>
                <p className="mt-2 text-gray-500 dark:text-gray-300">
                  Please upload all necessary documents for your NGO
                  registration
                </p>
              </div>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {/* Document Card 1 */}
                <div className="rounded-xl border border-gray-200 bg-gray-50 p-5 transition-all hover:border-primary dark:border-gray-600 dark:bg-gray-700">
                  <div className="mb-4 flex items-center">
                    <div className="mr-4 rounded-lg bg-primary/10 p-3">
                      <DocumentIcon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-800 dark:text-white">
                        NGO Constitution
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-300">
                        PDF, DOC, JPG (Max 5MB)
                      </p>
                    </div>
                  </div>
                  <input
                    type="file"
                    name="constitution"
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-lg file:border-0 file:bg-primary/10 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-primary hover:file:bg-primary/20"
                    required
                  />
                </div>

                {/* Document Card 2 */}
                <div className="rounded-xl border border-gray-200 bg-gray-50 p-5 transition-all hover:border-primary dark:border-gray-600 dark:bg-gray-700">
                  <div className="mb-4 flex items-center">
                    <div className="mr-4 rounded-lg bg-blue-100 p-3 dark:bg-blue-900/30">
                      <CertificateIcon className="h-6 w-6 text-blue-500 dark:text-blue-300" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-800 dark:text-white">
                        Certificate of Registrar General
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-300">
                        PDF, DOC, JPG (Max 5MB)
                      </p>
                    </div>
                  </div>
                  <input
                    type="file"
                    name="certificateFromRegistrarGeneral"
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-lg file:border-0 file:bg-blue-100 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-blue-600 hover:file:bg-blue-200 dark:file:bg-blue-900/30 dark:file:text-blue-300 dark:hover:file:bg-blue-900/50"
                    required
                  />
                </div>

                {/* Document Card 3 */}
                <div className="rounded-xl border border-gray-200 bg-gray-50 p-5 transition-all hover:border-primary dark:border-gray-600 dark:bg-gray-700">
                  <div className="mb-4 flex items-center">
                    <div className="mr-4 rounded-lg bg-green-100 p-3 dark:bg-green-900/30">
                      <MinutesIcon className="h-6 w-6 text-green-500 dark:text-green-300" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-800 dark:text-white">
                        Board Minutes
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-300">
                        PDF, DOC, JPG (Max 5MB)
                      </p>
                    </div>
                  </div>
                  <input
                    type="file"
                    name="minutesOfFirstMeeting"
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-lg file:border-0 file:bg-green-100 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-green-600 hover:file:bg-green-200 dark:file:bg-green-900/30 dark:file:text-green-300 dark:hover:file:bg-green-900/50"
                    required
                  />
                </div>

                {/* Document Card 4 */}
                <div className="rounded-xl border border-gray-200 bg-gray-50 p-5 transition-all hover:border-primary dark:border-gray-600 dark:bg-gray-700">
                  <div className="mb-4 flex items-center">
                    <div className="mr-4 rounded-lg bg-blue-100 p-3 dark:bg-blue-900/30">
                      <AffidavitIcon className="h-6 w-6 text-blue-500 dark:text-blue-300" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-800 dark:text-white">
                        Sworn-in Affidavits
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-300">
                        PDF, DOC, JPG (Max 5MB)
                      </p>
                    </div>
                  </div>
                  <input
                    type="file"
                    name="swornInAffidavit"
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-lg file:border-0 file:bg-blue-100 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-blue-600 hover:file:bg-blue-200 dark:file:bg-blue-900/30 dark:file:text-blue-300 dark:hover:file:bg-blue-900/50"
                    required
                  />
                </div>

                {/* Payment Proof - Full Width */}
                <div className="rounded-xl border border-gray-200 bg-gray-50 p-5 transition-all hover:border-primary dark:border-gray-600 dark:bg-gray-700 md:col-span-2">
                  <div className="mb-4 flex items-center">
                    <div className="mr-4 rounded-lg bg-yellow-100 p-3 dark:bg-yellow-900/30">
                      <PaymentIcon className="h-6 w-6 text-yellow-500 dark:text-yellow-300" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-800 dark:text-white">
                        Processing Fee Proof of Payment
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-300">
                        PDF, JPG, PNG (Max 5MB)
                      </p>
                    </div>
                  </div>
                  <input
                    type="file"
                    name="processingFee"
                    onChange={handleFileChange}
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-lg file:border-0 file:bg-yellow-100 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-yellow-600 hover:file:bg-yellow-200 dark:file:bg-yellow-900/30 dark:file:text-yellow-300 dark:hover:file:bg-yellow-900/50"
                    required
                  />
                </div>

                {/* Registration Fee - Full Width */}
                <div className="rounded-xl border border-gray-200 bg-gray-50 p-5 transition-all hover:border-primary dark:border-gray-600 dark:bg-gray-700 md:col-span-2">
                  <div className="mb-4 flex items-center">
                    <div className="mr-4 rounded-lg bg-purple-100 p-3 dark:bg-purple-900/30">
                      <PaymentIcon className="h-6 w-6 text-purple-500 dark:text-purple-300" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-800 dark:text-white">
                        Registration Fee Proof of Payment
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-300">
                        PDF, JPG, PNG (Max 5MB)
                      </p>
                    </div>
                  </div>
                  <input
                    type="file"
                    name="registrationFee"
                    onChange={handleFileChange}
                    accept=".pdf,.jpg,.jpeg,.png"
                    className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-lg file:border-0 file:bg-purple-100 file:px-4 file:py-2 file:text-sm file:font-semibold file:text-purple-600 hover:file:bg-purple-200 dark:file:bg-purple-900/30 dark:file:text-purple-300 dark:hover:file:bg-purple-900/50"
                    required
                  />
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex gap-3 border-t border-gray-200 pt-6 dark:border-gray-700">
            {currentStep > 1 ? (
              <button
                type="button"
                onClick={handlePrevious}
                className="flex items-center gap-2 rounded-lg border border-stroke px-4 py-3 font-medium text-black transition hover:bg-gray-50 dark:text-white dark:hover:bg-gray-700"
              >
                <ArrowLeftIcon />
                Previous
              </button>
            ) : (
              <button
                type="button"
                onClick={onCancel}
                className="flex-1 rounded-lg border border-stroke px-4 py-3 font-medium text-black transition hover:bg-gray-50 dark:text-white dark:hover:bg-gray-700"
              >
                Cancel
              </button>
            )}

            {currentStep < 6 ? (
              <button
                type="button"
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && !isStep1Valid) ||
                  (currentStep === 2 && !isStep2Valid) ||
                  (currentStep === 3 && !isStep3Valid) ||
                  (currentStep === 4 && !isStep4Valid) ||
                  (currentStep === 5 && !isStep5Valid)
                }
                className="flex-1 rounded-lg bg-primary px-4 py-3 font-medium text-white transition hover:bg-opacity-90 disabled:opacity-50"
              >
                Next Step
              </button>
            ) : (
              <button
                type="submit"
                onClick={handleSubmit}
                disabled={loading || !isStep6Valid}
                className="flex-1 rounded-lg bg-primary px-4 py-3 font-medium text-white transition hover:bg-opacity-90 disabled:opacity-50"
              >
                {loading ? "Submitting..." : "Submit Application"}
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}
