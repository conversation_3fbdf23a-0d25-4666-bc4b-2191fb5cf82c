import * as Icons from "../icons";

export const NAV_DATA = [
  {
    label: "DASHBOARD",
    items: [
      {
        title: "Overview",
        url: "/overview",
        icon: Icons.DashboardIcon,
      }
    ]
  },
  {
    label: "CONTENT MANAGEMENT",
    items: [
      {
        title: "Networks",
        icon: Icons.NetworkIcon,
        items: [
          { title: "Sector Network", url: "/networks/sector" },
          { title: "District Network", url: "/networks/district" }
        ]
      },
      {
        title: "Organizations",
        icon: Icons.OrganizationIcon,
        items: [
          { title: "Local Organization", url: "/organizations/local" },
          { title: "International Organization", url: "/organizations/international" }
        ]
      },
      {
        title: "Area of Focus",
        url: "/focus-areas",
        icon: Icons.FocusIcon,
      }
    ]
  },
  {
    label: "PROJECTS",
    items: [
      {
        title: "All Projects",
        url: "/projects",
        icon: Icons.ProjectIcon,
      }
    ]
  },
  {
    label: "USER MANAGEMENT",
    items: [
      {
        title: "User Directory",
        url: "/users",
        icon: Icons.UsersIcon,
      },
      {
        title: "User Alerts",
        url: "/alerts",
        icon: Icons.AlertIcon,
      },
      {
        title: "Profile",
        url: "/profile",
        icon: Icons.UserIcon,
      }
    ]
  },
  {
    label: "DOCUMENTS",
    items: [
      {
        title: "Certificates",
        url: "/documents/certificates",
        icon: Icons.CertificateIcon,
      },
      {
        title: "NGO Documents",
        url: "/documents/ngo",
        icon: Icons.FolderIcon,
      }
    ]
  },
  {
    label: "FINANCIAL MANAGEMENT",
    items: [
      {
        title: "Invoices",
        url: "/financial/invoices",
        icon: Icons.InvoiceIcon,
      },
      {
        title: "Payments",
        url: "/financial/payments",
        icon: Icons.PaymentIcon,
      },
      {
        title: "Receipts",
        url: "/financial/receipts",
        icon: Icons.ReceiptIcon,
      },
      {
        title: "Financial Reports",
        url: "/financial/reports",
        icon: Icons.ReportIcon,
      },
      {
        title: "Refunds & Adjustments",
        url: "/financial/refunds",
        icon: Icons.RefundIcon,
      }
    ]
  },
  {
    label: "REPORTS & ANALYTICS",
    items: [
      {
        title: "Reports",
        url: "/reports",
        icon: Icons.ReportIcon,
      },
      {
        title: "Suggestions",
        url: "/suggestions",
        icon: Icons.SuggestionIcon,
      }
    ]
  },
  {
    label: "SYSTEM",
    items: [
      {
        title: "Settings",
        url: "/settings",
        icon: Icons.SettingsIcon,
      },
      {
        title: "Activity Logs",
        url: "/activity-logs",
        icon: Icons.SettingsIcon,
      },
      {
        title: "Help Center",
        url: "/help",
        icon: Icons.HelpIcon,
      },
      {
        title: "Logout",
        url: "/logout",
        icon: Icons.LogoutIcon,
      }
    ]
  }
];