"use client";

import { useState } from "react";
import { EmailIcon, UserIcon, CallIcon, ArrowLeftIcon } from "@/assets/icons";
import InputGroup from "./FormElements/InputGroup";

interface MembershipFormProps {
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

export function MembershipForm({ onSubmit, onCancel }: MembershipFormProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    organizationName: "",
    contactPerson: "",
    email: "",
    phone: "",
    address: "",
    sector: "",
    website: "",
    description: "",
  });

  const [loading, setLoading] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleNext = () => {
    if (currentStep === 1) {
      // Validate step 1 fields
      if (
        formData.organizationName &&
        formData.contactPerson &&
        formData.email &&
        formData.phone
      ) {
        setCurrentStep(2);
      }
    }
  };

  const handlePrevious = () => {
    setCurrentStep(1);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    onSubmit(formData);
    setLoading(false);
  };

  const isStep1Valid =
    formData.organizationName &&
    formData.contactPerson &&
    formData.email &&
    formData.phone;
  const isStep2Valid =
    formData.address && formData.sector && formData.description;

  return (
    <div>
      {/* Step Indicator */}
      <div className="mb-6 flex items-center justify-center space-x-4">
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
            currentStep >= 1
              ? "bg-primary text-white"
              : "bg-gray-200 text-gray-500"
          }`}
        >
          1
        </div>
        <div
          className={`h-1 w-12 rounded ${
            currentStep >= 2 ? "bg-primary" : "bg-gray-200"
          }`}
        ></div>
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
            currentStep >= 2
              ? "bg-primary text-white"
              : "bg-gray-200 text-gray-500"
          }`}
        >
          2
        </div>
      </div>

      {/* Step 1: Basic Information */}
      {currentStep === 1 && (
        <div className="space-y-4">
          <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
            Basic Information
          </h3>

          <InputGroup
            type="text"
            label="Organization Name"
            placeholder="Enter your organization name"
            name="organizationName"
            value={formData.organizationName}
            handleChange={handleChange}
            icon={<UserIcon />}
            required
          />

          <InputGroup
            type="text"
            label="Contact Person"
            placeholder="Enter contact person name"
            name="contactPerson"
            value={formData.contactPerson}
            handleChange={handleChange}
            icon={<UserIcon />}
            required
          />

          <InputGroup
            type="email"
            label="Email Address"
            placeholder="Enter your email address"
            name="email"
            value={formData.email}
            handleChange={handleChange}
            icon={<EmailIcon />}
            required
          />

          <InputGroup
            type="tel"
            label="Phone Number"
            placeholder="Enter your phone number"
            name="phone"
            value={formData.phone}
            handleChange={handleChange}
            icon={<CallIcon />}
            required
          />

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="dark:border-strokedark flex-1 rounded-lg border border-stroke px-4 py-3 font-medium text-black transition hover:bg-gray-50 dark:text-white dark:hover:bg-gray-dark"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleNext}
              disabled={!isStep1Valid}
              className="flex-1 rounded-lg bg-primary px-4 py-3 font-medium text-white transition hover:bg-opacity-90 disabled:opacity-50"
            >
              Next Step
            </button>
          </div>
        </div>
      )}

      {/* Step 2: Additional Details */}
      {currentStep === 2 && (
        <form onSubmit={handleSubmit} className="space-y-4">
          <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
            Additional Details
          </h3>

          <div>
            <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
              Address
            </label>
            <textarea
              name="address"
              value={formData.address}
              onChange={handleChange}
              placeholder="Enter your organization address"
              className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
              rows={3}
              required
            />
          </div>

          <div>
            <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
              Sector of Operation
            </label>
            <select
              name="sector"
              value={formData.sector}
              onChange={handleChange}
              className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
              required
            >
              <option value="">Select a sector</option>
              <option value="health">Health</option>
              <option value="education">Education</option>
              <option value="agriculture">Agriculture</option>
              <option value="environment">Environment</option>
              <option value="human-rights">Human Rights</option>
              <option value="women-empowerment">Women Empowerment</option>
              <option value="youth-development">Youth Development</option>
              <option value="disability">Disability</option>
              <option value="water-sanitation">Water & Sanitation</option>
              <option value="economic-development">Economic Development</option>
              <option value="other">Other</option>
            </select>
          </div>

          <InputGroup
            type="url"
            label="Website (Optional)"
            placeholder="Enter your website URL"
            name="website"
            value={formData.website}
            handleChange={handleChange}
          />

          <div>
            <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
              Organization Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Briefly describe your organization's mission and activities"
              className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
              rows={4}
              required
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={handlePrevious}
              className="dark:border-strokedark flex items-center gap-2 rounded-lg border border-stroke px-4 py-3 font-medium text-black transition hover:bg-gray-50 dark:text-white dark:hover:bg-gray-dark"
            >
              <ArrowLeftIcon />
              Previous
            </button>
            <button
              type="submit"
              disabled={loading || !isStep2Valid}
              className="flex-1 rounded-lg bg-primary px-4 py-3 font-medium text-white transition hover:bg-opacity-90 disabled:opacity-50"
            >
              {loading ? "Submitting..." : "Submit Application"}
            </button>
          </div>
        </form>
      )}
    </div>
  );
}
