"use client";

import React, { useState } from "react";
import { changeAppovementStage, INgo } from "@/services/ngo.services";

type Payment = {
  _id: string;
  ngoName: string;
  contactPerson: string;
  contactEmail: string;
  processingFeeUrl: string;
  registrationFeeUrl: string;
  ngoType: string;
  status: string;
  createdAt: string;
};
type PaymentOrNgo = Payment | INgo;

type PaymentModalDemoProps = {
  data: PaymentOrNgo | null;
  onClose: () => void;
};

export default function ApprovalModal({
  data,
  onClose,
}: PaymentModalDemoProps) {
  const [rejectionReason, setRejectionReason] = useState("");
  const [showDeclineError, setShowDeclineError] = useState(false);

  if (!data) return null;

  const handleApprove = () => {
    console.log("✅ Approved:", data._id);
    const token = localStorage.getItem("accessToken")!;
    changeAppovementStage(data._id, token);
    onClose();
  };

  const handleDecline = () => {
    if (!rejectionReason.trim()) {
      setShowDeclineError(true);
      return;
    }
    console.log("❌ Declined:", data._id, "Reason:", rejectionReason);
    const token = localStorage.getItem("accessToken")!;
    changeAppovementStage(data._id, token, rejectionReason);
    setRejectionReason("");
    setShowDeclineError(false);
    onClose();
  };

  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const role = user?.role?.name ?? "";
  const isStaff = role === "staff_admin" || role === "staff_registry";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-lg rounded-lg bg-white p-6 shadow-lg dark:bg-gray-900">
        <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">
          {isStaff ? "NGO Details" : "Payment Details"}
        </h2>

        {/* Details Section */}
        <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
          {isStaff ? (
            <>
              <p>
                <strong>NGO Name:</strong> {(data as INgo).name}
              </p>
              <p>
                <strong>Type:</strong> {(data as INgo).type}
              </p>
              <p>
                <strong>Chairperson:</strong> {(data as INgo).chairpersonName} (
                {(data as INgo).chairpersonEmail})
              </p>
              <p>
                <strong>Chief Executive:</strong>{" "}
                {(data as INgo).chiefExecutiveName} (
                {(data as INgo).chiefExecutiveEmail})
              </p>
              <p>
                <strong>Founded:</strong>{" "}
                {new Date((data as INgo).dateFounded).toDateString()}
              </p>
              <p>
                <strong>Status:</strong> {(data as INgo).approveStatus}
              </p>
              <p>
                <strong>Documents:</strong>
              </p>
              <ul className="list-disc pl-6">
                {((data as INgo).minutesOfFirstMeetingUrl ||
                  (data as INgo).certificateFromRegistrarGeneralUrl ||
                  (data as INgo).swornInAffidavitUrl) && (
                  <>
                    {(data as INgo).minutesOfFirstMeetingUrl && (
                      <li>
                        <a
                          href={(data as INgo).minutesOfFirstMeetingUrl}
                          target="_blank"
                          className="text-blue-600 underline"
                        >
                          Minutes of First Meeting
                        </a>
                      </li>
                    )}
                    {(data as INgo).certificateFromRegistrarGeneralUrl && (
                      <li>
                        <a
                          href={
                            (data as INgo).certificateFromRegistrarGeneralUrl
                          }
                          target="_blank"
                          className="text-blue-600 underline"
                        >
                          Certificate from Registrar General
                        </a>
                      </li>
                    )}
                    {(data as INgo).swornInAffidavitUrl && (
                      <li>
                        <a
                          href={(data as INgo).swornInAffidavitUrl}
                          target="_blank"
                          className="text-blue-600 underline"
                        >
                          Sworn-in Affidavit
                        </a>
                      </li>
                    )}
                  </>
                )}
              </ul>
            </>
          ) : (
            <>
              <p>
                <strong>NGO Name:</strong> {(data as Payment).ngoName}
              </p>
              <p>
                <strong>Contact:</strong> {(data as Payment).contactPerson} (
                {(data as Payment).contactEmail})
              </p>
              <p>
                <strong>Type:</strong> {(data as Payment).ngoType}
              </p>
              <p>
                <strong>Status:</strong> {(data as Payment).status}
              </p>
              <p>
                <strong>Registered:</strong>{" "}
                {new Date((data as Payment).createdAt).toDateString()}
              </p>
              <p>
                <strong>Processing Fee:</strong>{" "}
                <a
                  href={(data as Payment).processingFeeUrl}
                  target="_blank"
                  className="text-blue-600 underline"
                >
                  View
                </a>
              </p>
              <p>
                <strong>Registration Fee:</strong>{" "}
                <a
                  href={(data as Payment).registrationFeeUrl}
                  target="_blank"
                  className="text-blue-600 underline"
                >
                  View
                </a>
              </p>
            </>
          )}
        </div>

        {/* Decline Reason */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Decline Reason
          </label>
          <textarea
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-white"
            rows={3}
            placeholder="Provide a reason if rejecting"
          />
          {showDeclineError && (
            <p className="mt-1 text-xs text-red-600">
              Please provide a reason before declining.
            </p>
          )}
        </div>

        {/* Buttons */}
        <div className="mt-6 flex justify-end gap-3">
          <button
            onClick={onClose}
            className="rounded-md border border-gray-300 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            onClick={handleDecline}
            className="rounded-md bg-red-500 px-4 py-2 text-sm text-white hover:bg-red-600"
          >
            Reject
          </button>
          <button
            onClick={handleApprove}
            className="rounded-md bg-green-500 px-4 py-2 text-sm text-white hover:bg-green-600"
          >
            Approve
          </button>
        </div>
      </div>
    </div>
  );
}

// export default function PaymentModal({
//   payment,
//   onClose,
// }: PaymentModalDemoProps) {
//   const [declineReason, setDeclineReason] = useState("");
//   const [showDeclineError, setShowDeclineError] = useState(false);

//   if (!payment) return null; // don’t render if no payment is selected

//   const handleApprove = () => {
//     console.log("✅ Approved payment:", payment._id);
//     const token = localStorage.getItem("accessToken")!;
//     changeAppovementStage(payment._id, token);
//     onClose();
//   };

//   const handleDecline = () => {
//     if (!declineReason.trim()) {
//       setShowDeclineError(true);
//       return;
//     }
//     console.log("❌ Declined payment:", payment._id, "Reason:", declineReason);
//     setDeclineReason("");
//     setShowDeclineError(false);
//     onClose();
//   };

//   return (
//     <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
//       <div className="w-full max-w-lg rounded-lg bg-white p-6 shadow-lg dark:bg-gray-900">
//         <h2 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">
//           Payment Details
//         </h2>
//         <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
//           <p>
//             <strong>NGO Name:</strong> {payment.ngoName}
//           </p>
//           <p>
//             <strong>Contact:</strong> {payment.contactPerson} (
//             {payment.contactEmail})
//           </p>
//           <p>
//             <strong>NGO Type:</strong> {payment.ngoType}
//           </p>
//           <p>
//             <strong>Approvement Status:</strong> {payment.status}
//           </p>
//           <p>
//             <strong>Registered:</strong>{" "}
//             {new Date(payment.createdAt).toDateString()}
//           </p>
//           <p>
//             <strong>Processing Fee:</strong>{" "}
//             <a
//               href={payment.processingFeeUrl}
//               target="_blank"
//               className="text-blue-600 underline"
//             >
//               View Document
//             </a>
//           </p>
//           <p>
//             <strong>Registration Fee:</strong>{" "}
//             <a
//               href={payment.registrationFeeUrl}
//               target="_blank"
//               className="text-blue-600 underline"
//             >
//               View Document
//             </a>
//           </p>
//         </div>

//         {/* Decline Reason */}
//         <div className="mt-4">
//           <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
//             Decline Reason
//           </label>
//           <textarea
//             value={declineReason}
//             onChange={(e) => setDeclineReason(e.target.value)}
//             className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-white"
//             rows={3}
//             placeholder="Provide a reason if rejecting this payment"
//           />
//           {showDeclineError && (
//             <p className="mt-1 text-xs text-red-600">
//               Please provide a reason before declining.
//             </p>
//           )}
//         </div>

//         {/* Buttons */}
//         <div className="mt-6 flex justify-end gap-3">
//           <button
//             onClick={onClose}
//             className="rounded-md border border-gray-300 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
//           >
//             Cancel
//           </button>
//           <button
//             onClick={handleDecline}
//             className="rounded-md bg-red-500 px-4 py-2 text-sm text-white hover:bg-red-600"
//           >
//             Reject
//           </button>
//           <button
//             onClick={handleApprove}
//             className="rounded-md bg-green-500 px-4 py-2 text-sm text-white hover:bg-green-600"
//           >
//             Approve
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// }
