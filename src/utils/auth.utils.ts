import {
  getLoggedInUser,
  checkIsAuthenticated,
  logoutUser,
  UserRole,
} from "@/services/auth.services";

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  return await checkIsAuthenticated();
};

/**
 * Get current user data
 */
export const getCurrentUser = async () => {
  const user = await getLoggedInUser();
  console.log("getCurrentUser result:", user); // Debug log
  return user;
};

/**
 * Logout user and redirect to login
 */
export const logout = async () => {
  const token = localStorage.getItem("accessToken")!;
  await logoutUser(token);
  // Redirect to login page
  if (typeof window !== "undefined") {
    window.location.href = "/auth/sign-in";
  }
};

/**
 * Check if user has required role
 */
export const hasRole = async (requiredRole: UserRole): Promise<boolean> => {
  const user = await getCurrentUser();
  if (!user) return false;

  return user.role.name === requiredRole;
};

/**
 * Check if user has any of the required roles
 */

const isUserRole = (role: string): role is UserRole => {
  return [
    "super_admin",
    "ngo_admin",
    "cso_chair",
    "staff_registry",
    "staff_admin",
    "finance_officer",
    "programmes_officer",
  ].includes(role as UserRole);
};

export const hasAnyRole = async (
  requiredRoles: UserRole[],
): Promise<boolean> => {
  const user = await getCurrentUser();
  if (!user) return false;

  if (!isUserRole(user.role.name)) return false;

  return requiredRoles.includes(user.role.name);
};

/**
 * Get user's NGO ID if they are associated with an NGO
 */
export const getUserNgoId = async (): Promise<string | null> => {
  const user = await getCurrentUser();
  return user?.ngoId || null;
};

/**
 * Check if user is a super admin
 */
export const isSuperAdmin = (): Promise<boolean> => {
  return hasRole("super_admin");
};

/**
 * Check if user is an NGO admin
 */
export const isNgoAdmin = (): Promise<boolean> => {
  return hasRole("ngo_admin");
};

/**
 * Check if user is CONGOMA staff
 */
export const isCongomaStaff = (): Promise<boolean> => {
  const staffRoles: UserRole[] = [
    "staff_registry",
    "staff_admin",
    "finance_officer",
    "programmes_officer",
  ];
  return hasAnyRole(staffRoles);
};

/**
 * Check if user is a finance officer
 */
export const isFinanceOfficer = (): Promise<boolean> => {
  return hasRole("finance_officer");
};
