// services/auth.service.ts
import request from "./api";
import { Role } from "./role.services";

// User role types
export type UserRole =
  | "super_admin"
  | "ngo_admin"
  | "cso_chair"
  | "staff_registry"
  | "staff_admin"
  | "finance_officer"
  | "programmes_officer";

export interface LoginPayload {
  email: string;
  password: string;
}
export interface User {
  _id: string;
  email: string;
  fullname: string;
  role: Role;
  ngoId?: string;
  status: string;
  emailVerified: boolean;
}

export interface LoginResponse {
  message: string;
  accessToken: string;
  refreshToken: string;
  user: User;
}

export interface InviteUserPayload {
  email: string;
  fullname: string;
  role: string;
  ngoId?: string;
}

export interface RegisterPayload {
  fullname: string;
  email: string;
  password: string;
  role: string;
  assignedRoles?: string[];
}
export const signUp = async (
  data: Omit<RegisterPayload, "role" | "assignedRoles">,
) => {
  const res = request<any>("/users/ngo", "POST", data);
  return res;
};
// Login
export async function login(data: LoginPayload): Promise<LoginResponse> {
  const res = request<LoginResponse>("/auth/login", "POST", data);

  // Store tokens in localStorage
  localStorage.setItem("accessToken", (await res).accessToken);
  localStorage.setItem("refreshToken", (await res).refreshToken);
  localStorage.setItem("user", JSON.stringify((await res).user));

  return res;
}
// Invite User (admin only, requires token)
export async function inviteUser(
  data: InviteUserPayload,
  token: string,
): Promise<any> {
  return request("/auth/invite", "POST", data, token);
}

// Self Registration
export async function selfRegister(data: {
  email: string;
  fullname: string;
  role: string;
}): Promise<any> {
  return request("/auth/self-register", "POST", data);
}

// Enhanced Registration
export async function enhancedRegister(data: {
  fullname: string;
  email: string;
  password: string;
  organizationId?: string;
}): Promise<any> {
  return request("/auth/enhanced-register", "POST", data);
}

// Create Organization + Assign Admin
export async function createOrganizationWithAdmin(
  data: Record<string, any>,
  token: string,
): Promise<any> {
  return request("/ngos/create-with-admin", "POST", data, token);
}

// Register user (admin only)
export async function registerUser(
  data: RegisterPayload,
  token: string,
): Promise<any> {
  return request("/auth/register", "POST", data, token);
}

// Logout
export async function logoutUser(token: string): Promise<any> {
  localStorage.removeItem("accessToken");
  localStorage.removeItem("refreshToken");
  localStorage.removeItem("user");
  return request("/auth/logout", "POST", undefined, token);
}

// Refresh Token
export async function refreshToken(refreshToken: string): Promise<any> {
  return request("/auth/refresh", "POST", { refreshToken });
}

// Forgot Password
export async function forgotPassword(email: string): Promise<any> {
  return request("/auth/forgot-password", "POST", { email });
}

// Reset Password
export async function resetPassword(
  token: string,
  newPassword: string,
): Promise<any> {
  return request("/auth/reset-password", "POST", { token, newPassword });
}

// Send Email Verification (protected)
export async function sendEmailVerification(token: string): Promise<any> {
  return request("/auth/send-verification", "POST", undefined, token);
}

// Verify Email
export async function verifyEmail(token: string): Promise<any> {
  return request("/auth/verify-email", "POST", { token });
}

// Verify Email & Setup Password
export async function verifyEmailAndSetupPassword(
  token: string,
  password: string,
): Promise<any> {
  return request("/auth/verify-email-and-setup", "POST", { token, password });
}

// Resend Verification Email
export async function resendEmailVerification(email: string): Promise<any> {
  return request("/auth/resend-verification", "POST", { email });
}

/**
 * Check if user is authenticated
 */
export async function checkIsAuthenticated(): Promise<boolean> {
  const token = localStorage.getItem("accessToken");
  return !!token;
}

//   /**
//    * Get current user from localStorage
//    */
export async function getLoggedInUser(): Promise<User | null> {
  const userStr = localStorage.getItem("user");
  console.log("localStorage user data:", userStr); // Debug log
  if (userStr) {
    try {
      const user = JSON.parse(userStr);
      console.log("Parsed user data:", user); // Debug log
      return user;
    } catch (error) {
      console.error("Error parsing user data:", error);
      return null;
    }
  }
  return null;
}

// // // Authentication service for CONGOMA IMS
// // // Handles login, token management, and user state

// import request from "./api";

// // Base URL
// // const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL
// //   ? `${process.env.NEXT_PUBLIC_API_URL}/api`
// //   : "http://localhost:3009/api";

// // User role types
// export type UserRole =
//   | "super_admin"
//   | "ngo_admin"
//   | "cso_chair"
//   | "staff_registry"
//   | "staff_admin"
//   | "finance_officer"
//   | "programmes_officer";

// export interface LoginCredentials {
//   email: string;
//   password: string;
// }

// export interface LoginResponse {
//   message: string;
//   accessToken: string;
//   refreshToken: string;
//   user: {
//     _id: string;
//     email: string;
//     fullname: string;
//     role: UserRole;
//     ngoId?: string;
//     status: string;
//     emailVerified: boolean;
//   };
// }

// export interface ApiError {
//   message: string;
//   statusCode?: number;
//   errors?: Array<{
//     field: string;
//     message: string;
//   }>;
// }

// class AuthService {
//   // private baseURL: string;

//   // constructor() {
//   //   this.baseURL = API_BASE_URL;
//   // }

//   /**
//    * Login user with email and password
//    */
//   async login(credentials: LoginCredentials): Promise<LoginResponse> {
//     const data = await request<LoginResponse>(
//       `/auth/login`,
//       "POST",
//       credentials,
//     );

//     // Store tokens in localStorage
//     if (data.accessToken) {
//       localStorage.setItem("accessToken", data.accessToken);
//       localStorage.setItem("refreshToken", data.refreshToken);
//       localStorage.setItem("user", JSON.stringify(data.user));
//     }

//     return data;
//   }

//   /**
//    * Logout user
//    */
//   logout(): void {
//     localStorage.removeItem("accessToken");
//     localStorage.removeItem("refreshToken");
//     localStorage.removeItem("user");
//   }

//   /**
//    * Get current user from localStorage
//    */
//   getLoggedInUser() {
//     const userStr = localStorage.getItem("user");
//     if (userStr) {
//       try {
//         return JSON.parse(userStr);
//       } catch (error) {
//         console.error("Error parsing user data:", error);
//         return null;
//       }
//     }
//     return null;
//   }

//   /**
//    * Check if user is authenticated
//    */
//   isAuthenticated(): boolean {
//     const token = localStorage.getItem("accessToken");
//     return !!token;
//   }

//   /**
//    * Get access token
//    */
//   getAccessToken(): string | null {
//     return localStorage.getItem("accessToken");
//   }

//   /**
//    * Refresh access token using refresh token
//    */
//   async refreshToken(): Promise<string | null> {
//     try {
//       const refreshToken = localStorage.getItem("refreshToken");
//       if (!refreshToken) {
//         throw new Error("No refresh token available");
//       }

//       const data = await request<{ accessToken: string }>(
//         `/auth/refresh`,
//         "POST",
//         { refreshToken },
//       );

//       if (data.accessToken) {
//         localStorage.setItem("accessToken", data.accessToken);
//         return data.accessToken;
//       }

//       return null;
//     } catch (error) {
//       console.error("Token refresh failed:", error);
//       this.logout();
//       return null;
//     }
//   }
// }

// export const authService = new AuthService();

// const API_BASE_URL =
//   `${process.env.NEXT_PUBLIC_API_URL}/api` || "http://localhost:3009/api";

// // User role types
// export type UserRole =
//   | "super_admin"
//   | "ngo_admin"
//   | "cso_chair"
//   | "staff_registry"
//   | "staff_admin"
//   | "finance_officer"
//   | "programmes_officer";

// export interface LoginCredentials {
//   email: string;
//   password: string;
// }

// export interface LoginResponse {
//   message: string;
//   accessToken: string;
//   refreshToken: string;
//   user: {
//     _id: string;
//     email: string;
//     fullname: string;
//     role: UserRole;
//     ngoId?: string;
//     status: string;
//     emailVerified: boolean;
//   };
// }

// export interface ApiError {
//   message: string;
//   statusCode?: number;
//   errors?: Array<{
//     field: string;
//     message: string;
//   }>;
// }

// class AuthService {
//   private baseURL: string;

//   constructor() {
//     this.baseURL = API_BASE_URL;
//   }

//   /**
//    * Login user with email and password
//    */
//   async login(credentials: LoginCredentials): Promise<LoginResponse> {
//     console.log("Logging in user", credentials);
//     try {
//       const response = await fetch(`${this.baseURL}/auth/login`, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(credentials),
//       });

//       const data = await response.json();

//       if (!response.ok) {
//         throw new Error(data.message || "Login failed");
//       }

//       // Store tokens in localStorage
//       if (data.accessToken) {
//         localStorage.setItem("accessToken", data.accessToken);
//         localStorage.setItem("refreshToken", data.refreshToken);
//         localStorage.setItem("user", JSON.stringify(data.user));
//       }

//       return data;
//     } catch (error) {
//       if (error instanceof Error) {
//         throw new Error(error.message);
//       }
//       throw new Error("An unexpected error occurred");
//     }
//   }

//   /**
//    * Logout user
//    */
//   logout(): void {
//     localStorage.removeItem("accessToken");
//     localStorage.removeItem("refreshToken");
//     localStorage.removeItem("user");
//   }

//   /**
//    * Get current user from localStorage
//    */
//   getCurrentUser() {
//     const userStr = localStorage.getItem("user");
//     if (userStr) {
//       try {
//         return JSON.parse(userStr);
//       } catch (error) {
//         console.error("Error parsing user data:", error);
//         return null;
//       }
//     }
//     return null;
//   }

//   /**
//    * Check if user is authenticated
//    */
//   isAuthenticated(): boolean {
//     const token = localStorage.getItem("accessToken");
//     return !!token;
//   }

//   /**
//    * Get access token
//    */
//   getAccessToken(): string | null {
//     return localStorage.getItem("accessToken");
//   }

//   /**
//    * Refresh access token using refresh token
//    */
//   async refreshToken(): Promise<string | null> {
//     try {
//       const refreshToken = localStorage.getItem("refreshToken");
//       if (!refreshToken) {
//         throw new Error("No refresh token available");
//       }

//       const response = await fetch(`${this.baseURL}/auth/refresh`, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify({ refreshToken }),
//       });

//       const data = await response.json();

//       if (!response.ok) {
//         throw new Error(data.message || "Token refresh failed");
//       }

//       if (data.accessToken) {
//         localStorage.setItem("accessToken", data.accessToken);
//         return data.accessToken;
//       }

//       return null;
//     } catch (error) {
//       console.error("Token refresh failed:", error);
//       this.logout();
//       return null;
//     }
//   }
// }

// export const authService = new AuthService();
