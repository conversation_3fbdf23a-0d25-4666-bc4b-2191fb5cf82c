import { FinanceOfficerStatistics } from "@/types/finance-officer-statistics.types";
import request from "./api";
import { ApiResponse } from "./role.services";
import { StaffRegistryStatistics } from "@/types/staff-registry-statistics.types";
import { SuperAdminStatistics } from "@/types/super-admin-statistics.types";
import { StaffAdminStatistics } from "@/types/staff-admin-statistics.types";

// Get Finance Officer Statistics
export async function getFinanceOfficerStatistics(
  token?: string,
): Promise<ApiResponse<FinanceOfficerStatistics>> {
  return request<ApiResponse<FinanceOfficerStatistics>>(
    "/statistics/finance-officer",
    "GET",
    undefined,
    token,
  );
}

// Get Staff Registry Statistics
export async function getStaffRegistryStatistics(
  token?: string,
): Promise<ApiResponse<StaffRegistryStatistics>> {
  return request<ApiResponse<StaffRegistryStatistics>>(
    "/statistics/staff-registry",
    "GET",
    undefined,
    token,
  );
}

// Get Super Admin Statistics
export async function getSuperAdminStatistics(
  token?: string,
): Promise<ApiResponse<SuperAdminStatistics>> {
  return request<ApiResponse<SuperAdminStatistics>>(
    "/statistics/super-admin",
    "GET",
    undefined,
    token,
  );
}

// Get Staff Admin Statistics
export async function getStaffAdminStatistics(
  token?: string,
): Promise<ApiResponse<StaffAdminStatistics>> {
  return request<ApiResponse<StaffAdminStatistics>>(
    "/statistics/staff-admin",
    "GET",
    undefined,
    token,
  );
}
