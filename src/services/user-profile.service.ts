import request from "./api";
import { ApiResponse } from "./api";
type Profile = {
  id: string;
  email: string;
  fullname: string;
  role: string;
  ngoName: string | null;
  createdAt: string;
  updatedAt: string;
};

type UpdateProfilePayload = {
  email?: string;
  fullname?: string;
};

export const getProfile = async (
  token: string,
): Promise<ApiResponse<Profile>> => {
  return request<ApiResponse<Profile>>("/profile", "GET", undefined, token);
};

export const updateProfile = async (
  payload: UpdateProfilePayload,
  token: string,
): Promise<ApiResponse<Profile>> => {
  return request<ApiResponse<Profile>>("/profiles", "PUT", payload, token);
};
