// services/api.ts
const API_BASE_URL =
  `${process.env.NEXT_PUBLIC_API_URL}/api` || "http://localhost:3009/api";

type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

export default async function request<T>(
  endpoint: string,
  method: HttpMethod = "GET",
  body?: unknown,
  token?: string,
): Promise<T> {
  const isFormData = body instanceof FormData;
  const headers: HeadersInit = {
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
  };

  // only set JSON header if not FormData
  if (!isFormData) {
    headers["Content-Type"] = "application/json";
  }
  const res = await fetch(`${API_BASE_URL}${endpoint}`, {
    method,
    headers,
    body: body
      ? isFormData
        ? (body as FormData) // pass directly
        : JSON.stringify(body)
      : undefined,
    cache: "no-store", // disable Next.js caching if needed
  });

  if (!res.ok) {
    const error = await res.json().catch(() => ({}));
    throw new Error(
      error.message || `Request failed with status ${res.status}`,
    );
  }

  return res.json();
}

export interface ApiResponse<T = any> {
  status: "success" | "error";
  message: string;
  data?: T;
}
