// services/ngoProjects.service.ts
import request from "./api";

// --- Interfaces ---
export interface INgoProject {
  _id: string;
  ngo: string;
  name: string;
  description: string;
  progress?: number;
  budget?: number;
  district?: string;
  startDate?: string;
  endDate?: string;
  urls?: string[];
  status?: string;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
  imageUrl?: string;
}

export interface ApiResponse<T> {
  status?: "success" | "error"; // optional since your controller sometimes returns raw objects
  message?: string;
  error?: string;
  data?: T;
}

// --- Services ---

/**
 * Create a new NGO project
 */
export async function createNgoProject(
  payload: FormData,
  token: string,
) {
  return await request<INgoProject>("/ngo-projects", "POST", payload, token);
}

/**
 * Get all NGO projects
 */
export async function getAllNgoProjects(token?: string) {
  return await request<INgoProject[]>("/ngo-projects", "GET", undefined, token);
}

/**
 * Get projects by NGO ID
 */
export async function getNgoProjectsByNgo(ngoId: string, token?: string) {
  return await request<INgoProject[]>(
    `/ngo-projects/ngo/${ngoId}`,
    "GET",
    undefined,
    token,
  );
}

/**
 * Update an NGO project
 */
export async function updateNgoProject(
  id: string,
  payload: Partial<{
    description: string;
    urls: string[];
    budget: number;
    status: string;
    startDate: string;
    endDate: string;
  }>,
  token: string,
) {
  return await request<INgoProject>(
    `/ngo-projects/${id}`,
    "PUT",
    payload,
    token,
  );
}

/**
 * Delete an NGO project
 */
export async function deleteNgoProject(id: string, token: string) {
  return await request<{ message: string }>(
    `/ngo-projects/${id}`,
    "DELETE",
    undefined,
    token,
  );
}
