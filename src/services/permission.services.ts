// services/permission.service.ts
import request from "./api";

export interface Permission {
  _id: string;
  name: string;
  description: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ApiResponse<T> {
  status: "success" | "error";
  message: string;
  data: T;
}

/**
 * Create a new permission
 */
export async function createPermission(
  permission: {
    name: string;
    description: string;
  },
  token: string,
): Promise<ApiResponse<Permission>> {
  return request<ApiResponse<Permission>>(
    "/permissions",
    "POST",
    permission,
    token,
  );
}

/**
 * Get all permissions
 */
export async function getAllPermissions(
  token: string,
): Promise<ApiResponse<Permission[]>> {
  return request<ApiResponse<Permission[]>>(
    "/permissions",
    "GET",
    undefined,
    token,
  );
}

/**
 * Get a permission by ID
 */
export async function getPermissionById(
  id: string,
  token: string,
): Promise<ApiResponse<Permission>> {
  return request<ApiResponse<Permission>>(
    `/permissions/${id}`,
    "GET",
    undefined,
    token,
  );
}

/**
 * Update a permission by ID
 */
export async function updatePermissionById(
  id: string,
  updates: {
    name?: string;
    description?: string;
  },
  token: string,
): Promise<ApiResponse<Permission>> {
  return request<ApiResponse<Permission>>(
    `/permissions/${id}`,
    "PUT",
    updates,
    token,
  );
}

/**
 * Delete a permission by ID
 */
export async function deletePermissionById(
  id: string,
  token: string,
): Promise<ApiResponse<null>> {
  return request<ApiResponse<null>>(
    `/permissions/${id}`,
    "DELETE",
    undefined,
    token,
  );
}
