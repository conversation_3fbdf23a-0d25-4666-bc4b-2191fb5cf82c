// services/role.service.ts
import request from "./api";
import { UserRole } from "./auth.services";

export interface Role {
  _id: string;
  name: User<PERSON>ole;
  description: string;
  permissions: {
    _id: string;
    name: string;
    description: string;
  }[];
  createdAt?: string;
  updatedAt?: string;
}

export interface ApiResponse<T> {
  status: "success" | "error";
  message: string;
  data: T;
}

/**
 * Create a new role
 */
export async function createRole(
  role: {
    name: string;
    description: string;
    permissions: string[];
  },
  token: string,
): Promise<ApiResponse<Role>> {
  return request<ApiResponse<Role>>("/roles", "POST", role, token);
}

/**
 * Get all roles
 */
export async function getRoles(token: string): Promise<ApiResponse<Role[]>> {
  return request<ApiResponse<Role[]>>("/roles", "GET", undefined, token);
}

/**
 * Get role by ID
 */
export async function getRoleById(
  id: string,
  token: string,
): Promise<ApiResponse<Role>> {
  return request<ApiResponse<Role>>(`/roles/${id}`, "GET", undefined, token);
}

/**
 * Update role by ID
 */
export async function updateRoleById(
  id: string,
  updates: {
    name?: string;
    description?: string;
    permissions?: string[];
  },
  token: string,
): Promise<ApiResponse<Role>> {
  return request<ApiResponse<Role>>(`/roles/${id}`, "PUT", updates, token);
}

/**
 * Delete role by ID
 */
export async function deleteRoleById(
  id: string,
  token: string,
): Promise<ApiResponse<null>> {
  return request<ApiResponse<null>>(`/roles/${id}`, "DELETE", undefined, token);
}
