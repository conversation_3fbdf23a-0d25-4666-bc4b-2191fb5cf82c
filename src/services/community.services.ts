// services/community.service.ts
import request from "./api";

// ---------- Types ----------
export interface Community {
  _id: string;
  name: string;
  location: string;
  description: string;
  date_formed: string;
  coordinator: string;
  createdBy: string;
  members: CommunityMember[];
}

export interface CommunityMember {
  ngo: string;
  status: "pending" | "approved" | "rejected";
  joinedAt: string;
  approvedAt?: string;
  approvedBy?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  [key: string]: any;
  community?: T;
  communities?: T[];
}

// ---------- Service Functions ----------

// Create community
export async function createCommunity(
  data: {
    name: string;
    location: string;
    description: string;
    date_formed?: string;
    coordinator: string;
  },
  token: string,
): Promise<ApiResponse<Community>> {
  return request(`/communities`, "POST", data, token);
}

// Join community
export async function joinCommunity(
  communityId: string,
  token: string,
): Promise<ApiResponse<null>> {
  return request(`/communities/${communityId}/join`, "POST", undefined, token);
}

// Leave community
export async function leaveCommunity(
  communityId: string,
  token: string,
): Promise<ApiResponse<Community>> {
  return request(`/communities/${communityId}/leave`, "POST", undefined, token);
}

// Get all communities
export async function getAllCommunities(
  token?: string,
): Promise<ApiResponse<Community[]>> {
  return request(`/communities`, "GET", undefined, token);
}

// Get communities by location
export async function getCommunitiesByLocation(
  location: string,
  token?: string,
): Promise<ApiResponse<Community[]>> {
  return request(`/communities/location/${location}`, "GET", undefined, token);
}

// Get community details
export async function getCommunityDetails(
  communityId: string,
  token?: string,
): Promise<ApiResponse<Community>> {
  return request(`/communities/${communityId}`, "GET", undefined, token);
}

// Update community
export async function updateCommunity(
  communityId: string,
  data: Partial<Community>,
  token: string,
): Promise<ApiResponse<Community>> {
  return request(`/communities/${communityId}`, "PATCH", data, token);
}

// Delete community
export async function deleteCommunity(
  communityId: string,
  token: string,
): Promise<ApiResponse<null>> {
  return request(`/communities/${communityId}`, "DELETE", undefined, token);
}

// List pending requests
export async function getPendingRequests(
  communityId: string,
  token: string,
): Promise<ApiResponse<CommunityMember[]>> {
  return request(
    `/communities/${communityId}/pending`,
    "GET",
    undefined,
    token,
  );
}

// Approve member
export async function approveMember(
  communityId: string,
  ngoId: string,
  token: string,
): Promise<ApiResponse<CommunityMember>> {
  return request(
    `/communities/${communityId}/approve`,
    "POST",
    { ngoId },
    token,
  );
}

// Reject member
export async function rejectMember(
  communityId: string,
  ngoId: string,
  token: string,
): Promise<ApiResponse<CommunityMember>> {
  return request(
    `/communities/${communityId}/reject`,
    "POST",
    { ngoId },
    token,
  );
}

// Add member (coordinator only)
export async function addMember(
  communityId: string,
  data: {
    ngoId: string;
    status?: "pending" | "approved" | "rejected";
    joinedAt?: string;
    approvedAt?: string;
    approvedBy?: string;
  },
  token: string,
): Promise<ApiResponse<Community>> {
  return request(`/communities/${communityId}/add-member`, "POST", data, token);
}
