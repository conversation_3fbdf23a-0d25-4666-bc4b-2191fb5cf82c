// services/certificates.service.ts
import request from "./api";

// ---------- Types ----------
export type CertificateType =
  | "registration certificate"
  | "subscription certificate";
export type CertificateStatus = "pending" | "valid" | "expired";
export type Certificate = {
  id: string;
  organizationName: string;
  certificateType: CertificateType;
  certificateNumber: string;
  issueDate: string;
  expiryDate: string;
  status: CertificateStatus;
  qrCodeUrl: string;
  certificateUrl: string;
  verificationUrl: string;
};

export type CertificateResponse = {
  status: string;
  message: string;
  data: {
    certificates: Certificate[];
  };
};

export type CertificateFilter = {
  validityStatus?: CertificateStatus;
  certificateType?: CertificateType;
  certificateNumber?: string;
};

// ---------- Helper Functions ----------
function queryGenerator(filter: CertificateFilter): string | undefined {
  const params = new URLSearchParams();
  if (filter.validityStatus)
    params.append("validityStatus", filter.validityStatus);
  if (filter.certificateType)
    params.append("certificateType", filter.certificateType);
  if (filter.certificateNumber)
    params.append("certificateNumber", filter.certificateNumber);
  return params.toString();
}

// ---------- Service Functions ----------
export async function getCertificates(
  filter?: CertificateFilter,
  orgId?: string,
  token?: string,
): Promise<CertificateResponse> {
  if (!filter) filter = {};
  const query = queryGenerator(filter);

  const url = orgId
    ? `/certificates/${orgId}?${query}`
    : `/certificates?${query}`;
  return await request<CertificateResponse>(url, "GET", undefined, token);
}

// Get certificates for a specific NGO
export async function getNgoCertificates(
  ngoId: string,
  filter?: CertificateFilter,
  token?: string,
): Promise<CertificateResponse> {
  if (!filter) filter = {};
  const query = queryGenerator(filter);
  
  return await request<CertificateResponse>(
    `/certificates/${ngoId}?${query}`,
    "GET",
    undefined,
    token,
  );
}
