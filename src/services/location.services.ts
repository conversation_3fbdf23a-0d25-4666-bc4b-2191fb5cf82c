// services/location.service.ts
import request from "./api";

export interface Location {
  _id: string;
  district: string;
  region: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  count?: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// ------------------- Public Endpoints ------------------- //

// Get all districts
export async function getAllDistricts(): Promise<ApiResponse<Location[]>> {
  return request<ApiResponse<Location[]>>("/locations/districts", "GET");
}

// Get all regions
export async function getAllRegions(): Promise<ApiResponse<string[]>> {
  return request<ApiResponse<string[]>>("/locations/regions", "GET");
}

// Get districts by region
export async function getDistrictsByRegion(
  region: string,
): Promise<ApiResponse<{ district: string }[]>> {
  return request<ApiResponse<{ district: string }[]>>(
    `/locations/districts/${encodeURIComponent(region)}`,
    "GET",
  );
}

// Validate location
export async function validateLocation(payload: {
  region: string;
  district: string;
}): Promise<ApiResponse<Location>> {
  return request<ApiResponse<Location>>("/locations/validate", "POST", payload);
}

// ------------------- Admin Protected Endpoints ------------------- //

// Create a new district
export async function createDistrict(
  payload: { district: string; region: string },
  token: string,
): Promise<ApiResponse<Location>> {
  return request<ApiResponse<Location>>(
    "/locations/districts",
    "POST",
    payload,
    token,
  );
}

// Update a district
export async function updateDistrict(
  id: string,
  payload: { district: string; region: string },
  token: string,
): Promise<ApiResponse<Location>> {
  return request<ApiResponse<Location>>(
    `/locations/districts/${id}`,
    "PUT",
    payload,
    token,
  );
}

// Delete a district
export async function deleteDistrict(
  id: string,
  token: string,
): Promise<ApiResponse<Location>> {
  return request<ApiResponse<Location>>(
    `/locations/districts/${id}`,
    "DELETE",
    undefined,
    token,
  );
}

// Get all locations with pagination
export async function getAllLocations(
  params: { page?: number; limit?: number; region?: string } = {},
  token: string,
): Promise<ApiResponse<Location[]>> {
  const query = new URLSearchParams(
    Object.entries(params).reduce(
      (acc, [key, val]) => {
        if (val !== undefined) acc[key] = String(val);
        return acc;
      },
      {} as Record<string, string>,
    ),
  ).toString();

  return request<ApiResponse<Location[]>>(
    `/locations${query ? `?${query}` : ""}`,
    "GET",
    undefined,
    token,
  );
}
