// services/cso.service.ts
import request from "./api";

// ---------- Types ----------
export interface CsoMember {
  ngo: string;
  status: "pending" | "approved" | "rejected";
  joinedAt?: string;
  approvedAt?: string;
  approvedBy?: string;
}

export interface CsoNetwork {
  _id: string;
  name: string;
  sector: string[];
  ngoId: {
    _id: string;
    name: string;
    email?: string;
    sectorsOfOperation?: string[];
    locationsOfOperation?: string[];
  };
  members: CsoMember[];
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  cso?: T;
  csos?: T[];
  pendingMembers?: CsoMember[];
  member?: CsoMember;
}

// ---------- Service Functions ----------

// Create a CSO Network
export async function createCsoNetwork(
  ngoId: string,
  token: string,
): Promise<ApiResponse<CsoNetwork>> {
  return request("/csos", "POST", { ngoId }, token);
}

// Delete a CSO Network
export async function deleteCsoNetwork(
  csoId: string,
  token: string,
): Promise<ApiResponse> {
  return request(`/csos/${csoId}`, "DELETE", undefined, token);
}

// Get all CSO Networks
export async function getAllCsos(
  token: string,
): Promise<ApiResponse<CsoNetwork[]>> {
  return request("/csos", "GET", undefined, token);
}

// Join a CSO Network
export async function joinCso(
  csoId: string,
  token: string,
): Promise<ApiResponse<CsoNetwork>> {
  return request(`/csos/${csoId}/join`, "POST", undefined, token);
}

// Get pending membership requests
export async function getPendingRequests(
  csoId: string,
  token: string,
): Promise<ApiResponse<CsoMember[]>> {
  return request(`/csos/${csoId}/pending`, "GET", undefined, token);
}

// Approve a member
export async function approveCsoMember(
  csoId: string,
  ngoId: string,
  token: string,
): Promise<ApiResponse<CsoMember>> {
  return request(`/csos/${csoId}/approve`, "POST", { ngoId }, token);
}

// Reject a member
export async function rejectCsoMember(
  csoId: string,
  ngoId: string,
  token: string,
): Promise<ApiResponse<CsoMember>> {
  return request(`/csos/${csoId}/reject`, "POST", { ngoId }, token);
}

// Get a CSO Network by ID
export async function getCsoById(
  csoId: string,
  token: string,
): Promise<ApiResponse<CsoNetwork>> {
  return request(`/csos/${csoId}`, "GET", undefined, token);
}
