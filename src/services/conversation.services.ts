// services/conversation.service.ts
import request from "./api";

// ---------- Types ----------
export interface Comment {
  _id: string;
  sender: string;
  text: string;
  createdAt: string;
}

export interface Message {
  _id: string;
  sender: string;
  text: string;
  createdAt: string;
  comments: Comment[];
}

export interface Conversation {
  _id: string;
  title: string;
  communityId: { _id: string; name: string };
  participants: { _id: string; name: string }[];
  createdBy: string;
  messages: Message[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  [key: string]: any;
  conversation?: T;
  conversations?: T[];
  updatedMessage?: Message;
}

// ---------- Service Functions ----------

// Get all conversations
export async function getAllConversations(
  token?: string,
): Promise<ApiResponse<Conversation[]>> {
  return request("/conversations", "GET", undefined, token);
}

// Get a single conversation by ID
export async function getConversationById(
  conversationId: string,
  token?: string,
): Promise<ApiResponse<Conversation>> {
  return request(`/conversations/${conversationId}`, "GET", undefined, token);
}

// Create a new conversation
export async function createConversation(
  data: { communityId: string; title: string; initialMessage?: string },
  token: string,
): Promise<ApiResponse<Conversation>> {
  return request("/conversations", "POST", data, token);
}

// Add comment to a message
export async function addComment(
  conversationId: string,
  messageId: string,
  text: string,
  token: string,
): Promise<ApiResponse<Message>> {
  return request(
    `/conversations/${conversationId}/messages/${messageId}/comment`,
    "POST",
    { text },
    token,
  );
}

// Update a conversation
export async function updateConversation(
  conversationId: string,
  updateData: Partial<Conversation>,
  token: string,
): Promise<ApiResponse<Conversation>> {
  return request(
    `/conversations/${conversationId}`,
    "PATCH",
    updateData,
    token,
  );
}

// Delete a conversation
export async function deleteConversation(
  conversationId: string,
  token: string,
): Promise<ApiResponse<null>> {
  return request(
    `/conversations/${conversationId}`,
    "DELETE",
    undefined,
    token,
  );
}
