// services/organization-documents.service.ts
import request from "./api";

// ---------- Types ----------
export interface OrganizationDocument {
  id: string;
  name: string;
  type: DocumentType;
  url: string;
  uploadDate: string;
  size?: number;
  status: "uploaded" | "pending" | "approved" | "rejected" | "missing";
  required?: boolean;
}

export type DocumentType = 
  | "constitution"
  | "minutes_of_first_meeting"
  | "certificate_from_registrar_general"
  | "sworn_in_affidavit"
  | "registration_fee"
  | "processing_fee";

export interface DocumentUploadResponse {
  status: "success" | "error";
  message: string;
  data?: {
    url: string;
    filename: string;
  };
}

export interface OrganizationDocumentsResponse {
  status: "success" | "error";
  message: string;
  data: {
    documents: OrganizationDocument[];
    organization: {
      id: string;
      name: string;
      registrationNumber: string;
    };
  };
}




export async function getOrganizationDocuments(
  ngoId: string,
  token: string,
): Promise<OrganizationDocumentsResponse> {
  try {
    // Use existing NGO endpoint to get organization data
    const ngoResponse = await request<{
      status: string;
      message: string;
      data: any;
    }>(`/ngos/${ngoId}`, "GET", undefined, token);

    const ngo = ngoResponse.data;
    console.log("NGO data received:", ngo); // Debug log

    // Map NGO data to document structure
    const documents: OrganizationDocument[] = [
      {
        id: "constitution",
        name: "Constitution",
        type: "constitution",
        url: ngo.constitutionUrl || "",
        uploadDate: ngo.updatedAt || ngo.createdAt,
        status: ngo.constitutionUrl ? "uploaded" : "missing",
        required: false,
      },
      {
        id: "minutes_of_first_meeting",
        name: "Minutes of First Meeting",
        type: "minutes_of_first_meeting",
        url: ngo.minutesOfFirstMeetingUrl || "",
        uploadDate: ngo.updatedAt || ngo.createdAt,
        status: ngo.minutesOfFirstMeetingUrl ? "uploaded" : "missing",
        required: true,
      },
      {
        id: "certificate_from_registrar_general",
        name: "Certificate from Registrar General",
        type: "certificate_from_registrar_general",
        url: ngo.certificateFromRegistrarGeneralUrl || "",
        uploadDate: ngo.updatedAt || ngo.createdAt,
        status: ngo.certificateFromRegistrarGeneralUrl ? "uploaded" : "missing",
        required: true,
      },
      {
        id: "sworn_in_affidavit",
        name: "Sworn-in Affidavit",
        type: "sworn_in_affidavit",
        url: ngo.swornInAffidavitUrl || "",
        uploadDate: ngo.updatedAt || ngo.createdAt,
        status: ngo.swornInAffidavitUrl ? "uploaded" : "missing",
        required: false,
      },
      {
        id: "registration_fee",
        name: "Registration Fee Receipt",
        type: "registration_fee",
        url: ngo.registrationFeeUrl || "",
        uploadDate: ngo.updatedAt || ngo.createdAt,
        status: ngo.registrationFeeUrl ? "uploaded" : "missing",
        required: true,
      },
      {
        id: "processing_fee",
        name: "Processing Fee Receipt",
        type: "processing_fee",
        url: ngo.processingFeeUrl || "",
        uploadDate: ngo.updatedAt || ngo.createdAt,
        status: ngo.processingFeeUrl ? "uploaded" : "missing",
        required: true,
      },
    ];

    return {
      status: "success",
      message: "Organization documents retrieved successfully",
      data: {
        documents,
        organization: {
          id: ngo._id,
          name: ngo.name,
          registrationNumber: ngo.registrationNumber,
        },
      },
    };
  } catch (error) {
    throw new Error(`Failed to fetch organization documents: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Upload organization document (placeholder - backend routes need to be implemented)
export async function uploadOrganizationDocument(
  ngoId: string,
  documentType: DocumentType,
  file: File,
  token: string,
): Promise<DocumentUploadResponse> {
  // For now, simulate upload since backend routes are not implemented yet
  // In production, this would upload to the actual backend
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: "success",
        message: "Document uploaded successfully (simulated)",
        data: {
          url: `https://example.com/documents/${ngoId}/${documentType}-${Date.now()}.pdf`,
          filename: file.name,
        },
      });
    }, 2000);
  });
}

// Update organization document (placeholder - backend routes need to be implemented)
export async function updateOrganizationDocument(
  ngoId: string,
  documentType: DocumentType,
  file: File,
  token: string,
): Promise<DocumentUploadResponse> {
  // For now, simulate update since backend routes are not implemented yet
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: "success",
        message: "Document updated successfully (simulated)",
        data: {
          url: `https://example.com/documents/${ngoId}/${documentType}-${Date.now()}.pdf`,
          filename: file.name,
        },
      });
    }, 2000);
  });
}

// Delete organization document (placeholder - backend routes need to be implemented)
export async function deleteOrganizationDocument(
  ngoId: string,
  documentType: DocumentType,
  token: string,
): Promise<{ status: "success" | "error"; message: string }> {
  // For now, simulate delete since backend routes are not implemented yet
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        status: "success",
        message: "Document deleted successfully (simulated)",
      });
    }, 1000);
  });
}

// Helper function to get document type display name
export function getDocumentTypeDisplayName(type: DocumentType): string {
  const displayNames: Record<DocumentType, string> = {
    constitution: "Constitution",
    minutes_of_first_meeting: "Minutes of First Meeting",
    certificate_from_registrar_general: "Certificate from Registrar General",
    sworn_in_affidavit: "Sworn-in Affidavit",
    registration_fee: "Registration Fee Receipt",
    processing_fee: "Processing Fee Receipt",
  };
  return displayNames[type] || type;
}

// Helper function to get document field name in NGO model
export function getDocumentFieldName(type: DocumentType): string {
  const fieldNames: Record<DocumentType, string> = {
    constitution: "constitutionUrl",
    minutes_of_first_meeting: "minutesOfFirstMeetingUrl",
    certificate_from_registrar_general: "certificateFromRegistrarGeneralUrl",
    sworn_in_affidavit: "swornInAffidavitUrl",
    registration_fee: "registrationFeeUrl",
    processing_fee: "processingFeeUrl",
  };
  return fieldNames[type] || type;
}

// Helper function to check if document is required
export function isDocumentRequired(type: DocumentType): boolean {
  const requiredDocs: DocumentType[] = [
    "minutes_of_first_meeting",
    "certificate_from_registrar_general",
    "registration_fee",
    "processing_fee",
  ];
  return requiredDocs.includes(type);
}
