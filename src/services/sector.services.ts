// services/sectorService.ts
import request from "./api";

// Types for Sector
export interface Sector {
  _id?: string;
  name: string;
  description?: string;
  category: string;
  status?: string;
  organizationCount?: number;
  createdAt?: string;
  updatedAt?: string;
}

interface ApiResponse<T> {
  status: "success" | "error";
  message: string;
  data?: T;
}

// Create Sector
export async function createSector(
  sector: Omit<Sector, "_id" | "organizationCount" | "createdAt" | "updatedAt">,
  token?: string,
): Promise<ApiResponse<Sector>> {
  return request<ApiResponse<Sector>>("/sectors", "POST", sector, token);
}

// Get all Sectors (with optional filters)
export async function getAllSectors(
  filters?: { name?: string; status?: string },
  token?: string,
): Promise<ApiResponse<Sector[]>> {
  const query = filters
    ? `?${new URLSearchParams(filters as Record<string, string>).toString()}`
    : "";
  return request<ApiResponse<Sector[]>>(
    `/sectors${query}`,
    "GET",
    undefined,
    token,
  );
}

// Get a Sector by ID
export async function getSectorById(
  id: string,
  token?: string,
): Promise<ApiResponse<Sector>> {
  return request<ApiResponse<Sector>>(
    `/sectors/${id}`,
    "GET",
    undefined,
    token,
  );
}

// Update a Sector by ID
export async function updateSector(
  id: string,
  updates: Partial<Sector>,
  token?: string,
): Promise<ApiResponse<Sector>> {
  return request<ApiResponse<Sector>>(`/sectors/${id}`, "PUT", updates, token);
}

// Delete a Sector by ID
export async function deleteSector(
  id: string,
  token?: string,
): Promise<ApiResponse<null>> {
  return request<ApiResponse<null>>(
    `/sectors/${id}`,
    "DELETE",
    undefined,
    token,
  );
}

// Search Sector by name
export async function searchSectors(
  name: string,
  token?: string,
): Promise<ApiResponse<Sector[]>> {
  return request<ApiResponse<Sector[]>>(
    `/sectors/search?name=${encodeURIComponent(name)}`,
    "GET",
    undefined,
    token,
  );
}
