// services/auditService.ts
import request from "./api";

export interface AuditLog {
  _id: string;
  user: {
    _id: string;
    fullname: string;
    email: string;
  };
  action: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaginatedResponse<T> {
  status: "success" | "error";
  message: string;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

/**
 * Get audit logs for a specific user
 */
export async function getUserAuditLogs(
  userId: string,
  page: number = 1,
  limit: number = 50,
  token?: string,
) {
  const data = await request<PaginatedResponse<AuditLog>>(
    `/audits/users/${userId}?page=${page}&limit=${limit}`,
    "GET",
    undefined,
    token,
  );

  return data;
}

/**
 * Get audit logs by action type
 */
export async function getAuditLogsByAction(
  action: string,
  page: number = 1,
  limit: number = 50,
  token?: string,
) {
  const data = await request<PaginatedResponse<AuditLog>>(
    `/audits/actions/${action}?page=${page}&limit=${limit}`,
    "GET",
    undefined,
    token,
  );

  return data;
}

/**
 * Get recent audit logs (all actions)
 */
export async function getRecentAuditLogs(
  page: number = 1,
  limit: number = 50,
  token?: string,
) {
  const data = await request<PaginatedResponse<AuditLog>>(
    `/audits/recent?page=${page}&limit=${limit}`,
    "GET",
    undefined,
    token,
  );

  return data;
}
