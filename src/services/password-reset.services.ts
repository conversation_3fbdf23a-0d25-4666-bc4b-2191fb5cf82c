import request from "./api";

export interface ForgotPasswordPayload {
  email: string;
}

export interface ResetPasswordPayload {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface PasswordResetResponse {
  message: string;
}

export interface VerifyTokenResponse {
  message: string;
  email: string;
}


export const requestPasswordReset = async (payload: ForgotPasswordPayload): Promise<PasswordResetResponse> => {
  return await request<PasswordResetResponse>("/auth/forgot-password", "POST", payload);
};

export const verifyResetToken = async (token: string): Promise<VerifyTokenResponse> => {
  return await request<VerifyTokenResponse>(`/auth/verify-reset-token/${token}`, "GET");
};


export const resetPassword = async (payload: ResetPasswordPayload): Promise<PasswordResetResponse> => {
  return await request<PasswordResetResponse>("/auth/reset-password", "POST", payload);
};

export interface ChangePasswordPayload {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export const changePassword = async (payload: ChangePasswordPayload): Promise<PasswordResetResponse> => {
  const token = localStorage.getItem("accessToken");
  if (!token) {
    throw new Error("No authentication token found. Please log in again.");
  }
  return await request<PasswordResetResponse>("/auth/change-password", "POST", payload, token);
};
