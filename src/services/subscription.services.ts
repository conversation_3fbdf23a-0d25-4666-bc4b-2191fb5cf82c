// services/subscription.service.ts
import request from "./api";

// ---------- Types ----------
export type SubscriptionStatus = "pending" | "approved" | "rejected";

export interface NgoInfo {
  id: string;
  name: string;
  registrationNumber: string;
  type?: string;
  initials?: string;
  contactEmail?: string;
}

export interface ResolvedByInfo {
  name: string;
  email: string;
}

export interface Subscription {
  id: string;
  ngo: NgoInfo;
  approvedStatus: SubscriptionStatus;
  expiryDate: string;
  submittedAt: string;
  proofOfPaymentUrl: string;
  resolvedBy?: ResolvedByInfo;
  resolvedAt?: string;
  rejectedReason?: string;
  certificate?: {
    id: string;
    certificateNumber: string;
    qrCodeUrl: string;
    expiryDate: string;
    status: string;
    verificationUrl: string;
  };
}

export interface Pagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface SubscriptionsResponse {
  subscriptions: Subscription[];
  pagination?: Pagination;
  filters?: { status: string; year: number };
}

export interface ApiResponse<T = any> {
  status: "success" | "error";
  message: string;
  data?: T;
}

// ---------- Service Functions ----------

// Create a subscription
export async function createSubscription(
  ngoId: string,
  proofOfPayment: string | File,
  token: string,
): Promise<ApiResponse<Subscription>> {
  const formData = new FormData();
  formData.append("ngoId", ngoId);

  if (typeof proofOfPayment === "string") {
    formData.append("proofOfPayment", proofOfPayment);
  } else {
    formData.append("proofOfPayment", proofOfPayment);
  }

  return request<ApiResponse<Subscription>>(
    "/subscriptions",
    "POST",
    formData,
    token,
  );
}

// Get all subscriptions with optional filters
export async function getAllSubscriptions(
  token: string,
  filters?: {
    status?: SubscriptionStatus;
    year?: number;
    page?: number;
    limit?: number;
  },
): Promise<ApiResponse<SubscriptionsResponse>> {
  const query = new URLSearchParams();
  if (filters?.status) query.append("status", filters.status);
  if (filters?.year) query.append("year", filters.year.toString());
  if (filters?.page) query.append("page", filters.page.toString());
  if (filters?.limit) query.append("limit", filters.limit.toString());

  return request<ApiResponse<SubscriptionsResponse>>(
    `/subscriptions?${query.toString()}`,
    "GET",
    undefined,
    token,
  );
}

// Approve a subscription
export async function approveSubscription(
  subscriptionId: string,
  token: string,
): Promise<ApiResponse<Subscription>> {
  return request<ApiResponse<Subscription>>(
    `/subscriptions/${subscriptionId}/approve`,
    "PATCH",
    undefined,
    token,
  );
}

// Reject a subscription
export async function rejectSubscription(
  subscriptionId: string,
  rejectedReason: string,
  token: string,
): Promise<ApiResponse<Subscription>> {
  return request<ApiResponse<Subscription>>(
    `/subscriptions/${subscriptionId}/reject`,
    "PATCH",
    { rejectedReason },
    token,
  );
}

// Get subscription status for a specific NGO
export async function getSubscriptionStatus(
  ngoId: string,
  token: string,
  year?: number,
): Promise<ApiResponse<Subscription>> {
  const query = year ? `?year=${year}` : "";
  return request<ApiResponse<Subscription>>(
    `/subscriptions/ngo/${ngoId}${query}`,
    "GET",
    undefined,
    token,
  );
}

// Get subscription statistics (dashboard)
export async function getSubscriptionStatistics(
  token: string,
  year?: number,
): Promise<ApiResponse<any>> {
  const query = year ? `?year=${year}` : "";
  return request<ApiResponse<any>>(
    `/subscriptions/statistics${query}`,
    "GET",
    undefined,
    token,
  );
}
