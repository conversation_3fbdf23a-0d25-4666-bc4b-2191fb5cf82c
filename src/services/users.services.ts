// services/users.service.ts
import request from "./api";

export interface Role {
  _id: string;
  name: string;
  description: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface User {
  _id: string;
  fullname: string;
  email: string;
  role: Role;
  status: string;
  emailVerified?: boolean;
  ngoId?: string;
  twoFA?: boolean;
  createdAt: string;
}

export interface ApiResponse<T> {
  status: "success" | "error";
  message: string;
  data?: T;
  user?: User;
  ngo?: any;
}

export interface PaginatedResponse<T> {
  status: "success" | "error";
  message: string;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

/**
 * Create staff member
 */
export async function createStaffMember(
  payload: { fullname: string; email: string; role?: string; ngoId?: string },
  token?: string,
) {
  return await request<ApiResponse<User>>(
    "/users/staff",
    "POST",
    payload,
    token,
  );
}

/**
 * Create NGO account
 */
export async function createNgoAccount(
  payload: { fullname: string; email: string; password: string; ngoId: string },
  token?: string,
) {
  return await request<ApiResponse<User>>("/users/ngo", "POST", payload, token);
}

/**
 * Deactivate users
 */
export async function deactivateUsers(ids: string[], token?: string) {
  return await request<ApiResponse<null>>(
    "/users/deactivate",
    "PATCH",
    { ids },
    token,
  );
}

/**
 * Activate users
 */
export async function activateUsers(ids: string[], token?: string) {
  return await request<ApiResponse<null>>(
    "/users/activate",
    "PATCH",
    { ids },
    token,
  );
}

/**
 * Get all users (with filters & pagination)
 */
export async function getUsers(
  params?: { page?: number; limit?: number; role?: string; status?: string },
  token?: string,
) {
  const query = new URLSearchParams();
  if (params?.page) query.append("page", params.page.toString());
  if (params?.limit) query.append("limit", params.limit.toString());
  if (params?.role) query.append("role", params.role);
  if (params?.status) query.append("status", params.status);

  return await request<ApiResponse<User>>(
    `/users?${query.toString()}`,
    "GET",
    undefined,
    token,
  );
}

/**
 * Search users
 */
export async function searchUsers(query: string, token?: string) {
  return await request<ApiResponse<User[]>>(
    `/users/search?query=${encodeURIComponent(query)}`,
    "GET",
    undefined,
    token,
  );
}

/**
 * Get user profile
 */
export async function getUserProfile(token?: string) {
  return await request<ApiResponse<User>>(
    `/users/profile`,
    "GET",
    undefined,
    token,
  );
}

/**
 * Update user by ID
 */
export async function updateUserById(
  id: string,
  payload: { fullname?: string; status?: string; role?: string },
  token?: string,
) {
  return await request<ApiResponse<User>>(
    `/users/${id}`,
    "PUT",
    payload,
    token,
  );
}

/**
 * Create NGO member (for NGO admins to invite members to their organization)
 */
export async function createNgoMember(
  payload: { fullname: string; email: string },
  token?: string,
) {
  return await request<ApiResponse<User>>(
    "/users/ngo-members",
    "POST",
    payload,
    token,
  );
}

/**
 * Get NGO members (for NGO admins to view their organization's members)
 */
export async function getNgoMembers(
  params?: { page?: number; limit?: number; status?: string },
  token?: string,
) {
  const query = new URLSearchParams();
  if (params?.page) query.append("page", params.page.toString());
  if (params?.limit) query.append("limit", params.limit.toString());
  if (params?.status) query.append("status", params.status);

  return await request<{
    status: "success" | "error";
    message: string;
    data: User[];
    pagination: {
      page: number;
      limit: number;
      total: number;
    };
  }>(
    `/users/ngo-members?${query.toString()}`,
    "GET",
    undefined,
    token,
  );
}

/**
 * Remove NGO member (for NGO admins to remove members from their organization)
 */
export async function removeNgoMember(
  memberId: string,
  token?: string,
) {
  return await request<ApiResponse<null>>(
    `/users/ngo-members/${memberId}`,
    "DELETE",
    undefined,
    token,
  );
}
