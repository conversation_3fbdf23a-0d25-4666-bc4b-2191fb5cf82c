// services/ngo.service.ts
import { Sectors } from "@/types/ngo.types";
import request from "./api";
import { Sector } from "./sector.services";

export interface INgo {
  _id: string;
  name: string;
  initials: string;
  type: string;
  headquartersAddress: string;
  dateFounded: string;
  dateApprovedByGOM?: string;
  chairpersonName: string;
  chairpersonEmail: string;
  chairpersonPhone: string;
  chiefExecutiveName: string;
  chiefExecutiveEmail: string;
  chiefExecutivePhone: string;
  missionStatement?: string;
  visionStatement?: string;
  valuesStatement?: string;
  ngoBackground?: string;
  goals?: string[];
  locationsOfOperation?: string[];
  sectorsOfOperation?: Partial<Sector>[];
  numberOfEmployees?: number;
  approvedStatus?: "approved" | "pending" | "rejected";
  approvementStage?: "financial" | "documentation" | "completed";
  minutesOfFirstMeetingUrl?: string;
  certificateFromRegistrarGeneralUrl?: string;
  swornInAffidavitUrl?: string;
  registrationFeeUrl?: string;
  processingFeeUrl?: string;
  status?: string;
  createdAt?: string;
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
}

interface ApiResponse<T> {
  status: string;
  message: string;
  data: T;
  pagination?: Pagination;
}

/**
 * Create NGO (multipart/form-data)
 */
export async function createNgo(
  formData: FormData,
  token: string,
): Promise<INgo> {
  console.log("Form data type:", typeof formData);
  console.log("Form data:", formData);

  return await request<INgo>("/ngos/create", "POST", formData, token);
}

/**
 * Create NGO with admin (multipart/form-data)
 */
export async function createOrganizationWithAdmin(
  formData: FormData,
  token: string,
): Promise<{
  message: string;
  accessToken: string;
  refreshToken: string;
  user: any;
  organization: INgo;
}> {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/api/ngos/create-with-admin`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    },
  );

  if (!res.ok) {
    const error = await res.json().catch(() => ({}));
    throw new Error(
      error.message || `Request failed with status ${res.status}`,
    );
  }

  return res.json();
}

/**
 * Update NGO (multipart/form-data)
 */
export async function updateNgo(
  id: string,
  formData: FormData,
  token: string,
): Promise<INgo> {
  return await request<INgo>("/ngos/update", "PUT", formData, token);
}

/**
 * Change approvement stage
 */
export async function changeAppovementStage(
  id: string,
  token: string,
  rejectionReason?: string,
): Promise<ApiResponse<null>> {
  return request<ApiResponse<null>>(
    `/ngos/change-stage/${id}`,
    "PATCH",
    rejectionReason ? { rejectionReason } : undefined,
    token,
  );
}

/**
 * Get NGOs by approvement stage
 */
export async function getNGOsByApprovementStage(
  approvementStage: string,
  token: string,
): Promise<ApiResponse<INgo[]>> {
  return request<ApiResponse<INgo[]>>(
    `/ngos/approvement-stage/${approvementStage}`,
    "GET",
    undefined,
    token,
  );
}

/**
 * Get all NGOs (with filters & pagination)
 */
export async function getAllNGOs(
  params: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
    approvedStatus?: string;
    locationsOfOperation?: string;
  },
  token: string,
): Promise<ApiResponse<INgo[]>> {
  const query = new URLSearchParams(
    params as Record<string, string>,
  ).toString();
  return request<ApiResponse<INgo[]>>(
    `/ngos?${query}`,
    "GET",
    undefined,
    token,
  );
}

/**
 * Get NGO by ID
 */
export async function getNGOById(
  id: string,
  token: string,
): Promise<ApiResponse<INgo>> {
  return request<ApiResponse<INgo>>(`/ngos/${id}`, "GET", undefined, token);
}

/**
 * Search NGOs
 */
export async function searchNGOs(
  params: {
    name?: string;
    initials?: string;
    chiefExecutiveName?: string;
    page?: number;
    limit?: number;
  },
  token: string,
): Promise<ApiResponse<INgo[]>> {
  const query = new URLSearchParams(
    params as Record<string, string>,
  ).toString();
  return request<ApiResponse<INgo[]>>(
    `/ngos/search?${query}`,
    "GET",
    undefined,
    token,
  );
}
