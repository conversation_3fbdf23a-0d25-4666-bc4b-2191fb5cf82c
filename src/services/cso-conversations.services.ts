// services/cso-conversation.service.ts
import request from "./api";

// ---------- Types ----------
export interface CsoConversationMessageComment {
  sender: string;
  text: string;
  createdAt: string;
}

export interface CsoConversationMessage {
  _id: string;
  sender: string;
  text: string;
  createdAt: string;
  comments: CsoConversationMessageComment[];
}

export interface CsoConversation {
  _id: string;
  title: string;
  csoId: { _id: string; name: string };
  participants: { _id: string; name: string }[];
  messages: CsoConversationMessage[];
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  conversation?: T;
  conversations?: T[];
  updatedMessage?: any;
}

// ---------- Service Functions ----------

// Get all CSO conversations
export async function getAllCsoConversations(
  token: string,
): Promise<ApiResponse<CsoConversation[]>> {
  return request("/csoconversations", "GET", undefined, token);
}

// Get a single CSO conversation by ID
export async function getCsoConversationById(
  conversationId: string,
  token: string,
): Promise<ApiResponse<CsoConversation>> {
  return request(
    `/csoconversations/${conversationId}`,
    "GET",
    undefined,
    token,
  );
}

// Create a new CSO conversation
export interface CreateCsoConversationDto {
  csoId: string;
  title: string;
  initialMessage?: string;
  participants?: string[];
}
export async function createCsoConversation(
  data: CreateCsoConversationDto,
  token: string,
): Promise<ApiResponse<CsoConversation>> {
  return request("/csoconversations", "POST", data, token);
}

// Add comment to a message
export interface AddCommentDto {
  text: string;
}
export async function addCommentToCsoMessage(
  conversationId: string,
  messageId: string,
  data: AddCommentDto,
  token: string,
): Promise<ApiResponse> {
  return request(
    `/csoconversations/${conversationId}/messages/${messageId}/comments`,
    "POST",
    data,
    token,
  );
}

// Update conversation title or participants
export interface UpdateCsoConversationDto {
  title?: string;
  participants?: string[];
}
export async function updateCsoConversation(
  conversationId: string,
  data: UpdateCsoConversationDto,
  token: string,
): Promise<ApiResponse<CsoConversation>> {
  return request(`/csoconversations/${conversationId}`, "PUT", data, token);
}

// Delete a conversation
export async function deleteCsoConversation(
  conversationId: string,
  token: string,
): Promise<ApiResponse> {
  return request(
    `/csoconversations/${conversationId}`,
    "DELETE",
    undefined,
    token,
  );
}
