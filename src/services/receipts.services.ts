import request from "./api";

// --- Interfaces ---
export interface IReceipt {
  _id: string;
  type: "processing fee" | "registration fee" | "subscription fee";
  receiptUrl: string;
  receiptNumber: string;
  ngo: {
    // _id: string;
    name: string;
  };
  amount: number;
  issueDate: Date;
  createdBy: string;
}

export interface ReceiptsResponse<T> {
  status: string;
  message: string;
  data: T[];
}

// ---------- Helper Functions ----------
function queryGenerator(filter: any = {}): string | undefined {
  const params = new URLSearchParams();
  if (filter.page) params.append("page", filter.page.toString());
  if (filter.limit) params.append("limit", filter.limit.toString());
  return params.toString();
}

// --- Services ---

/**
 * Get all receipts
 */
export const getReceipts = async (
  filter: any = { page: 1, limit: 10 },
  token: string,
): Promise<ReceiptsResponse<IReceipt>> => {
  try {
    const response = await request<ReceiptsResponse<IReceipt>>(
      `/receipts?${queryGenerator(filter)}`,
      "GET",
      undefined,
      token,
    );
    return response;
  } catch (error) {
    throw new Error("Failed to fetch receipts");
  }
};

/**
 * Get a receipt by ID
 */
export const getReceiptById = async (
  id: string,
): Promise<ReceiptsResponse<IReceipt>> => {
  try {
    const response = await request<ReceiptsResponse<IReceipt>>(
      `/receipts/${id}`,
    );
    return response;
  } catch (error) {
    throw new Error(`Failed to fetch receipt with ID ${id}`);
  }
};

/**
 *  Get receipt by ngo ID
 */
export async function getByNgoId(
  ngoId: string,
): Promise<ReceiptsResponse<IReceipt>> {
  try {
    const response = await request<ReceiptsResponse<IReceipt>>(
      `/receipts/ngo/${ngoId}`,
    );
    return response;
  } catch (error) {
    throw new Error(`Failed to fetch receipts for NGO with ID ${ngoId}`);
  }
}
