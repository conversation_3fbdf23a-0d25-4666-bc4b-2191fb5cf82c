// Types for Staff Registry Statistics
export interface TotalNgoApplicants {
  currentYear: number;
  currentWeek: number;
}

export interface NgoStatus {
  pending: number;
  approved: number;
  rejected: number;
}

export interface NgoTypes {
  local: number;
  international: number;
}

export interface RecentActivity {
  action: string;
  description: string;
  timeAgo: string;
}

export interface RegistrySnapshot {
  name: string;
  status: string;
  registrationDate: string;
  lastActionDate: string;
}

export interface TopCommunicationThread {
  title: string;
  group: string;
  type: string;
  lastActivity: string;
  participants: number;
  messages: number;
}

export interface StaffRegistryStatistics {
  totalNgoApplicants: TotalNgoApplicants;
  ngoStatus: NgoStatus;
  dormantNgos: number;
  ngoTypes: NgoTypes;
  recentActivity: RecentActivity[];
  registrySnapshot: RegistrySnapshot[];
  topCommunicationThreads: TopCommunicationThread[];
  lastUpdated: string;
}
