export interface StaffAdminStatistics {
  newApplicantsThisWeek: number;
  pendingNgos: number;
  approvedNgos: number;
  rejectedNgos: number;
  inactiveNgos: number;
  localNgos: number;
  internationalNgos: number;
  totalNgos: number;
  activeNgos: number;
  percentages: {
    pendingPercentage: string;
    approvedPercentage: string;
    rejectedPercentage: string;
    inactivePercentage: string;
    localPercentage: string;
    internationalPercentage: string;
  };
  recentNgos: {
    id: string;
    name: string;
    approvedStatus: string;
    type: string;
    status: string;
    registrationNumber: string;
    createdAt: string;
    lastUpdated: string;
    daysSinceCreation: number;
    daysSinceUpdate: number;
  }[];
  recentAuditLogs: {
    id: string;
    userName: string;
    userEmail: string;
    userRole: string;
    action: string;
    details: string;
    status: string;
    timeAgo: string;
    exactTime: string;
    ipAddress: string;
    actionFormatted: string;
  }[];
  expiringCertificates: {
    total: number;
    list: any[];
    byUrgency: {
      critical: any[];
      high: any[];
      medium: any[];
      low: any[];
    };
    counts: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    };
  };
  lastUpdated: string;
  dateRange: {
    newApplicantsFrom: string;
    newApplicantsTo: string;
  };
}
