// Types for Finance Officer Statistics
export interface Revenue {
  total: number;
  local: number;
  international: number;
}

export interface Ratio {
  local: number;
  international: number;
}

export interface MonthlyRevenue {
  month: string;
  revenue: number;
}

export interface RecentPayment {
  ngoName: string;
  receiptNumber: string;
  amount: number;
  date: string;
}

export interface FinanceOfficerStatistics {
  revenue: Revenue;
  ratio: Ratio;
  monthlyRevenue: MonthlyRevenue[];
  recentPayments: RecentPayment[];
}

interface ApiResponse<T> {
  status: "success" | "error";
  message: string;
  data?: T;
}
