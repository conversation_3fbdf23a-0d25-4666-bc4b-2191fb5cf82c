export interface User {
  _id: string;
  fullname: string;
  email: string;
  role: string;
  assignedRoles: string[];
  ngoId: string | null;
  status: string;
  emailVerified: boolean;
  lastEmailSent: string | null;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export type NGOType = "international" | "local";

export type ApprovalStatus = "pending" | "approved" | "rejected";

export type ApprovementStage = "financial" | "documentation" | "completed";

export type EntityStatus = "active" | "inactive";

export type Sectors = {
  _id: string;
  name: string;
  description: string;
  organizationCount: number;
  status: EntityStatus;
};

export type LocationsOfOperation = {
  _id: string;
  region: string;
  district: string;
};

export interface NGO {
  _id: string;
  user: User;
  type: NGOType;
  name: string;
  initials: string;
  headquartersAddress: string;
  dateFounded: string;
  dateApprovedByGOM: string;
  chairpersonName: string;
  chairpersonEmail: string;
  chairpersonPhone: string;
  chairpersonAddress: string;
  chiefExecutiveName: string;
  chiefExecutiveEmail: string;
  chiefExecutivePhone: string;
  chiefExecutiveFax: string;
  chiefExecutiveAddress: string;
  statementAgreement: boolean;
  physicalOfficesAddress: string;
  ngoBackground: string;
  missionStatement: string;
  visionStatement: string;
  valuesStatement: string;
  locationsOfOperation: LocationsOfOperation[];
  sectorsOfOperation: Sectors[];
  goals: string;
  numberOfEmployees: number;
  constitutionUrl: string;
  minutesOfFirstMeetingUrl: string;
  certificateFromRegistrarGeneralUrl: string;
  swornInAffidavitUrl: string;
  registrationFeeUrl: string;
  processingFeeUrl: string;
  approvedStatus: ApprovalStatus;
  approvementStage: ApprovementStage;
  status: EntityStatus;
  joinCommunity: boolean;
  refToken: string;
  __v: number;
}
