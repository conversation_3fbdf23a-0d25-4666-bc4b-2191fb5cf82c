// Types for Super Admin Statistics
export interface Growth {
  total: number;
  growthInCurrentYear: number;
  growthRatePercentage: string;
}

export interface StatusDistributionItem {
  count: number;
  percentage: string;
}

export interface StatusDistribution {
  pending: StatusDistributionItem;
  approved: StatusDistributionItem;
  rejected: StatusDistributionItem;
}

export interface MonthlyGrowth {
  month: string;
  year: number;
  registeredNgos: number;
  growthRatePercentage: string;
}

export interface AreaOfFocus {
  name: string;
  totalNgos: number;
  growthInCurrentYear: number;
  growthRatePercentage: string;
}

export interface NgosStatistics {
  total: number;
  growthInCurrentYear: number;
  growthRatePercentage: string;
  statusDistribution: StatusDistribution;
  monthlyGrowth: MonthlyGrowth[];
  areaOfFocus: AreaOfFocus[];
}

export interface DateRange {
  from: string;
  to: string;
}

export interface SuperAdminStatistics {
  totalNetworks: Growth;
  sectorNetworks: Growth;
  districtNetworks: Growth;
  activeMembers: Growth;
  ngos: NgosStatistics;
  users: Growth;
  lastUpdated: string;
  dateRange: DateRange;
}
