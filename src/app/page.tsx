"use client";

import { useState } from "react";
import Image from "next/image";
import LoginPopup from "@/components/login/LoginPopup";
import RegisterPopup from "@/components/register/RegisterPopup";

export default function Home() {
  const [email, setEmail] = useState("");
  const [subscribed, setSubscribed] = useState(false);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [showRegisterPopup, setShowRegisterPopup] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [activeFaq, setActiveFaq] = useState<number | null>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showDocumentChecklist, setShowDocumentChecklist] = useState(false);
  const [showMembershipFees, setShowMembershipFees] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Submitted email:", email);
    setSubscribed(true);
    setEmail("");
    setTimeout(() => setSubscribed(false), 5000);
  };

  const handleAuthClick = () => {
    setShowLoginPopup(true);
    setMobileMenuOpen(false);
  };

  const handleRegisterClick = () => {
    setShowRegisterPopup(true);
    setMobileMenuOpen(false);
  };

  const toggleFaq = (index: number) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const toggleDocumentChecklist = () => {
    setShowDocumentChecklist(!showDocumentChecklist);
  };

  const toggleMembershipFees = () => {
    setShowMembershipFees(!showMembershipFees);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100">
      {/* Navigation */}
      <header className="sticky top-0 z-50 bg-white/95 shadow-sm backdrop-blur-sm">
        <nav className="container mx-auto px-4 py-4 sm:px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-xl font-bold text-blue-600 sm:text-2xl">
              <Image
                src="/images/logo/icon.png"
                alt="CONGOMA Logo"
                width={40}
                height={40}
                className="mr-3 h-8 w-8 object-contain sm:h-10 sm:w-10"
              />
            </div>
            <div className="hidden space-x-6 md:flex md:space-x-8">
              <a
                href="#about"
                className="text-gray-700 transition-colors duration-200 hover:text-blue-600 hover:underline hover:underline-offset-8"
              >
                About Us
              </a>
              <a
                href="#registration"
                className="text-gray-700 transition-colors duration-200 hover:text-blue-600 hover:underline hover:underline-offset-8"
              >
                How to Register
              </a>
              <a
                href="#demo"
                className="text-gray-700 transition-colors duration-200 hover:text-blue-600 hover:underline hover:underline-offset-8"
              >
                Platform Demo
              </a>
              <a
                href="#faqs"
                className="text-gray-700 transition-colors duration-200 hover:text-blue-600 hover:underline hover:underline-offset-8"
              >
                FAQs
              </a>
              <a
                href="#contact"
                className="text-gray-700 transition-colors duration-200 hover:text-blue-600 hover:underline hover:underline-offset-8"
              >
                Contact Us
              </a>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleRegisterClick}
                className="hidden rounded border-2 border-blue-600 bg-transparent px-4 py-2 text-sm text-blue-600 transition-all duration-300 hover:bg-blue-50 hover:shadow-lg sm:block sm:px-6 sm:text-base"
              >
                Sign Up
              </button>
              <button
                onClick={handleAuthClick}
                className="hidden rounded bg-gradient-to-r from-blue-600 to-blue-500 px-4 py-2 text-sm text-white transition-all duration-300 hover:from-blue-700 hover:to-blue-700 hover:shadow-lg sm:block sm:px-6 sm:text-base"
              >
                Sign In
              </button>
              <button
                onClick={toggleMobileMenu}
                className="ml-4 text-gray-700 focus:outline-none md:hidden"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  {mobileMenuOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile menu */}
          {mobileMenuOpen && (
            <div className="mt-4 space-y-2 pb-4 md:hidden">
              <a
                href="#about"
                className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                About Us
              </a>
              <a
                href="#registration"
                className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                How to Register
              </a>
              <a
                href="#demo"
                className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                Platform Demo
              </a>
              <a
                href="#faqs"
                className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                FAQs
              </a>
              <a
                href="#contact"
                className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                onClick={() => setMobileMenuOpen(false)}
              >
                Contact Us
              </a>
              <button
                onClick={handleRegisterClick}
                className="w-full rounded border-2 border-blue-600 bg-transparent px-4 py-2 text-sm font-medium text-blue-600 transition-all duration-300 hover:bg-blue-50 hover:shadow-lg"
              >
                Sign Up
              </button>
              <button
                onClick={handleAuthClick}
                className="w-full rounded bg-gradient-to-r from-blue-600 to-blue-500 px-4 py-2 text-sm font-medium text-white transition-all duration-300 hover:from-blue-700 hover:to-blue-700 hover:shadow-lg"
              >
                Sign In
              </button>
            </div>
          )}
        </nav>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-12 sm:px-6 sm:py-16 md:py-24">
        <div className="flex flex-col items-center md:flex-row">
          <div className="mb-8 md:mb-0 md:w-1/2">
            <h1 className="mb-4 text-3xl font-bold leading-tight text-gray-800 sm:text-4xl md:text-5xl lg:text-6xl">
              Uniting and Strengthening{" "}
              <span className="bg-gradient-to-r from-blue-600 to-blue-500 bg-clip-text text-transparent">
                Malawi&apos;s NGO
              </span>{" "}
              Through Digital Transformation
            </h1>
            <p className="mb-6 text-base text-gray-600 sm:text-lg">
              The CONGOMA Information Management System (IMS) has been designed
              to digitise and streamline the CONGOMA membership registration,
              compliance, and communication processes for both local and
              international NGOs operating in Malawi. Experience efficiency,
              transparency, and seamless collaboration.
            </p>
            <div className="flex flex-col gap-3 sm:flex-row sm:gap-4">
              <button
                onClick={handleAuthClick}
                className="rounded bg-gradient-to-r from-blue-600 to-blue-500 px-6 py-2 text-sm font-medium text-white transition-all hover:from-blue-700 hover:to-blue-700 hover:shadow-lg sm:px-8 sm:py-3 sm:text-base"
              >
                Become a Member
              </button>
              <button className="rounded border-2 border-blue-600 px-6 py-2 text-sm font-medium text-blue-600 transition-all hover:bg-blue-50 hover:shadow-md sm:px-8 sm:py-3 sm:text-base">
                Learn About NGOs in Malawi
              </button>
            </div>
          </div>
          <div className="mt-8 w-full md:mt-0 md:w-1/2">
            <div className="relative w-full overflow-hidden rounded-xl shadow-2xl sm:rounded-2xl">
              <Image
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f"
                alt="Team working together"
                width={800}
                height={600}
                className="h-auto w-full object-cover"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-blue-900/30 to-blue-600/20"></div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="bg-white py-12 sm:py-16">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center">
            <span className="mb-3 inline-block rounded-full bg-blue-100 px-3 py-1 text-xs font-semibold text-blue-600 sm:px-4 sm:text-sm">
              ABOUT US
            </span>
            <h2 className="mb-8 text-2xl font-bold text-gray-800 sm:mb-12 sm:text-3xl md:text-4xl">
              About <span className="text-blue-600">CONGOMA</span>
            </h2>
          </div>
          <div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 md:gap-12">
            <div>
              {/* Who We Are */}
              <h3 className="mb-3 text-xl font-semibold text-gray-800 sm:text-2xl">
                Who We Are
              </h3>
              <p className="mb-4 text-base text-gray-600 sm:text-lg">
                The Council for Non-Governmental Organisations in Malawi
                (CONGOMA) is the legally mandated coordinating body for both
                local and international NGOs operating in Malawi. Established in
                1985 and operating under the NGO Act, CONGOMA serves as the
                primary platform for networking, information sharing, advocacy,
                and collective action for the NGO sector.
              </p>
              <p className="mb-6 text-base text-gray-600 sm:text-lg">
                With a membership base of over 1,500 NGOs, CONGOMA continues to lead efforts in
                unifying the sector, strengthening good governance, and
                promoting impactful development initiatives across the country.
              </p>

              {/* Our Mission */}
              <h3 className="mb-3 text-xl font-semibold text-gray-800 sm:text-2xl">
                Our Mission
              </h3>
              <p className="mb-6 text-base text-gray-600 sm:text-lg">
                To create an enabling environment where NGOs can operate effectively and efficiently, contributing to
                the sustainable development of Malawi.
              </p>

              {/* What We Do */}
              <h3 className="mb-3 text-xl font-semibold text-gray-800 sm:text-2xl">
                What We Do
              </h3>
              <p className="mb-4 text-base text-gray-600 sm:text-lg">
                We are a designated NGO coordinating body in Malawi responsible for membership:
              </p>
              <ul className="space-y-3">
                {[
                  "Coordination: Building strong networks and partnerships within and across sectors.",
                  "Capacity Strengthening: Equipping members with tools, knowledge, and resources for effective operations.",
                  "Advocacy: Influencing policy reforms within and outside the NGO Sector and Promoting transparency, ethical standards, and compliance through digital innovation.",
                  "Representation: Serving the interests for NGOs at national and international forums.",
                  "Communication and Fundraising: Sharing information and identifying funding opportunities.",
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2 mt-1 flex h-4 w-4 items-center justify-center rounded-full bg-blue-100 text-blue-600 sm:mr-3 sm:h-5 sm:w-5">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-2 w-2 sm:h-3 sm:w-3"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </span>
                    <span className="text-sm text-gray-700 sm:text-base">
                      {item}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
            <div className="relative mt-8 md:mt-0">
              <div className="relative overflow-hidden rounded-xl shadow-2xl sm:rounded-2xl">
                <Image
                  src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d"
                  alt="Team meeting"
                  width={800}
                  height={600}
                  className="h-auto w-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 to-blue-600/30"></div>
              </div>
              <div className="absolute -bottom-4 -right-4 hidden h-32 w-32 rounded-xl bg-blue-600/10 sm:block sm:h-40 sm:w-40 sm:rounded-2xl"></div>
              <div className="absolute -left-4 -top-4 hidden h-24 w-24 rounded-full bg-blue-500/10 sm:block sm:h-32 sm:w-32"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="bg-white py-12 sm:py-16">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center">
            <span className="mb-3 inline-block rounded-full bg-blue-100 px-3 py-1 text-xs font-semibold text-blue-600 sm:px-4 sm:text-sm">
              KEY FEATURES
            </span>
            <h2 className="mb-4 text-2xl font-bold text-gray-800 sm:text-3xl md:text-4xl">
              A Platform Built for <span className="text-blue-600">NGO Success</span>
            </h2>
            <p className="mx-auto mb-8 max-w-3xl text-base text-gray-600 sm:mb-12 sm:text-lg">
              Discover the powerful features designed to streamline your NGO operations, 
              enhance collaboration, and ensure compliance with CONGOMA requirements.
            </p>
          </div>

          <div className="mx-auto max-w-7xl">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {/* Feature 1: Self-Service Member Dashboard */}
              <div className="group rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 sm:p-8">
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-blue-600 text-white shadow-lg sm:h-14 sm:w-14">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 sm:h-7 sm:w-7"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-lg font-semibold text-gray-800 sm:text-xl">
                  Self-Service Member Dashboard
                </h3>
                <p className="text-sm text-gray-600 sm:text-base">
                  Take control of your organisation&apos;s information. Securely log in to your personalised dashboard to update your profile, manage compliance documents, view payment history, and download official receipts and membership certificates anytime.
                </p>
              </div>

              {/* Feature 2: Integrated & Secure Payments */}
              <div className="group rounded-2xl bg-gradient-to-br from-green-50 to-green-100 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 sm:p-8">
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-green-600 text-white shadow-lg sm:h-14 sm:w-14">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 sm:h-7 sm:w-7"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-lg font-semibold text-gray-800 sm:text-xl">
                  Integrated & Secure Payments
                </h3>
                <p className="text-sm text-gray-600 sm:text-base">
                  Handle all financial obligations with ease. The system features automated invoicing for fees and subscriptions. Pay via bank transfer with a simple receipt upload, with future integrations planned for mobile money and credit/debit cards.
                </p>
              </div>

              {/* Feature 3: Real-Time Communication & Alerts */}
              <div className="group rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 sm:p-8">
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-purple-600 text-white shadow-lg sm:h-14 sm:w-14">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 sm:h-7 sm:w-7"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-lg font-semibold text-gray-800 sm:text-xl">
                  Real-Time Communication & Alerts
                </h3>
                <p className="text-sm text-gray-600 sm:text-base">
                  Stay informed with instant notifications. Receive automated updates on your application status, payment reminders, and important announcements from CONGOMA via in-platform alerts, email, and SMS.
                </p>
              </div>

              {/* Feature 4: Robust Document Management */}
              <div className="group rounded-2xl bg-gradient-to-br from-orange-50 to-orange-100 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 sm:p-8">
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-orange-600 text-white shadow-lg sm:h-14 sm:w-14">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 sm:h-7 sm:w-7"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-lg font-semibold text-gray-800 sm:text-xl">
                  Robust Document Management
                </h3>
                <p className="text-sm text-gray-600 sm:text-base">
                  Say goodbye to physical paperwork. Our secure portal allows you to upload, store, and manage all essential documents, from governance charters to annual reports. Approved documents are locked to ensure their integrity throughout the review process.
                </p>
              </div>

              {/* Feature 5: Enhanced Community Collaboration */}
              <div className="group rounded-2xl bg-gradient-to-br from-teal-50 to-teal-100 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 sm:p-8">
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-teal-600 text-white shadow-lg sm:h-14 sm:w-14">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 sm:h-7 sm:w-7"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-lg font-semibold text-gray-800 sm:text-xl">
                  Enhanced Community Collaboration
                </h3>
                <p className="text-sm text-gray-600 sm:text-base">
                  Connect and network with your peers. The IMS features community channels that group NGOs by operational sector (e.g., Health, Education) and District of operation to foster collaboration, share opportunities, and discuss challenges.
                </p>
              </div>

              {/* Feature 6: Accessible Anytime, Anywhere */}
              <div className="group rounded-2xl bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 sm:p-8">
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-indigo-600 text-white shadow-lg sm:h-14 sm:w-14">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 sm:h-7 sm:w-7"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-lg font-semibold text-gray-800 sm:text-xl">
                  Accessible Anytime, Anywhere
                </h3>
                <p className="text-sm text-gray-600 sm:text-base">
                  The IMS is designed as a fully responsive Progressive Web App (PWA). Optimised for low-bandwidth environments, it ensures you can access the platform from any device, whether you are in the city or a rural district.
                </p>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-12 text-center sm:mt-16">
              <div className="rounded-2xl bg-gradient-to-r from-blue-600 to-blue-500 p-8 text-white shadow-2xl sm:p-12">
                <h3 className="mb-4 text-xl font-bold sm:text-2xl">
                  Ready to Experience These Features?
                </h3>
                <p className="mb-6 text-base opacity-90 sm:text-lg">
                  Join over 1,500 NGOs already using the CONGOMA platform to streamline their operations and maximize their impact.
                </p>
                <button
                  onClick={handleRegisterClick}
                  className="rounded-lg bg-white px-8 py-3 text-sm font-medium text-blue-600 shadow-lg transition-all hover:bg-gray-50 hover:shadow-xl sm:px-10 sm:py-4 sm:text-lg"
                >
                  Start Your Registration Today
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Registration Steps Section */}
      <section
        id="registration"
        className="bg-gradient-to-br from-blue-50 to-blue-50 py-12 sm:py-16"
      >
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center">
            <span className="mb-3 inline-block rounded bg-blue-100 px-3 py-1 text-xs font-semibold text-blue-600 sm:px-4 sm:text-sm">
              JOIN US
            </span>
            <h2 className="mb-4 text-2xl font-bold text-gray-800 sm:text-3xl md:text-4xl">
              A Simple Path to Registration: How It Works
            </h2>
            <p className="mx-auto mb-8 max-w-2xl text-base text-gray-600 sm:mb-12 sm:text-lg">
              Follow our guided, step-by-step process to get your Organisation
              registered and recognised.
            </p>
          </div>

          <div className="mx-auto max-w-5xl">
            {/* Vertical Stepper */}
            <div className="relative">
              {/* Vertical line */}
              <div className="absolute left-4 h-full w-0.5 bg-blue-200 sm:left-8 md:left-1/2"></div>

              {/* Steps */}
              {[
                {
                  title: "Prepare Your Documents",
                  description:
                    "Gather all required documents, including your NGO's constitution, proof of registration with the government, minutes of the first meeting or the minutes of the meeting at which it was agreed to register the NGO, and sworn-in affidavits of the board of trustees/directors.",
                  action: "Show document checklist",
                },
                {
                  title: "Create a Representative account",
                  description:
                    "Create a Contact person/Representative account with CONGOMA. If the NGO is already registered with the CONGOMA, then use the details that were provided by CONGOMA.",
                  action: "Start Registration",
                },
                {
                  title: "Create Your Account",
                  description:
                    "After all requirements in step 2 are met, start your registration by providing basic information about your organisation and creating login credentials.",
                  action: "View Application Form",
                },
                {
                  title: "Complete Application",
                  description:
                    "Fill out the detailed application form with information about your NGO's mission, activities, governance, and source of funding.",
                },
                {
                  title: "Review & Approval",
                  description:
                    "Our team will review your application within 3-5 working days. You may be contacted for clarity.",
                  action: "Learn About Review Process",
                },
                {
                  title: "Payment & Activation",
                  description:
                    "Pay the processing and annual membership fee based on your organisation's category (Local or International). Once confirmed, you will become a bonafide member of CONGOMA.",
                  action: "View membership fees",
                },
              ].map((step, index) => (
                <div
                  key={index}
                  className={`relative mb-8 flex flex-col items-center transition-all duration-300 hover:scale-[1.01] sm:mb-12 md:flex-row ${index % 2 === 0 ? "md:justify-start" : "md:justify-end"}`}
                  onMouseEnter={() => setActiveStep(index)}
                >
                  {/* Step indicator */}
                  <div
                    className={`absolute left-0 z-10 flex h-12 w-12 items-center justify-center rounded-full border-4 border-white text-xl font-bold shadow-lg transition-all duration-300 sm:h-16 sm:w-16 sm:text-2xl md:left-1/2 md:-ml-8 ${activeStep === index ? "scale-110 bg-blue-600 text-white" : "bg-white text-blue-600"}`}
                  >
                    {index + 1}
                  </div>

                  {/* Step content */}
                  <div
                    className={`mt-6 w-full rounded-xl bg-white p-6 shadow-lg transition-all duration-300 sm:mt-8 sm:rounded-2xl sm:p-8 md:mt-0 md:w-5/12 ${index % 2 === 0 ? "md:ml-6 md:mr-auto" : "md:ml-auto md:mr-6"} ${activeStep === index ? "border-l-4 border-blue-600" : ""}`}
                  >
                    <h3
                      className={`mb-2 text-lg font-semibold sm:mb-3 sm:text-xl ${activeStep === index ? "text-blue-600" : "text-gray-800"}`}
                    >
                      {step.title}
                    </h3>
                    <p className="mb-3 text-sm text-gray-600 sm:text-base">
                      {step.description}
                    </p>
                    <button
                      onClick={index === 0 ? toggleDocumentChecklist : index === 1 ? handleRegisterClick : index === 5 ? toggleMembershipFees : undefined}
                      className={`text-xs font-medium sm:text-sm ${activeStep === index ? "text-blue-600 hover:text-blue-800" : "text-gray-500 hover:text-gray-700"}`}
                    >
                      {step.action} →
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Membership Fees Modal */}
            {showMembershipFees && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                <div className="relative max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-2xl bg-white shadow-2xl">
                  <div className="sticky top-0 bg-white px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-bold text-gray-800 sm:text-2xl">
                        CONGOMA Membership Fees
                      </h3>
                      <button
                        onClick={toggleMembershipFees}
                        className="flex h-8 w-8 items-center justify-center rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    {/* Local NGOs Section */}
                    <div className="mb-8">
                      <h4 className="mb-4 flex items-center text-lg font-semibold text-green-600">
                        <span className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-green-100 text-sm">
                          🏠
                        </span>
                        LOCAL NGOs
                      </h4>
                      
                      {/* First-time registration */}
                      <div className="mb-6 rounded-lg border border-green-200 bg-green-50 p-4">
                        <h5 className="mb-3 font-semibold text-green-800">First-time registration</h5>
                        <ul className="space-y-2">
                          <li className="flex justify-between items-center">
                            <span className="text-sm text-gray-700">Processing fee</span>
                            <span className="font-semibold text-green-700">MWK15,500</span>
                          </li>
                          <li className="flex justify-between items-center">
                            <span className="text-sm text-gray-700">Annual Subscription fee</span>
                            <span className="font-semibold text-green-700">MWK120,750</span>
                          </li>
                          <li className="flex justify-between items-center border-t border-green-200 pt-2 mt-2">
                            <span className="font-medium text-green-800">Total</span>
                            <span className="font-bold text-green-800">MWK136,250</span>
                          </li>
                        </ul>
                      </div>

                      {/* Established Local NGOs */}
                      <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                        <h5 className="mb-3 font-semibold text-green-800">Established Local NGOs</h5>
                        <ul className="space-y-2">
                          <li className="flex justify-between items-center">
                            <span className="text-sm text-gray-700">Annual Subscription fee</span>
                            <span className="font-semibold text-green-700">MWK241,500</span>
                          </li>
                        </ul>
                      </div>
                    </div>

                    {/* International NGOs Section */}
                    <div className="mb-6">
                      <h4 className="mb-4 flex items-center text-lg font-semibold text-blue-600">
                        <span className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-sm">
                          🌍
                        </span>
                        INTERNATIONAL NGOs
                      </h4>
                      
                      {/* First-time registration */}
                      <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                        <h5 className="mb-3 font-semibold text-blue-800">First-time registration</h5>
                        <ul className="space-y-2">
                          <li className="flex justify-between items-center">
                            <span className="text-sm text-gray-700">Processing fee</span>
                            <span className="font-semibold text-blue-700">MWK35,250</span>
                          </li>
                          <li className="flex justify-between items-center">
                            <span className="text-sm text-gray-700">Annual Subscription fee</span>
                            <span className="font-semibold text-blue-700">MWK402,500</span>
                          </li>
                          <li className="flex justify-between items-center border-t border-blue-200 pt-2 mt-2">
                            <span className="font-medium text-blue-800">Total</span>
                            <span className="font-bold text-blue-800">MWK437,750</span>
                          </li>
                        </ul>
                      </div>
                    </div>

                    {/* Note */}
                    <div className="mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                      <div className="flex items-start">
                        <span className="mr-2 flex h-5 w-5 items-center justify-center rounded-full bg-yellow-200 text-yellow-800 text-xs">
                          ℹ
                        </span>
                        <div>
                          <p className="text-sm text-yellow-800">
                            <strong>Note:</strong> All fees are in Malawian Kwacha (MWK). Payment methods and additional details will be provided during the registration process.
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col gap-3 border-t border-gray-200 pt-6 sm:flex-row">
                      <button
                        onClick={handleRegisterClick}
                        className="flex-1 rounded-lg bg-gradient-to-r from-blue-600 to-blue-500 px-6 py-3 text-sm font-medium text-white transition-all hover:from-blue-700 hover:to-blue-700 hover:shadow-lg"
                      >
                        Start Registration Process
                      </button>
                      <button
                        onClick={toggleMembershipFees}
                        className="flex-1 rounded-lg border-2 border-gray-300 px-6 py-3 text-sm font-medium text-gray-700 transition-all hover:border-gray-400 hover:bg-gray-50"
                      >
                        Close Fee Information
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Document Checklist Modal */}
            {showDocumentChecklist && (
              <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                <div className="relative max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-2xl bg-white shadow-2xl">
                  <div className="sticky top-0 bg-white px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-bold text-gray-800 sm:text-2xl">
                        Document Checklist for NGO Registration
                      </h3>
                      <button
                        onClick={toggleDocumentChecklist}
                        className="flex h-8 w-8 items-center justify-center rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    {/* For All NGOs Section */}
                    <div className="mb-8">
                      <h4 className="mb-4 flex items-center text-lg font-semibold text-blue-600">
                        <span className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-sm">
                          ✓
                        </span>
                        For All NGOs
                      </h4>
                      <ul className="space-y-3">
                        {[
                          "A certified copy of the NGO's constitution or a Governing instrument of the NGO.",
                          "A copy of the minutes of the first meeting at which it was agreed to establish or register an NGO",
                          "A copy of the certificate of Incorporation from the Registrar General.",
                          "Sworn-in Affidavits of Trustees/Directors giving their names, citizenship, occupation postal address and their passport sized photos.",
                          "Proof of payment (slip/receipt) for the processing fee and annual membership fee"
                        ].map((item, index) => (
                          <li key={index} className="flex items-start">
                            <span className="mr-3 mt-1 flex h-4 w-4 items-center justify-center rounded-full bg-green-100 text-green-600">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-2.5 w-2.5"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </span>
                            <span className="text-sm text-gray-700">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Additional for International NGOs Section */}
                    <div className="mb-6">
                      <h4 className="mb-4 flex items-center text-lg font-semibold text-orange-600">
                        <span className="mr-2 flex h-6 w-6 items-center justify-center rounded-full bg-orange-100 text-sm">
                          +
                        </span>
                        Additional for International NGOs specifically
                      </h4>
                      <ul className="space-y-3">
                        {[
                          "Certificate of registration from the country of origin",
                          "A letter of approval from the line ministry (if applicable)",
                          "Temporary Employment Permit (TEP) (optional)"
                        ].map((item, index) => (
                          <li key={index} className="flex items-start">
                            <span className="mr-3 mt-1 flex h-4 w-4 items-center justify-center rounded-full bg-orange-100 text-orange-600">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-2.5 w-2.5"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </span>
                            <span className="text-sm text-gray-700">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col gap-3 border-t border-gray-200 pt-6 sm:flex-row">
                      <button
                        onClick={handleRegisterClick}
                        className="flex-1 rounded-lg bg-gradient-to-r from-blue-600 to-blue-500 px-6 py-3 text-sm font-medium text-white transition-all hover:from-blue-700 hover:to-blue-700 hover:shadow-lg"
                      >
                        Start Registration Process
                      </button>
                      <button
                        onClick={toggleDocumentChecklist}
                        className="flex-1 rounded-lg border-2 border-gray-300 px-6 py-3 text-sm font-medium text-gray-700 transition-all hover:border-gray-400 hover:bg-gray-50"
                      >
                        Close Checklist
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="mt-8 text-center sm:mt-12">
              <button
                onClick={handleAuthClick}
                className="rounded bg-gradient-to-r from-blue-600 to-blue-500 px-8 py-3 text-sm font-medium text-white shadow-lg transition-all hover:from-blue-700 hover:to-blue-700 hover:shadow-xl sm:px-10 sm:py-4 sm:text-lg"
              >
                Begin Your Registration Now
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="bg-white py-12 sm:py-16">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center">
            <span className="mb-3 inline-block rounded-full bg-blue-100 px-3 py-1 text-xs font-semibold text-blue-600 sm:px-4 sm:text-sm">
              PLATFORM TOUR
            </span>
            <h2 className="mb-4 text-2xl font-bold text-gray-800 sm:text-3xl md:text-4xl">
              Explore Our{" "}
              <span className="text-blue-600">Membership Platform</span>
            </h2>
            <p className="mx-auto mb-8 max-w-2xl text-base text-gray-600 sm:mb-12 sm:text-lg">
              Discover how our platform can streamline your NGOs&apos;
              operations and connect you with the broader development community
            </p>
          </div>

          <div className="mx-auto max-w-6xl">
            {/* Video Demo */}
            <div className="relative mb-8 overflow-hidden rounded-2xl shadow-2xl sm:mb-12 sm:rounded-3xl">
              <div className="aspect-w-16 aspect-h-9 bg-gradient-to-r from-blue-500 to-blue-500">
                <div className="flex h-full items-center justify-center">
                  <button className="group flex flex-col items-center">
                    <div className="mb-2 flex h-16 w-16 items-center justify-center rounded-full bg-white/90 text-blue-600 shadow-lg transition-all duration-300 group-hover:scale-110 group-hover:bg-white sm:mb-3 sm:h-20 sm:w-20">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8 sm:h-10 sm:w-10"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <span className="text-sm font-medium text-white sm:text-lg">
                      Watch Platform Demo
                    </span>
                  </button>
                </div>
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
            </div>

            {/* Platform Features */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 md:gap-8">
              {[
                {
                  icon: (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-8 w-8 sm:h-10 sm:w-10"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  ),
                  title: "Member Dashboard",
                  description:
                    "Access all your membership information, documents, and communications in one centralized dashboard.",
                },
                {
                  icon: (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-8 w-8 sm:h-10 sm:w-10"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                  ),
                  title: "Document Upload",
                  description:
                    "Securely submit all required documents through our easy-to-use upload system with progress tracking.",
                },
                {
                  icon: (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-8 w-8 sm:h-10 sm:w-10"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  ),
                  title: "Event Registration",
                  description:
                    "Browse and register for upcoming workshops, training sessions, and networking events.",
                },
              ].map((feature, index) => (
                <div
                  key={index}
                  className="group rounded-xl bg-white p-6 shadow-lg transition-all duration-300 hover:translate-y-[-4px] hover:shadow-xl sm:rounded-2xl sm:p-8"
                >
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-blue-100 text-blue-600 transition-all duration-300 group-hover:bg-blue-600 group-hover:text-white sm:mb-5 sm:h-16 sm:w-16 sm:rounded-2xl">
                    {feature.icon}
                  </div>
                  <h3 className="mb-2 text-lg font-semibold text-gray-800 sm:mb-3 sm:text-xl">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-gray-600 sm:text-base">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>

            <div className="mt-8 text-center sm:mt-12">
              <button
                onClick={handleAuthClick}
                className="rounded bg-gradient-to-r from-blue-600 to-blue-500 px-8 py-3 text-sm font-medium text-white shadow-lg transition-all hover:from-blue-700 hover:to-blue-700 hover:shadow-xl sm:px-10 sm:py-4 sm:text-lg"
              >
                Try the Platform Now
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-blue-600 py-12 text-white sm:py-16">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="grid grid-cols-2 gap-4 text-center sm:grid-cols-4 sm:gap-8">
            {[
              { number: "500+", label: "Member NGOs" },
              { number: "35+", label: "Years of Service" },
              { number: "20+", label: "Development Sectors" },
              { number: "100%", label: "Coverage Nationwide" },
            ].map((stat, index) => (
              <div key={index} className="p-2 sm:p-4">
                <div className="mb-1 text-2xl font-bold sm:mb-2 sm:text-3xl md:text-4xl">
                  {stat.number}
                </div>
                <div className="text-xs text-blue-100 sm:text-sm">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQs Section */}
      <section
        id="faqs"
        className="bg-gradient-to-br from-blue-50 to-blue-50 py-12 sm:py-16"
      >
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center">
            <span className="mb-3 inline-block rounded-full bg-blue-100 px-3 py-1 text-xs font-semibold text-blue-600 sm:px-4 sm:text-sm">
              HAVE QUESTIONS?
            </span>
            <h2 className="mb-8 text-2xl font-bold text-gray-800 sm:mb-12 sm:text-3xl md:text-4xl">
              Frequently Asked <span className="text-blue-600">Questions</span>
            </h2>
          </div>

          <div className="mx-auto max-w-4xl">
            {[
              {
                question: "What documents are required for registration?",
                answer:
                  "For local NGOs, you will need your Constitution, minutes of the first meeting, trustee affidavits, and your Certificate of Registration from the Registrar General. The system provides a clear checklist during the application process. International NGOs have additional document requirements.",
              },
              {
                question: "Is my data secure on this platform?",
                answer:
                  "Absolutely. We prioritise your security. All data is protected with SSL/TLS encryption, and sensitive documents are further secured. Administrative accounts require Multi-Factor Authentication (MFA) to ensure controlled access.",
              },
              {
                question:
                  "What if I am already registered with CONGOMA through the old manual system?",
                answer:
                  "We are migrating all existing member data into the new IMS. You will be contacted with temporary login credentials to access your new profile, verify your information, and update any necessary details to ensure a seamless transition.",
              },
              {
                question: "Can I use the system on my mobile phone?",
                answer:
                  "Yes. The IMS is a fully responsive web application that works seamlessly on desktops, tablets, and mobile phones. It is also designed as a Progressive Web App (PWA) for an enhanced, app-like experience on mobile devices.",
              },
              {
                question: "Who do I contact for support?",
                answer:
                  "For any questions or assistance, please reach out to our team through the contact channels provided below. We offer comprehensive support, including user manuals and training sessions, to ensure you can navigate the IMS effortlessly.",
              },
            ].map((faq, index) => (
              <div
                key={index}
                className="mb-3 overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-300 hover:shadow-md sm:mb-4 sm:rounded-xl"
              >
                <button
                  className="flex w-full items-center justify-between p-4 text-left sm:p-6"
                  onClick={() => toggleFaq(index)}
                >
                  <h3 className="text-sm font-medium text-gray-800 sm:text-lg md:text-xl">
                    {faq.question}
                  </h3>
                  <svg
                    className={`h-5 w-5 transform text-blue-600 transition-transform duration-300 sm:h-6 sm:w-6 ${activeFaq === index ? "rotate-180" : ""}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                <div
                  className={`transition-all duration-300 ${activeFaq === index ? "max-h-96 opacity-100" : "max-h-0 opacity-0"}`}
                >
                  <div className="p-4 pt-0 text-sm text-gray-600 sm:p-6 sm:pt-0 sm:text-base">
                    {faq.answer}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Us Section */}
      <section id="contact" className="bg-white py-12 sm:py-16">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="text-center">
            <span className="mb-3 inline-block rounded-full bg-blue-100 px-3 py-1 text-xs font-semibold text-blue-600 sm:px-4 sm:text-sm">
              GET IN TOUCH
            </span>
            <h2 className="mb-4 text-2xl font-bold text-gray-800 sm:text-3xl md:text-4xl">
              Contact <span className="text-blue-600">CONGOMA</span>
            </h2>
            <p className="mx-auto mb-8 max-w-2xl text-base text-gray-600 sm:mb-12 sm:text-lg">
              Have questions or need assistance? Reach out to our team through
              any of these channels.
            </p>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {/* Contact Information */}
            <div className="rounded-xl bg-blue-50 p-6 shadow-sm sm:rounded-2xl sm:p-8">
              <h3 className="mb-4 text-xl font-semibold text-blue-600 sm:mb-6 sm:text-2xl">
                Our Office
              </h3>

              <div className="space-y-4 sm:space-y-6">
                <div className="flex items-start">
                  <div className="mr-3 mt-1 flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 sm:mr-4 sm:h-10 sm:w-10">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 sm:h-5 sm:w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="mb-1 text-sm font-medium text-gray-800 sm:text-base">
                      Address
                    </h4>
                    <p className="text-xs text-gray-600 sm:text-sm">
                      CONGOMA Secretariat
                      <br />
                      Area 14, Plot No. 14/222
                      <br />
                      P.O. Box 30035
                      <br />
                      Lilongwe 3, Malawi
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="mr-3 mt-1 flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 sm:mr-4 sm:h-10 sm:w-10">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 sm:h-5 sm:w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="mb-1 text-sm font-medium text-gray-800 sm:text-base">
                      Email
                    </h4>
                    <p className="text-xs text-gray-600 sm:text-sm">
                      <EMAIL>
                      <br />
                      <EMAIL>
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="mr-3 mt-1 flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 sm:mr-4 sm:h-10 sm:w-10">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 sm:h-5 sm:w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="mb-1 text-sm font-medium text-gray-800 sm:text-base">
                      Phone
                    </h4>
                    <p className="text-xs text-gray-600 sm:text-sm">
                      +265 1 770 703
                      <br />
                      +265 888 123 456 (Mobile)
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="mr-3 mt-1 flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 sm:mr-4 sm:h-10 sm:w-10">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 sm:h-5 sm:w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h4 className="mb-1 text-sm font-medium text-gray-800 sm:text-base">
                      Working Hours
                    </h4>
                    <p className="text-xs text-gray-600 sm:text-sm">
                      Monday - Friday: 8:00 AM - 5:00 PM
                      <br />
                      Saturday: 9:00 AM - 1:00 PM
                      <br />
                      Sunday: Closed
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="rounded-xl bg-white p-6 shadow-lg sm:rounded-2xl sm:p-8">
              <h3 className="mb-4 text-xl font-semibold text-gray-800 sm:mb-6 sm:text-2xl">
                Send Us a Message
              </h3>
              <form className="space-y-4 sm:space-y-6">
                <div>
                  <label
                    htmlFor="name"
                    className="mb-1 block text-xs font-medium text-gray-700 sm:mb-2 sm:text-sm"
                  >
                    Your Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-blue-500 sm:px-4 sm:py-3"
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <label
                    htmlFor="email"
                    className="mb-1 block text-xs font-medium text-gray-700 sm:mb-2 sm:text-sm"
                  >
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-blue-500 sm:px-4 sm:py-3"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label
                    htmlFor="subject"
                    className="mb-1 block text-xs font-medium text-gray-700 sm:mb-2 sm:text-sm"
                  >
                    Subject
                  </label>
                  <input
                    type="text"
                    id="subject"
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-blue-500 sm:px-4 sm:py-3"
                    placeholder="How can we help?"
                  />
                </div>
                <div>
                  <label
                    htmlFor="message"
                    className="mb-1 block text-xs font-medium text-gray-700 sm:mb-2 sm:text-sm"
                  >
                    Your Message
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-blue-500 sm:px-4 sm:py-3"
                    placeholder="Type your message here..."
                  ></textarea>
                </div>
                <button
                  type="submit"
                  className="w-full rounded bg-gradient-to-r from-blue-600 to-blue-500 px-4 py-2 text-sm font-medium text-white transition-all hover:from-blue-700 hover:to-blue-700 hover:shadow-lg sm:px-6 sm:py-3 sm:text-base"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-white py-12 sm:py-16">
        <div className="container mx-auto px-4 text-center sm:px-6">
          <h2 className="mb-4 text-2xl font-bold text-gray-800 sm:mb-6 sm:text-3xl">
            Stay Updated with CONGOMA
          </h2>
          <p className="mx-auto mb-6 max-w-2xl text-base text-gray-600 sm:mb-8 sm:text-xl">
            Subscribe to our newsletter for the latest news, events, and
            opportunities in Malawi&apos;s NGO sector.
          </p>

          {subscribed ? (
            <div className="inline-block rounded-lg bg-green-500 px-4 py-2 text-xs text-white sm:px-6 sm:py-3 sm:text-sm">
              Thank you for subscribing!
            </div>
          ) : (
            <form
              onSubmit={handleSubmit}
              className="mx-auto flex max-w-md flex-col gap-3 sm:flex-row sm:gap-4"
            >
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="flex-grow rounded-lg px-3 py-2 text-sm text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-300 sm:px-4 sm:py-3 sm:text-base"
                required
              />
              <button
                type="submit"
                className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700 sm:px-6 sm:py-3 sm:text-base"
              >
                Subscribe
              </button>
            </form>
          )}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 py-8 text-white sm:py-12">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4">
            <div>
              <h3 className="mb-3 text-lg font-bold sm:text-xl">CONGOMA</h3>
              <p className="mb-3 text-xs text-gray-400 sm:text-sm">
                The coordinating body for NGOs in Malawi since 1985.
              </p>
              <div className="flex space-x-3 sm:space-x-4">
                <a href="#" className="text-gray-400 hover:text-white">
                  <span className="sr-only">Facebook</span>
                  <svg
                    className="h-5 w-5 sm:h-6 sm:w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      fillRule="evenodd"
                      d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                      clipRule="evenodd"
                    />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white">
                  <span className="sr-only">Twitter</span>
                  <svg
                    className="h-5 w-5 sm:h-6 sm:w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-white">
                  <span className="sr-only">LinkedIn</span>
                  <svg
                    className="h-5 w-5 sm:h-6 sm:w-6"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      fillRule="evenodd"
                      d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"
                      clipRule="evenodd"
                    />
                  </svg>
                </a>
              </div>
            </div>
            <div>
              <h4 className="mb-2 text-sm font-semibold sm:text-base">
                Quick Links
              </h4>
              <ul className="space-y-1">
                <li>
                  <a
                    href="#about"
                    className="text-xs text-gray-400 hover:text-white sm:text-sm"
                  >
                    About Us
                  </a>
                </li>
                <li>
                  <a
                    href="#registration"
                    className="text-xs text-gray-400 hover:text-white sm:text-sm"
                  >
                    How to Register
                  </a>
                </li>
                <li>
                  <a
                    href="#demo"
                    className="text-xs text-gray-400 hover:text-white sm:text-sm"
                  >
                    Platform Demo
                  </a>
                </li>
                <li>
                  <a
                    href="#faqs"
                    className="text-xs text-gray-400 hover:text-white sm:text-sm"
                  >
                    FAQs
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="mb-2 text-sm font-semibold sm:text-base">
                Resources
              </h4>
              <ul className="space-y-1">
                <li>
                  <a
                    href="#"
                    className="text-xs text-gray-400 hover:text-white sm:text-sm"
                  >
                    NGO Directory
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-xs text-gray-400 hover:text-white sm:text-sm"
                  >
                    Reports
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-xs text-gray-400 hover:text-white sm:text-sm"
                  >
                    Policy Documents
                  </a>
                </li>
              </ul>
            </div>
            <div id="contact">
              <h4 className="mb-2 text-sm font-semibold sm:text-base">
                Contact Us
              </h4>
              <address className="text-xs not-italic text-gray-400 sm:text-sm">
                <p>CONGOMA Secretariat</p>
                <p>Area 14, Plot No. 14/222</p>
                <p>P.O. Box 30035</p>
                <p>Lilongwe 3, Malawi</p>
                <p>Email: <EMAIL></p>
                <p>Phone: +265 1 770 703</p>
              </address>
            </div>
          </div>
          <div className="mt-8 border-t border-gray-700 pt-6 text-center text-xs text-gray-400 sm:mt-12 sm:text-sm">
            <p>
              © {new Date().getFullYear()} Council for NGOs in Malawi
              (CONGOMA). All rights reserved.
            </p>
          </div>
        </div>
      </footer>

      {/* Popups */}
      <LoginPopup
        isOpen={showLoginPopup}
        onClose={() => setShowLoginPopup(false)}
        onRegisterClick={() => {
          setShowLoginPopup(false);
          setShowRegisterPopup(true);
        }}
      />

      <RegisterPopup
        isOpen={showRegisterPopup}
        onClose={() => setShowRegisterPopup(false)}
        onLoginClick={() => {
          setShowRegisterPopup(false);
          setShowLoginPopup(true);
        }}
      />

    </div>
  );
}
