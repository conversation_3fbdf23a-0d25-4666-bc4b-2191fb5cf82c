"use client";

import React, { useState } from "react";

export default function ComplianceStatusPage() {
  const [searchTerm, setSearchTerm] = useState("");

  const complianceStatus = [
    {
      id: "1",
      ngoName: "Youth Empowerment Initiative",
      status: "compliant",
      lastReviewDate: "2024-01-15",
      issues: [],
      complianceScore: 95,
    },
    {
      id: "2",
      ngoName: "Community Health Partners",
      status: "non_compliant",
      lastReviewDate: "2024-01-10",
      issues: ["Missing annual report", "Incomplete board member info"],
      complianceScore: 65,
    },
    {
      id: "3",
      ngoName: "Education for All",
      status: "pending_review",
      lastReviewDate: "2024-01-18",
      issues: ["Under review"],
      complianceScore: 0,
    },
  ];

  const filteredStatus = complianceStatus.filter((status) =>
    status.ngoName.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "compliant":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-400";
      case "non_compliant":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-400";
      case "pending_review":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const totalNGOs = filteredStatus.length;
  const compliantNGOs = filteredStatus.filter(
    (s) => s.status === "compliant",
  ).length;
  const averageScore =
    filteredStatus.length > 0
      ? Math.round(
          filteredStatus.reduce((sum, s) => sum + s.complianceScore, 0) /
            filteredStatus.length,
        )
      : 0;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Compliance Status by NGO
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Monitor compliance status and issues for all NGOs
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total NGOs
              </p>
              <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
                {totalNGOs}
              </p>
            </div>
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Compliant
              </p>
              <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
                {compliantNGOs}
              </p>
            </div>
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Avg Compliance Score
              </p>
              <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
                {averageScore}%
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
        <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Compliance Status
            </h2>
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
              <input
                type="text"
                placeholder="Search NGOs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="rounded-md border border-gray-300 px-4 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  NGO Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Compliance Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Last Review
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Issues
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {filteredStatus.map((status) => (
                <tr
                  key={status.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                    {status.ngoName}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(status.status)}`}
                    >
                      {status.status.replace("_", " ")}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                    {status.complianceScore}%
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {status.lastReviewDate}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {status.issues.length > 0
                      ? status.issues.join(", ")
                      : "None"}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
