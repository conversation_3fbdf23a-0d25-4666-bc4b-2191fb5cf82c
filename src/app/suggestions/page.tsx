"use client";
import React from "react";

const suggestions = [
  {
    id: 1,
    suggestion: "Add dark mode support",
    submittedBy: "<PERSON>",
    date: "2024-02-10",
    status: "Reviewed",
  },
  {
    id: 2,
    suggestion: "Improve dashboard speed",
    submittedBy: "<PERSON>",
    date: "2024-03-15",
    status: "Pending",
  },
  {
    id: 3,
    suggestion: "Add export to PDF",
    submittedBy: "<PERSON>",
    date: "2024-04-20",
    status: "Implemented",
  },
];

const statusStyles: Record<string, string> = {
  Reviewed: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  Pending:
    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  Implemented:
    "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
};

export default function SuggestionsPage() {
  return (
    <div className="mt-4 w-full">
      <div className="space-y-6">
        {suggestions.map((sug) => (
          <div
            key={sug.id}
            className="flex flex-col rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark sm:flex-row sm:items-center sm:justify-between"
          >
            <div>
              <div className="mb-1 text-lg font-semibold text-dark dark:text-white">
                {sug.suggestion}
              </div>
              <div className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                Submitted by: {sug.submittedBy}
              </div>
              <div className="mb-1 text-sm text-gray-500 dark:text-gray-400">
                Date: {sug.date}
              </div>
              <span
                className={`inline-block rounded px-3 py-1 text-xs font-semibold ${statusStyles[sug.status]}`}
              >
                {sug.status}
              </span>
            </div>
            <button className="mt-4 rounded bg-primary px-4 py-2 text-white transition hover:bg-primary/80 sm:mt-0">
              View
            </button>
          </div>
        ))}
        {suggestions.length === 0 && (
          <div className="rounded bg-gray-100 p-8 text-center text-gray-500 dark:bg-dark-2 dark:text-gray-400">
            No suggestions found.
          </div>
        )}
      </div>
    </div>
  );
}
