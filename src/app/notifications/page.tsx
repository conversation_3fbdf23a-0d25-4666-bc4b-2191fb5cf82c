"use client";
import React, { useState } from "react";
import Image from "next/image";
import {
  HiOutlineBell,
  HiOutlineCheckCircle,
  HiOutlineExclamationTriangle,
  HiOutlineInformationCircle,
} from "react-icons/hi2";

interface Notification {
  id: number;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  timestamp: string;
  isRead: boolean;
  userImage?: string;
  userName?: string;
}

const notifications: Notification[] = [
  {
    id: 1,
    title: "New Network Member",
    message: "<PERSON> has joined the Health Sector Network",
    type: "success",
    timestamp: "2024-01-15T10:30:00",
    isRead: false,
    userImage: "/images/user/user-15.png",
    userName: "<PERSON>",
  },
  {
    id: 2,
    title: "Document Update",
    message: "Certificate renewal reminder for Local Org 1",
    type: "warning",
    timestamp: "2024-01-15T09:15:00",
    isRead: false,
    userImage: "/images/user/user-03.png",
    userName: "Local Org 1",
  },
  {
    id: 3,
    title: "System Maintenance",
    message: "Scheduled maintenance on January 20th, 2024",
    type: "info",
    timestamp: "2024-01-15T08:45:00",
    isRead: true,
  },
  {
    id: 4,
    title: "Application Approved",
    message: "Your organization registration has been approved",
    type: "success",
    timestamp: "2024-01-14T16:20:00",
    isRead: true,
    userImage: "/images/user/user-26.png",
    userName: "Your Organization",
  },
  {
    id: 5,
    title: "Network Meeting",
    message: "Monthly sector network meeting scheduled for next week",
    type: "info",
    timestamp: "2024-01-14T14:30:00",
    isRead: false,
  },
  {
    id: 6,
    title: "Document Expired",
    message: "Certificate for International Org 2 has expired",
    type: "error",
    timestamp: "2024-01-14T11:15:00",
    isRead: false,
    userImage: "/images/user/user-28.png",
    userName: "International Org 2",
  },
  {
    id: 7,
    title: "New Message",
    message: "You have a new message from Sector Network Coordinator",
    type: "info",
    timestamp: "2024-01-14T10:00:00",
    isRead: true,
  },
  {
    id: 8,
    title: "Profile Update",
    message: "Your profile information has been successfully updated",
    type: "success",
    timestamp: "2024-01-13T15:45:00",
    isRead: true,
  },
];

export default function NotificationsPage() {
  const [filter, setFilter] = useState<"all" | "unread" | "read">("all");
  const [search, setSearch] = useState("");

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "success":
        return <HiOutlineCheckCircle className="text-green-500" />;
      case "warning":
        return <HiOutlineExclamationTriangle className="text-yellow-500" />;
      case "error":
        return <HiOutlineExclamationTriangle className="text-red-500" />;
      default:
        return <HiOutlineInformationCircle className="text-blue-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "success":
        return "border-l-green-500 bg-green-50 dark:bg-green-900/20";
      case "warning":
        return "border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20";
      case "error":
        return "border-l-red-500 bg-red-50 dark:bg-red-900/20";
      default:
        return "border-l-blue-500 bg-blue-50 dark:bg-blue-900/20";
    }
  };

  const filteredNotifications = notifications.filter((notification) => {
    const matchesFilter =
      filter === "all" ||
      (filter === "unread" && !notification.isRead) ||
      (filter === "read" && notification.isRead);

    const matchesSearch =
      notification.title.toLowerCase().includes(search.toLowerCase()) ||
      notification.message.toLowerCase().includes(search.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60),
    );

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return "Yesterday";
    return date.toLocaleDateString();
  };

  return (
    <div className="container mx-auto px-4 py-8 w-full rounded-xl bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex size-12 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
            <HiOutlineBell className="size-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Notifications
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Manage your notifications and stay updated
            </p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex space-x-2">
          <button
            onClick={() => setFilter("all")}
            className={`rounded-lg px-4 py-2 text-sm font-medium transition ${
              filter === "all"
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter("unread")}
            className={`rounded-lg px-4 py-2 text-sm font-medium transition ${
              filter === "unread"
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            Unread
          </button>
          <button
            onClick={() => setFilter("read")}
            className={`rounded-lg px-4 py-2 text-sm font-medium transition ${
              filter === "read"
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
            }`}
          >
            Read
          </button>
        </div>

        <div className="relative">
          <input
            type="text"
            placeholder="Search notifications..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 pl-10 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          />
          <HiOutlineBell className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400" />
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-3">
        {filteredNotifications.length === 0 ? (
          <div className="rounded-lg bg-gray-50 p-8 text-center dark:bg-gray-800">
            <HiOutlineBell className="mx-auto size-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
              No notifications found
            </h3>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {search
                ? "Try adjusting your search terms"
                : "You're all caught up!"}
            </p>
          </div>
        ) : (
          filteredNotifications.map((notification) => (
            <div
              key={notification.id}
              className={`rounded-lg border-l-4 p-4 transition hover:shadow-md ${
                notification.isRead
                  ? "bg-white dark:bg-gray-800"
                  : "bg-white shadow-sm dark:bg-gray-800"
              } ${getTypeColor(notification.type)}`}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  {notification.userImage ? (
                    <Image
                      src={notification.userImage}
                      alt={notification.userName || "User"}
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                  ) : (
                    <div className="flex size-10 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
                      {getTypeIcon(notification.type)}
                    </div>
                  )}
                </div>

                <div className="min-w-0 flex-1">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3
                        className={`text-sm font-medium ${
                          notification.isRead
                            ? "text-gray-700 dark:text-gray-300"
                            : "text-gray-900 dark:text-white"
                        }`}
                      >
                        {notification.title}
                      </h3>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        {notification.message}
                      </p>
                      {notification.userName && (
                        <p className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                          {notification.userName}
                        </p>
                      )}
                    </div>
                    <div className="ml-4 flex-shrink-0">
                      <span className="text-xs text-gray-400 dark:text-gray-500">
                        {formatTime(notification.timestamp)}
                      </span>
                    </div>
                  </div>
                </div>

                {!notification.isRead && (
                  <div className="flex-shrink-0">
                    <div className="size-2 rounded-full bg-primary"></div>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
