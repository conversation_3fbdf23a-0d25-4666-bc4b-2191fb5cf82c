"use client";

import Image from "next/image";
import { useState, useEffect } from "react";
import {
  CameraIcon,
  EditIcon,
  LocationIcon,
  CalendarIcon,
  LinkIcon,
  AwardIcon,
  StarIcon,
} from "./_components/icons";
import { SocialAccounts } from "./_components/social-accounts";
import { ProfileSkeleton } from "./_components/profile-skeleton";

// Default Avatar Component
const DefaultAvatar = ({
  name,
  size = "h-32 w-32",
}: {
  name: string;
  size?: string;
}) => {
  const initials = name
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase();

  return (
    <div
      className={`${size} flex items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-blue-600 text-2xl font-bold text-white shadow-2xl`}
    >
      {initials}
    </div>
  );
};

export default function Page() {
  const [data, setData] = useState({
    name: "<PERSON><PERSON> <PERSON>",
    profilePhoto: "", // Empty to show default avatar
    bio: "Executive Director of the Council for NGOs in Malawi (CONGOMA) with over 15 years of experience in NGO coordination, policy advocacy, and development sector leadership. Passionate about strengthening Malawi's civil society and creating an enabling environment for NGOs to contribute effectively to national development.",
    location: "Lilongwe, Malawi",
    joinedDate: "January 2020",
    website: "congoma.mw",
    phone: "+265 1 770 703",
    email: "<EMAIL>",
    skills: [
      "NGO Coordination",
      "Policy Advocacy",
      "Development Sector",
      "Capacity Building",
      "Strategic Planning",
      "Stakeholder Engagement",
      "Project Management",
      "Leadership",
    ],
    achievements: [
      {
        title: "Excellence in NGO Leadership",
        year: "2023",
        icon: AwardIcon,
        description:
          "Awarded by Malawi Government for outstanding contribution to civil society development",
      },
      {
        title: "Regional NGO Coordinator",
        year: "2022",
        icon: StarIcon,
        description:
          "Recognized as top NGO coordinator in Southern Africa region",
      },
      {
        title: "Policy Advocacy Champion",
        year: "2021",
        icon: AwardIcon,
        description:
          "Successfully advocated for favorable NGO policies in Malawi",
      },
    ],
    responsibilities: [
      "Lead CONGOMA's strategic direction and operations",
      "Coordinate 500+ member NGOs across Malawi",
      "Represent NGO sector at national and international forums",
      "Advocate for favorable NGO policies and regulations",
      "Oversee capacity building programs for member organizations",
      "Manage partnerships with government and development partners",
    ],
    stats: {
      memberNGOs: "500+",
      yearsService: "35+",
      sectorsCovered: "20+",
      nationwideCoverage: "100%",
    },
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isImageLoading, setIsImageLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e: any) => {
    if (e.target.name === "profilePhoto") {
      const file = e.target?.files[0];
      setData({
        ...data,
        profilePhoto: file && URL.createObjectURL(file),
      });
    } else {
      setData({
        ...data,
        [e.target.name]: e.target.value,
      });
    }
  };

  const handleImageLoad = () => {
    setIsImageLoading(false);
  };

  if (isLoading) {
    return <ProfileSkeleton />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="animate-fade-in mb-8">
          <h1 className="bg-gradient-to-r from-blue-600 to-blue-600 bg-clip-text text-4xl font-bold text-transparent">
            Admin Profile
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Executive Director - Council for NGOs in Malawi
          </p>
        </div>

        {/* Main Profile Card */}
        <div className="hover:shadow-3xl animate-fade-in-up relative overflow-hidden rounded-3xl bg-white/80 shadow-2xl backdrop-blur-xl transition-all duration-500 dark:bg-gray-800/80">
          {/* Profile Info Section */}
          <div className="relative px-8 py-8">
            {/* Profile Photo */}
            <div className="relative mb-6 flex justify-center">
              <div className="group relative">
                <div className="hover:shadow-3xl relative h-32 w-32 overflow-hidden rounded-full border-4 border-white shadow-2xl transition-all duration-300 dark:border-gray-800">
                  {data?.profilePhoto ? (
                    <Image
                      src={data?.profilePhoto}
                      width={128}
                      height={128}
                      className={`h-full w-full object-cover transition-all duration-500 ${
                        isImageLoading
                          ? "scale-110 blur-sm"
                          : "scale-100 blur-0"
                      }`}
                      alt="CONGOMA Executive Director"
                      onLoad={handleImageLoad}
                    />
                  ) : (
                    <DefaultAvatar name={data?.name} />
                  )}
                </div>

                {/* Profile Photo Edit Button */}
                <label
                  htmlFor="profilePhoto"
                  className="absolute -bottom-2 -right-2 flex h-10 w-10 cursor-pointer items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-500 text-white shadow-lg transition-all duration-300 hover:scale-110 hover:shadow-xl group-hover:scale-110"
                >
                  <input
                    type="file"
                    name="profilePhoto"
                    id="profilePhoto"
                    className="sr-only"
                    onChange={handleChange}
                    accept="image/png, image/jpg, image/jpeg"
                  />
                  <CameraIcon className="h-4 w-4 transition-transform duration-300 group-hover:scale-110" />
                </label>
              </div>
            </div>

            {/* User Info */}
            <div className="text-center">
              <h2 className="animate-fade-in-up mb-2 text-3xl font-bold text-gray-900 dark:text-white">
                {data?.name}
              </h2>
              <p className="animate-fade-in-up mb-4 text-lg font-medium text-blue-600 dark:text-blue-400">
                Executive Director - CONGOMA
              </p>

              {/* CONGOMA Stats */}
              <div className="animate-fade-in-up mb-8 grid grid-cols-2 gap-4 rounded-2xl bg-gradient-to-r from-blue-50 to-blue-50 p-6 transition-all duration-300 hover:shadow-lg dark:from-gray-700/50 dark:to-gray-600/50 md:grid-cols-4">
                <div className="group text-center">
                  <div className="text-2xl font-bold text-gray-900 transition-all duration-300 group-hover:scale-110 dark:text-white">
                    {data?.stats?.memberNGOs}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Member NGOs
                  </div>
                </div>
                <div className="group text-center">
                  <div className="text-2xl font-bold text-gray-900 transition-all duration-300 group-hover:scale-110 dark:text-white">
                    {data?.stats?.yearsService}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Years of Service
                  </div>
                </div>
                <div className="group text-center">
                  <div className="text-2xl font-bold text-gray-900 transition-all duration-300 group-hover:scale-110 dark:text-white">
                    {data?.stats?.sectorsCovered}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Sectors Covered
                  </div>
                </div>
                <div className="group text-center">
                  <div className="text-2xl font-bold text-gray-900 transition-all duration-300 group-hover:scale-110 dark:text-white">
                    {data?.stats?.nationwideCoverage}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Nationwide Coverage
                  </div>
                </div>
              </div>

              {/* Bio */}
              <div className="animate-fade-in-up mb-8 text-left">
                <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">
                  About Me
                </h3>
                <p className="leading-relaxed text-gray-600 dark:text-gray-300">
                  {data?.bio}
                </p>
              </div>

              {/* Contact Info */}
              <div className="animate-fade-in-up mb-8 grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="flex items-center gap-3 rounded-xl bg-gray-50 p-4 transition-all duration-300 hover:scale-105 hover:shadow-lg dark:bg-gray-700/50">
                  <LocationIcon className="h-5 w-5 text-blue-500" />
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Office Location
                    </div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {data?.location}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-xl bg-gray-50 p-4 transition-all duration-300 hover:scale-105 hover:shadow-lg dark:bg-gray-700/50">
                  <CalendarIcon className="h-5 w-5 text-blue-500" />
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Started Role
                    </div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {data?.joinedDate}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-xl bg-gray-50 p-4 transition-all duration-300 hover:scale-105 hover:shadow-lg dark:bg-gray-700/50">
                  <LinkIcon className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Website
                    </div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {data?.website}
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Contact Info */}
              <div className="animate-fade-in-up mb-8 grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="flex items-center gap-3 rounded-xl bg-gray-50 p-4 transition-all duration-300 hover:scale-105 hover:shadow-lg dark:bg-gray-700/50">
                  <div className="h-5 w-5 rounded-full bg-green-500"></div>
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Email
                    </div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {data?.email}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3 rounded-xl bg-gray-50 p-4 transition-all duration-300 hover:scale-105 hover:shadow-lg dark:bg-gray-700/50">
                  <div className="h-5 w-5 rounded-full bg-blue-500"></div>
                  <div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Phone
                    </div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {data?.phone}
                    </div>
                  </div>
                </div>
              </div>

              {/* Key Responsibilities */}
              <div className="animate-fade-in-up mb-8 text-left">
                <h3 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
                  Key Responsibilities
                </h3>
                <div className="grid gap-3 md:grid-cols-2">
                  {data?.responsibilities?.map((responsibility, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3 rounded-xl bg-gradient-to-r from-blue-50 to-blue-50 p-4 transition-all duration-300 hover:shadow-lg dark:from-gray-700/50 dark:to-gray-600/50"
                    >
                      <div className="mt-1 h-2 w-2 rounded-full bg-blue-500"></div>
                      <p className="text-gray-700 dark:text-gray-300">
                        {responsibility}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Skills */}
              <div className="animate-fade-in-up mb-8 text-left">
                <h3 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
                  Core Competencies
                </h3>
                <div className="flex flex-wrap gap-2">
                  {data?.skills?.map((skill, index) => (
                    <span
                      key={index}
                      className="rounded-full bg-gradient-to-r from-blue-500 to-blue-500 px-4 py-2 text-sm font-medium text-white shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {/* Achievements */}
              <div className="animate-fade-in-up mb-8 text-left">
                <h3 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
                  Recognition & Awards
                </h3>
                <div className="grid gap-4 md:grid-cols-3">
                  {data?.achievements?.map((achievement, index) => (
                    <div
                      key={index}
                      className="group rounded-xl bg-gradient-to-r from-yellow-50 to-orange-50 p-4 transition-all duration-300 hover:scale-105 hover:shadow-lg dark:from-gray-700/50 dark:to-gray-600/50"
                    >
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 text-white transition-transform duration-300 group-hover:scale-110">
                          <achievement.icon className="h-5 w-5" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {achievement.title}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {achievement.year}
                          </div>
                          <div className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                            {achievement.description}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Social Accounts */}
              <SocialAccounts />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
