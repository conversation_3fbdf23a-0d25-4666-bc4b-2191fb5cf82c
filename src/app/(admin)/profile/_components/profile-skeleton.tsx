import React from "react";

export function ProfileSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Header Skeleton */}
        <div className="mb-8">
          <div className="h-10 w-48 animate-pulse rounded-lg bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
          <div className="mt-2 h-4 w-80 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
        </div>

        {/* Main Profile Card Skeleton */}
        <div className="relative overflow-hidden rounded-3xl bg-white/80 shadow-2xl backdrop-blur-xl dark:bg-gray-800/80">
          {/* Cover Photo Skeleton */}
          <div className="relative h-80 overflow-hidden">
            <div className="h-full w-full animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700" />
          </div>

          {/* Profile Info Section Skeleton */}
          <div className="relative px-8 pb-8">
            {/* Profile Photo Skeleton */}
            <div className="relative -mt-20 mb-6 flex justify-center">
              <div className="h-32 w-32 animate-pulse rounded-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
            </div>

            {/* User Info Skeleton */}
            <div className="text-center">
              <div className="mx-auto mb-2 h-8 w-48 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
              <div className="mx-auto mb-4 h-6 w-32 animate-pulse rounded bg-gradient-to-r from-blue-200 to-blue-300 dark:from-blue-700 dark:to-blue-600" />

              {/* Stats Skeleton */}
              <div className="mb-8 grid grid-cols-3 gap-4 rounded-2xl bg-gradient-to-r from-blue-50 to-blue-50 p-6 dark:from-gray-700/50 dark:to-gray-600/50">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="text-center">
                    <div className="mx-auto mb-1 h-6 w-12 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                    <div className="mx-auto h-4 w-16 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                  </div>
                ))}
              </div>

              {/* Bio Skeleton */}
              <div className="mb-8 text-left">
                <div className="mb-3 h-6 w-24 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                <div className="space-y-2">
                  <div className="h-4 w-full animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                  <div className="h-4 w-3/4 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                  <div className="h-4 w-5/6 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                </div>
              </div>

              {/* Contact Info Skeleton */}
              <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-3">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="flex items-center gap-3 rounded-xl bg-gray-50 p-4 dark:bg-gray-700/50"
                  >
                    <div className="h-5 w-5 animate-pulse rounded-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                    <div className="flex-1">
                      <div className="mb-1 h-3 w-16 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                      <div className="h-4 w-24 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                    </div>
                  </div>
                ))}
              </div>

              {/* Skills Skeleton */}
              <div className="mb-8 text-left">
                <div className="mb-4 h-6 w-32 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                <div className="flex flex-wrap gap-2">
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <div
                      key={i}
                      className="h-8 w-20 animate-pulse rounded-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600"
                    />
                  ))}
                </div>
              </div>

              {/* Achievements Skeleton */}
              <div className="mb-8 text-left">
                <div className="mb-4 h-6 w-40 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                <div className="grid gap-4 md:grid-cols-3">
                  {[1, 2, 3].map((i) => (
                    <div
                      key={i}
                      className="rounded-xl bg-gradient-to-r from-yellow-50 to-orange-50 p-4 dark:from-gray-700/50 dark:to-gray-600/50"
                    >
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 animate-pulse rounded-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                        <div className="flex-1">
                          <div className="mb-1 h-4 w-24 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                          <div className="h-3 w-12 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Social Accounts Skeleton */}
              <div className="text-left">
                <div className="mb-4 h-6 w-32 animate-pulse rounded bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600" />
                <div className="flex flex-wrap items-center gap-3">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="h-12 w-12 animate-pulse rounded-xl bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600"
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
