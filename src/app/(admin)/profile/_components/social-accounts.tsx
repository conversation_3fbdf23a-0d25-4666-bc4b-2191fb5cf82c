import Link from "next/link";
import {
  DribbleIcon,
  FacebookIcon,
  GitHubIcon,
  LinkedInIcon,
  XIcon,
} from "./icons";

const ACCOUNTS = [
  {
    platform: "LinkedIn",
    url: "#",
    Icon: LinkedInIcon,
    color: "from-blue-600 to-blue-700",
    hoverColor: "hover:from-blue-700 hover:to-blue-800",
    description: "Professional Network",
  },
  {
    platform: "Facebook",
    url: "#",
    Icon: FacebookIcon,
    color: "from-blue-500 to-blue-600",
    hoverColor: "hover:from-blue-600 hover:to-blue-700",
    description: "CONGOMA Updates",
  },
  {
    platform: "X",
    url: "#",
    Icon: XIcon,
    color: "from-gray-700 to-gray-800",
    hoverColor: "hover:from-gray-800 hover:to-gray-900",
    description: "Policy Updates",
  },
  {
    platform: "Development",
    url: "#",
    Icon: GitHubIcon,
    color: "from-green-600 to-green-700",
    hoverColor: "hover:from-green-700 hover:to-green-800",
    description: "Sector Resources",
  },
  {
    platform: "Partnerships",
    url: "#",
    Icon: DribbleIcon,
    color: "from-blue-500 to-blue-600",
    hoverColor: "hover:from-blue-600 hover:to-blue-700",
    description: "Collaborations",
  },
];

export function SocialAccounts() {
  return (
    <div className="text-left">
      <h3 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
        Professional Networks & Platforms
      </h3>
      <div className="flex flex-wrap items-center gap-3">
        {ACCOUNTS.map(({ Icon, color, hoverColor, description, ...item }) => (
          <Link
            key={item.platform}
            href={item.url}
            className={`group flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r ${color} ${hoverColor} text-white shadow-lg transition-all duration-300 hover:scale-110 hover:shadow-xl`}
            title={`${item.platform} - ${description}`}
          >
            <span className="sr-only">View {item.platform} Profile</span>
            <Icon className="h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
          </Link>
        ))}
      </div>
      <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
        Connect with CONGOMA across various professional platforms and stay
        updated with the latest NGO sector developments.
      </p>
    </div>
  );
}
