"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineCreditCard,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineCalendar,
  HiOutlineBuildingOffice,
  HiOutlineGlobeAlt,
  HiOutlineMagnifyingGlass,
  HiOutlineFunnel,
  HiOutlineEye,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineArrowDownTray,
  HiOutlineDocumentText,
} from "react-icons/hi2";

interface PaymentRecord {
  id: string;
  organizationName: string;
  organizationType: "local" | "international";
  paymentType: string;
  amount: number;
  status: "paid" | "pending" | "overdue" | "cancelled";
  dueDate: string;
  paidDate?: string;
  invoiceNumber: string;
  description: string;
}

export default function OrganizationPaymentsPage() {
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterType, setFilterType] = useState<string>("all");

  useEffect(() => {
    // Simulate API call
    const fetchPayments = async () => {
      setLoading(true);
      // Mock data
      const mockPayments: PaymentRecord[] = [
        {
          id: "1",
          organizationName: "Community Development Initiative",
          organizationType: "local",
          paymentType: "Annual Membership",
          amount: 50000,
          status: "paid",
          dueDate: "2024-01-15",
          paidDate: "2024-01-10",
          invoiceNumber: "INV-2024-001",
          description: "Annual membership fee for 2024",
        },
        {
          id: "2",
          organizationName: "International Aid Foundation",
          organizationType: "international",
          paymentType: "Registration Fee",
          amount: 75000,
          status: "pending",
          dueDate: "2024-02-28",
          invoiceNumber: "INV-2024-002",
          description: "New organization registration fee",
        },
        {
          id: "3",
          organizationName: "Rural Empowerment Network",
          organizationType: "local",
          paymentType: "Annual Membership",
          amount: 50000,
          status: "overdue",
          dueDate: "2024-01-31",
          invoiceNumber: "INV-2024-003",
          description: "Annual membership fee for 2024",
        },
        {
          id: "4",
          organizationName: "Global Health Partners",
          organizationType: "international",
          paymentType: "Event Participation",
          amount: 25000,
          status: "paid",
          dueDate: "2024-02-15",
          paidDate: "2024-02-12",
          invoiceNumber: "INV-2024-004",
          description: "Conference participation fee",
        },
        {
          id: "5",
          organizationName: "Youth Development Association",
          organizationType: "local",
          paymentType: "Annual Membership",
          amount: 50000,
          status: "cancelled",
          dueDate: "2024-01-15",
          invoiceNumber: "INV-2024-005",
          description: "Annual membership fee for 2024",
        },
      ];

      setTimeout(() => {
        setPayments(mockPayments);
        setLoading(false);
      }, 1000);
    };

    fetchPayments();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "overdue":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "cancelled":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "paid":
        return <HiOutlineCheckCircle className="h-4 w-4 text-green-600" />;
      case "pending":
        return <HiOutlineClock className="h-4 w-4 text-yellow-600" />;
      case "overdue":
        return <HiOutlineXCircle className="h-4 w-4 text-red-600" />;
      case "cancelled":
        return <HiOutlineXCircle className="h-4 w-4 text-gray-600" />;
      default:
        return <HiOutlineClock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "local":
        return <HiOutlineBuildingOffice className="h-5 w-5 text-blue-600" />;
      case "international":
        return <HiOutlineGlobeAlt className="h-5 w-5 text-purple-600" />;
      default:
        return <HiOutlineBuildingOffice className="h-5 w-5 text-gray-600" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-MW", {
      style: "currency",
      currency: "MWK",
    }).format(amount);
  };

  const filteredPayments = payments.filter((payment) => {
    const matchesSearch =
      payment.organizationName
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      payment.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || payment.status === filterStatus;
    const matchesType =
      filterType === "all" || payment.organizationType === filterType;

    return matchesSearch && matchesStatus && matchesType;
  });

  const totalAmount = payments.reduce(
    (sum, payment) => sum + payment.amount,
    0,
  );
  const paidAmount = payments
    .filter((p) => p.status === "paid")
    .reduce((sum, payment) => sum + payment.amount, 0);
  const pendingAmount = payments
    .filter((p) => p.status === "pending")
    .reduce((sum, payment) => sum + payment.amount, 0);
  const overdueAmount = payments
    .filter((p) => p.status === "overdue")
    .reduce((sum, payment) => sum + payment.amount, 0);

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-gray-600 dark:text-gray-400">
            Loading payment records...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Organization Payment Status
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track and manage organization payment records
          </p>
        </div>
        <div className="flex gap-2">
          <button className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700">
            <HiOutlineArrowDownTray className="h-4 w-4" />
            Export Report
          </button>
          <button className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
            <HiOutlinePencil className="h-4 w-4" />
            Add Payment
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Amount
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(totalAmount)}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <HiOutlineCreditCard className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Paid Amount
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(paidAmount)}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <HiOutlineCheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Amount
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(pendingAmount)}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
              <HiOutlineClock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Overdue Amount
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(overdueAmount)}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900">
              <HiOutlineXCircle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="relative max-w-md flex-1">
          <HiOutlineMagnifyingGlass className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search organizations or invoices..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          />
        </div>

        <div className="flex gap-2">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="paid">Paid</option>
            <option value="pending">Pending</option>
            <option value="overdue">Overdue</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            <option value="all">All Types</option>
            <option value="local">Local</option>
            <option value="international">International</option>
          </select>
        </div>
      </div>

      {/* Payments Table */}
      <div className="rounded-lg bg-white shadow-sm dark:bg-gray-800">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Payment Records
          </h2>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Organization
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Invoice
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Payment Type
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Amount
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Due Date
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredPayments.map((payment) => (
                  <tr
                    key={payment.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
                          {getTypeIcon(payment.organizationType)}
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {payment.organizationName}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {payment.organizationType === "local"
                              ? "Local"
                              : "International"}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {payment.invoiceNumber}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {payment.description}
                        </p>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {payment.paymentType}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(payment.amount)}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(payment.status)}
                        <span
                          className={`rounded-full px-2 py-1 text-xs ${getStatusColor(payment.status)}`}
                        >
                          {payment.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {payment.dueDate}
                        </p>
                        {payment.paidDate && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Paid: {payment.paidDate}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlineEye className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlineDocumentText className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlinePencil className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-red-400 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-300">
                          <HiOutlineTrash className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredPayments.length === 0 && (
            <div className="py-8 text-center">
              <HiOutlineCreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                No payment records found matching your criteria.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
