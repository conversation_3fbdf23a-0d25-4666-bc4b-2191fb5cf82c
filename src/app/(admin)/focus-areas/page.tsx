"use client";
import React, { useState, useEffect } from "react";
import { HiOutlineLightBulb } from "react-icons/hi2";
import {
  getAllSectors,
  createSector,
  Sector,
} from "@/services/sector.services";

export default function FocusAreasPage() {
  const [focusAreas, setFocusAreas] = useState<Sector[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [form, setForm] = useState({
    name: "",
    description: "",
    category: "",
    status: "active",
  });

  useEffect(() => {
    const fetchSectors = async () => {
      try {
        setLoading(true);
        const response = await getAllSectors();
        if (response.status === "success" && response.data) {
          setFocusAreas(response.data);
        } else {
          setError(response.message || "Failed to fetch focus areas");
        }
      } catch (err) {
        setError("An error occurred while fetching focus areas");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchSectors();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await createSector({
        name: form.name,
        description: form.description,
        category: form.category,
        status: form.status,
      });

      if (response.status === "success" && response.data) {
        setFocusAreas([...focusAreas, response.data]);
        setShowModal(false);
        setForm({
          name: "",
          description: "",
          category: "",
          status: "active",
        });
      } else {
        setError(response.message || "Failed to create focus area");
      }
    } catch (err) {
      setError("An error occurred while creating the focus area");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mt-2 w-full" style={{ marginTop: "4px" }}>
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Focus Areas</h1>
        <button
          className="rounded bg-primary px-4 py-2 text-white transition hover:bg-primary/80"
          onClick={() => setShowModal(true)}
          disabled={loading}
        >
          Add Area of Focus
        </button>
      </div>

      {error && (
        <div className="mb-4 rounded-md bg-red-50 p-4 dark:bg-red-900/20">
          <div className="text-sm text-red-700 dark:text-red-400">{error}</div>
        </div>
      )}

      {loading && !showModal && (
        <div className="flex items-center justify-center py-10">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div
            className="relative w-full max-w-md rounded-xl bg-white p-8 shadow-lg dark:bg-gray-dark"
            style={{ marginTop: "60px" }}
          >
            <button
              className="absolute right-2 top-2 text-gray-400 hover:text-gray-700 dark:hover:text-white"
              onClick={() => setShowModal(false)}
            >
              &times;
            </button>
            <h2 className="mb-4 text-xl font-bold">Add Area of Focus</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="mb-1 block font-medium">Name</label>
                <input
                  type="text"
                  name="name"
                  value={form.name}
                  onChange={handleChange}
                  required
                  className="w-full rounded border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
                />
              </div>
              <div>
                <label className="mb-1 block font-medium">Category</label>
                <input
                  type="text"
                  name="category"
                  value={form.category}
                  onChange={handleChange}
                  required
                  className="w-full rounded border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
                />
              </div>
              <div>
                <label className="mb-1 block font-medium">Description</label>
                <textarea
                  name="description"
                  value={form.description}
                  onChange={handleChange}
                  required
                  className="w-full rounded border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
                />
              </div>
              <div>
                <label className="mb-1 block font-medium">Status</label>
                <select
                  name="status"
                  value={form.status}
                  onChange={handleChange}
                  className="w-full rounded border border-gray-300 px-3 py-2 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <button
                type="submit"
                className="w-full rounded bg-primary px-4 py-2 font-semibold text-white hover:bg-primary/80"
                disabled={loading}
              >
                {loading ? "Adding..." : "Add"}
              </button>
            </form>
          </div>
        </div>
      )}

      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {!loading &&
          focusAreas.map((area) => (
            <div
              key={area._id}
              className="flex flex-col rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark"
            >
              <div className="mb-4 flex size-16 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
                <HiOutlineLightBulb className="size-10 text-primary" />
              </div>
              <div className="mb-2 text-xl font-bold text-dark dark:text-white">
                {area.name}
              </div>
              <div className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                Category: {area.category}
              </div>
              <div className="mb-3 text-sm text-gray-600 dark:text-gray-300">
                {area.description || "No description available"}
              </div>
              <div className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                Organizations: {area.organizationCount || 0}
              </div>
              <div className="mb-4">
                <span
                  className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                    area.status === "active"
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                      : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                  }`}
                >
                  {area.status || "Unknown"}
                </span>
              </div>
              <button className="mt-auto rounded bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
                View Details
              </button>
            </div>
          ))}
        {!loading && focusAreas.length === 0 && (
          <div className="col-span-full rounded bg-gray-100 p-8 text-center text-gray-500 dark:bg-dark-2 dark:text-gray-400">
            No focus areas found.
          </div>
        )}
      </div>
    </div>
  );
}
