"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Network,
  Users,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Plus,
} from "lucide-react";

interface NetworkData {
  id: string;
  name: string;
  type: "sector" | "district";
  status: "active" | "inactive" | "pending";
  members: number;
  location: string;
  createdDate: string;
  description: string;
}

const mockNetworks: NetworkData[] = [
  {
    id: "1",
    name: "Health Sector Network",
    type: "sector",
    status: "active",
    members: 45,
    location: "Lilongwe",
    createdDate: "2024-01-10",
    description: "Network of health-focused organizations",
  },
  {
    id: "2",
    name: "Central Region District Network",
    type: "district",
    status: "active",
    members: 32,
    location: "Central Region",
    createdDate: "2024-01-15",
    description: "District-level network for Central Region",
  },
  {
    id: "3",
    name: "Education Sector Network",
    type: "sector",
    status: "pending",
    members: 28,
    location: "Blantyre",
    createdDate: "2024-02-05",
    description: "Network of education-focused organizations",
  },
];

export default function NetworkRegistryPage() {
  const [networks, setNetworks] = useState<NetworkData[]>(mockNetworks);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<"all" | "sector" | "district">(
    "all",
  );
  const [filterStatus, setFilterStatus] = useState<
    "all" | "active" | "inactive" | "pending"
  >("all");

  const filteredNetworks = networks.filter((network) => {
    const matchesSearch =
      network.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      network.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || network.type === filterType;
    const matchesStatus =
      filterStatus === "all" || network.status === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    return type === "sector"
      ? "bg-blue-100 text-blue-800"
      : "bg-purple-100 text-purple-800";
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Network Registry
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage and monitor registered networks in the system
        </p>
      </div>

      {/* Search and Filter Section */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"
                  size={20}
                />
                <Input
                  placeholder="Search networks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="sector">Sector</option>
                <option value="district">District</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Networks
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {networks.length}
                </p>
              </div>
              <Network className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Active Networks
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {
                    networks.filter((network) => network.status === "active")
                      .length
                  }
                </p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Members
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {networks.reduce(
                    (total, network) => total + network.members,
                    0,
                  )}
                </p>
              </div>
              <MapPin className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Networks Table */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Registered Networks</CardTitle>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Network
          </Button>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-4 py-3 text-left font-medium text-gray-900 dark:text-white">
                    Network
                  </th>
                  <th className="px-4 py-3 text-left font-medium text-gray-900 dark:text-white">
                    Type
                  </th>
                  <th className="px-4 py-3 text-left font-medium text-gray-900 dark:text-white">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left font-medium text-gray-900 dark:text-white">
                    Members
                  </th>
                  <th className="px-4 py-3 text-left font-medium text-gray-900 dark:text-white">
                    Location
                  </th>
                  <th className="px-4 py-3 text-left font-medium text-gray-900 dark:text-white">
                    Created
                  </th>
                  <th className="px-4 py-3 text-left font-medium text-gray-900 dark:text-white">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredNetworks.map((network) => (
                  <tr
                    key={network.id}
                    className="border-b border-gray-100 hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800"
                  >
                    <td className="px-4 py-4">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {network.name}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {network.description}
                        </p>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <Badge className={getTypeColor(network.type)}>
                        {network.type.charAt(0).toUpperCase() +
                          network.type.slice(1)}
                      </Badge>
                    </td>
                    <td className="px-4 py-4">
                      <Badge className={getStatusColor(network.status)}>
                        {network.status.charAt(0).toUpperCase() +
                          network.status.slice(1)}
                      </Badge>
                    </td>
                    <td className="px-4 py-4 text-gray-900 dark:text-white">
                      {network.members}
                    </td>
                    <td className="px-4 py-4 text-gray-900 dark:text-white">
                      {network.location}
                    </td>
                    <td className="px-4 py-4 text-gray-900 dark:text-white">
                      {new Date(network.createdDate).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredNetworks.length === 0 && (
            <div className="py-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                No networks found matching your criteria.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
