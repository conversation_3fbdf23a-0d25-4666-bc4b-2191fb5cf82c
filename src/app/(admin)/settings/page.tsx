"use client";
import React, { useState } from "react";
import {
  HiO<PERSON><PERSON>U<PERSON>,
  <PERSON><PERSON><PERSON>lineBell,
  HiOutlineShieldCheck,
  <PERSON><PERSON>utlineCog,
  <PERSON><PERSON>utlineEye,
  HiOutlineEyeSlash,
} from "react-icons/hi2";

export default function SettingsPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    updates: true,
    alerts: true,
  });

  return (
    <div className="mt-4 w-full">
      <h1 className="mb-6 text-3xl font-extrabold tracking-tight text-primary">
        Settings
      </h1>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Account Settings */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
              <HiOutlineUser className="size-6 text-primary" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              Account Settings
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Full Name
              </label>
              <input
                type="text"
                defaultValue="John Doe"
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              />
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Email
              </label>
              <input
                type="email"
                defaultValue="<EMAIL>"
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              />
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  defaultValue="••••••••"
                  className="w-full rounded border border-gray-300 bg-white px-3 py-2 pr-10 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  {showPassword ? (
                    <HiOutlineEyeSlash className="size-5" />
                  ) : (
                    <HiOutlineEye className="size-5" />
                  )}
                </button>
              </div>
            </div>

            <button className="w-full rounded bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
              Update Account
            </button>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
              <HiOutlineBell className="size-6 text-primary" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              Notifications
            </h2>
          </div>

          <div className="space-y-4">
            {Object.entries(notifications).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="text-sm font-medium capitalize text-gray-700 dark:text-gray-300">
                  {key} Notifications
                </span>
                <button
                  onClick={() =>
                    setNotifications((prev) => ({
                      ...prev,
                      [key]: !prev[key as keyof typeof notifications],
                    }))
                  }
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    value ? "bg-primary" : "bg-gray-300 dark:bg-gray-600"
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      value ? "translate-x-6" : "translate-x-1"
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Security Settings */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
              <HiOutlineShieldCheck className="size-6 text-primary" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              Security
            </h2>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Two-Factor Authentication
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Add an extra layer of security
                </p>
              </div>
              <button className="rounded bg-primary px-3 py-1 text-xs text-white transition hover:bg-primary/80">
                Enable
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Login History
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  View recent login activity
                </p>
              </div>
              <button className="rounded bg-gray-200 px-3 py-1 text-xs text-gray-700 transition hover:bg-gray-300 dark:bg-dark-2 dark:text-white dark:hover:bg-dark-3">
                View
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Session Management
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Manage active sessions
                </p>
              </div>
              <button className="rounded bg-gray-200 px-3 py-1 text-xs text-gray-700 transition hover:bg-gray-300 dark:bg-dark-2 dark:text-white dark:hover:bg-dark-3">
                Manage
              </button>
            </div>
          </div>
        </div>

        {/* Preferences */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
              <HiOutlineCog className="size-6 text-primary" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              Preferences
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Language
              </label>
              <select className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white">
                <option>English</option>
                <option>French</option>
                <option>Spanish</option>
              </select>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Time Zone
              </label>
              <select className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white">
                <option>UTC+00:00</option>
                <option>UTC+01:00</option>
                <option>UTC+02:00</option>
              </select>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Date Format
              </label>
              <select className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white">
                <option>MM/DD/YYYY</option>
                <option>DD/MM/YYYY</option>
                <option>YYYY-MM-DD</option>
              </select>
            </div>

            <button className="w-full rounded bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
              Save Preferences
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
