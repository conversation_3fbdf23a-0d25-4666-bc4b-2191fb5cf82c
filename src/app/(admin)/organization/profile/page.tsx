"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineBuildingOffice2,
  Hi<PERSON>utlineUser,
  HiOutlineEnvelope,
  HiOutlinePhone,
  HiOutlineMapPin,
  HiOutlineCalendar,
  HiOutlineDocumentText,
  HiOutlineEye,
  HiOutlinePencil,
} from "react-icons/hi2";
import { getNGOById, INgo } from "@/services/ngo.services";
import { getCurrentUser } from "@/utils/auth.utils";

// Use INgo interface from services instead of custom interface

export default function OrganizationProfilePage() {
  const [profile, setProfile] = useState<INgo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Get current user to extract NGO ID
        const currentUser = await getCurrentUser();
        if (!currentUser || !currentUser.ngoId) {
          setError("No organization found for current user");
          return;
        }

        // Get access token
        const token = localStorage.getItem("accessToken");
        if (!token) {
          setError("Authentication required");
          return;
        }

        // Fetch NGO data by ID
        const response = await getNGOById(currentUser.ngoId, token);
        
        if (response.status === "success") {
          // Show profile data regardless of approval status
          setProfile(response.data);
        } else {
          setError(response.message || "Failed to fetch organization data");
        }
      } catch (error: any) {
        console.error("Error fetching profile:", error);
        setError(error.message || "Failed to load organization profile");
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
      case "active":
      case "completed":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "pending":
      case "financial":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "documentation":
        return "text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-400";
      case "rejected":
      case "expired":
      case "suspended":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            {error.includes("pending") || error.includes("rejected") ? "Organization Pending Approval" : "Organization Not Found"}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {error}
          </p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            Organization Not Found
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Unable to load organization profile.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Organization Profile
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            View and manage your organization information
          </p>
          {/* Status Alert for non-approved organizations */}
          {profile.approvedStatus !== "approved" && (
            <div className={`mt-3 rounded-lg p-3 ${
              profile.approvedStatus === "pending" 
                ? "bg-yellow-50 border border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200"
                : "bg-red-50 border border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200"
            }`}>
              <p className="text-sm font-medium">
                {profile.approvedStatus === "pending" 
                  ? "⏳ Your organization registration is pending approval. You can view your information below, but some features may be limited until approved."
                  : "❌ Your organization registration has been rejected. Please contact support for more information."
                }
              </p>
            </div>
          )}
        </div>
        <button className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/90">
          <HiOutlinePencil className="h-4 w-4" />
          Edit Profile
        </button>
      </div>

      {/* Status Cards */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Registration Status
              </p>
              <p className="text-lg font-semibold capitalize text-gray-900 dark:text-white">
                {profile.approvedStatus || "pending"}
              </p>
            </div>
            <span
              className={`rounded-full px-2 py-1 text-xs ${getStatusColor(profile.approvedStatus || "pending")}`}
            >
              {profile.approvedStatus || "pending"}
            </span>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Approval Stage
              </p>
              <p className="text-lg font-semibold capitalize text-gray-900 dark:text-white">
                {profile.approvementStage || "financial"}
              </p>
            </div>
            <span
              className={`rounded-full px-2 py-1 text-xs ${getStatusColor(profile.approvementStage || "financial")}`}
            >
              {profile.approvementStage || "financial"}
            </span>
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Organization Type
              </p>
              <p className="text-lg font-semibold capitalize text-gray-900 dark:text-white">
                {profile.type}
              </p>
            </div>
            <HiOutlineBuildingOffice2 className="h-5 w-5 text-blue-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Date Founded
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {new Date(profile.dateFounded).toLocaleDateString()}
              </p>
            </div>
            <HiOutlineCalendar className="h-5 w-5 text-gray-600" />
          </div>
        </div>
      </div>

      {/* Organization Details */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Basic Information */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineBuildingOffice2 className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Basic Information
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Organization Name
              </label>
              <p className="mt-1 text-gray-900 dark:text-white">
                {profile.name}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Organization Initials
              </label>
              <p className="mt-1 text-gray-900 dark:text-white">
                {profile.initials}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Headquarters Address
              </label>
              <p className="mt-1 text-gray-900 dark:text-white">
                {profile.headquartersAddress}
              </p>
            </div>

            <div className="grid gap-4 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Date Founded
                </label>
                <p className="mt-1 text-gray-900 dark:text-white">
                  {new Date(profile.dateFounded).toLocaleDateString()}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Date Approved by GOM
                </label>
                <p className="mt-1 text-gray-900 dark:text-white">
                  {profile.dateApprovedByGOM ? new Date(profile.dateApprovedByGOM).toLocaleDateString() : "N/A"}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Leadership Information */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineUser className="h-5 w-5 text-green-600" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Leadership Information
            </h2>
          </div>

          <div className="space-y-4">
            {/* Chairperson */}
            <div>
              <h3 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Chairperson
              </h3>
              <div className="space-y-2 rounded-lg border border-gray-200 p-3 dark:border-gray-700">
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400">
                    Name
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {profile.chairpersonName}
                  </p>
                </div>
                <div className="grid gap-2 sm:grid-cols-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 dark:text-gray-400">
                      Email
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {profile.chairpersonEmail}
                    </p>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-600 dark:text-gray-400">
                      Phone
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {profile.chairpersonPhone}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Chief Executive */}
            <div>
              <h3 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Chief Executive
              </h3>
              <div className="space-y-2 rounded-lg border border-gray-200 p-3 dark:border-gray-700">
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400">
                    Name
                  </label>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">
                    {profile.chiefExecutiveName}
                  </p>
                </div>
                <div className="grid gap-2 sm:grid-cols-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 dark:text-gray-400">
                      Email
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {profile.chiefExecutiveEmail}
                    </p>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-600 dark:text-gray-400">
                      Phone
                    </label>
                    <p className="mt-1 text-sm text-gray-900 dark:text-white">
                      {profile.chiefExecutivePhone}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Organization Statements */}
      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-2">
          <HiOutlineDocumentText className="h-5 w-5 text-purple-600" />
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Organization Statements
          </h2>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Mission Statement
            </label>
            <p className="mt-1 text-gray-900 dark:text-white">
              {profile.missionStatement || "No mission statement provided"}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Vision Statement
            </label>
            <p className="mt-1 text-gray-900 dark:text-white">
              {profile.visionStatement || "No vision statement provided"}
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-2">
          <HiOutlineEye className="h-5 w-5 text-blue-600" />
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Quick Actions
          </h2>
        </div>

        <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-4">
          {profile.approvedStatus === "approved" && (
            <button className="rounded-lg bg-blue-600 px-4 py-2 text-white transition hover:bg-blue-700">
              View Public Profile
            </button>
          )}
          {profile.approvedStatus === "approved" && (
            <button className="rounded-lg bg-green-600 px-4 py-2 text-white transition hover:bg-green-700">
              Download Certificate
            </button>
          )}
          <button className="rounded-lg bg-purple-600 px-4 py-2 text-white transition hover:bg-purple-700">
            Update Documents
          </button>
          {profile.approvedStatus !== "approved" && (
            <button className="rounded-lg bg-orange-600 px-4 py-2 text-white transition hover:bg-orange-700">
              Contact Support
            </button>
          )}
          {profile.approvedStatus === "approved" && (
            <button className="rounded-lg bg-orange-600 px-4 py-2 text-white transition hover:bg-orange-700">
              Request Changes
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
