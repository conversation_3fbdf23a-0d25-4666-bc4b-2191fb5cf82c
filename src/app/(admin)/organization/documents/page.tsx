"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineDocumentText,
  HiOutlineArrowUpTray,
  HiOutlineArrowDownTray,
  HiOutlineEye,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineCalendar,
  HiOutlineDocument,
  HiOutlineFolder,
  HiOutlinePlus,
} from "react-icons/hi2";

interface OrganizationDocument {
  id: string;
  title: string;
  type:
    | "constitution"
    | "annual_report"
    | "financial_statement"
    | "certificate"
    | "policy"
    | "other";
  status: "approved" | "pending" | "rejected" | "expired";
  uploadDate: string;
  expiryDate?: string;
  fileSize: string;
  fileType: string;
  uploadedBy: string;
  description: string;
  version: string;
  lastReviewed?: string;
}

export default function OrganizationDocumentsPage() {
  const [documents, setDocuments] = useState<OrganizationDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const mockDocuments: OrganizationDocument[] = [
          {
            id: "1",
            title: "Organization Constitution",
            type: "constitution",
            status: "approved",
            uploadDate: "2024-01-15",
            expiryDate: "2025-01-15",
            fileSize: "2.5 MB",
            fileType: "PDF",
            uploadedBy: "John Mhango",
            description: "Official constitution document of the organization",
            version: "2.1",
            lastReviewed: "2024-10-15",
          },
          {
            id: "2",
            title: "Annual Report 2023",
            type: "annual_report",
            status: "approved",
            uploadDate: "2024-03-20",
            fileSize: "5.2 MB",
            fileType: "PDF",
            uploadedBy: "Mary Banda",
            description: "Annual report for the year 2023",
            version: "1.0",
            lastReviewed: "2024-04-10",
          },
          {
            id: "3",
            title: "Financial Statement Q3 2024",
            type: "financial_statement",
            status: "pending",
            uploadDate: "2024-10-15",
            fileSize: "1.8 MB",
            fileType: "PDF",
            uploadedBy: "James Phiri",
            description: "Third quarter financial statement for 2024",
            version: "1.0",
          },
          {
            id: "4",
            title: "Registration Certificate",
            type: "certificate",
            status: "approved",
            uploadDate: "2020-06-20",
            expiryDate: "2025-06-20",
            fileSize: "1.2 MB",
            fileType: "PDF",
            uploadedBy: "System Admin",
            description: "Official registration certificate from GOM",
            version: "1.0",
            lastReviewed: "2024-09-15",
          },
          {
            id: "5",
            title: "Human Resources Policy",
            type: "policy",
            status: "approved",
            uploadDate: "2023-08-10",
            fileSize: "3.1 MB",
            fileType: "PDF",
            uploadedBy: "Sarah Mvula",
            description: "HR policies and procedures manual",
            version: "3.2",
            lastReviewed: "2024-07-20",
          },
          {
            id: "6",
            title: "Project Proposal Template",
            type: "other",
            status: "rejected",
            uploadDate: "2024-10-10",
            fileSize: "0.8 MB",
            fileType: "DOCX",
            uploadedBy: "David Kachale",
            description: "Standard template for project proposals",
            version: "1.0",
          },
        ];

        setDocuments(mockDocuments);
      } catch (error) {
        console.error("Error fetching documents:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "pending":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "rejected":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      case "expired":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <HiOutlineCheckCircle className="h-4 w-4 text-green-600" />;
      case "pending":
        return <HiOutlineClock className="h-4 w-4 text-yellow-600" />;
      case "rejected":
        return <HiOutlineXCircle className="h-4 w-4 text-red-600" />;
      case "expired":
        return <HiOutlineXCircle className="h-4 w-4 text-red-600" />;
      default:
        return <HiOutlineClock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case "constitution":
        return <HiOutlineDocument className="h-4 w-4 text-blue-600" />;
      case "annual_report":
        return <HiOutlineDocumentText className="h-4 w-4 text-green-600" />;
      case "financial_statement":
        return <HiOutlineDocument className="h-4 w-4 text-purple-600" />;
      case "certificate":
        return <HiOutlineDocument className="h-4 w-4 text-orange-600" />;
      case "policy":
        return <HiOutlineDocument className="h-4 w-4 text-indigo-600" />;
      default:
        return <HiOutlineFolder className="h-4 w-4 text-gray-600" />;
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    switch (type) {
      case "constitution":
        return "Constitution";
      case "annual_report":
        return "Annual Report";
      case "financial_statement":
        return "Financial Statement";
      case "certificate":
        return "Certificate";
      case "policy":
        return "Policy";
      default:
        return "Other";
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Organization Documents
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your organization&apos;s documents and certificates
          </p>
        </div>
        <button className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
          <HiOutlinePlus className="h-4 w-4" />
          Upload Document
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Documents
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {documents.length}
              </p>
            </div>
            <HiOutlineDocumentText className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Approved
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {documents.filter((d) => d.status === "approved").length}
              </p>
            </div>
            <HiOutlineCheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Review
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {documents.filter((d) => d.status === "pending").length}
              </p>
            </div>
            <HiOutlineClock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Expiring Soon
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {
                  documents.filter(
                    (d) =>
                      d.expiryDate &&
                      new Date(d.expiryDate) <
                        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                  ).length
                }
              </p>
            </div>
            <HiOutlineCalendar className="h-8 w-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Documents Table */}
      <div className="rounded-lg bg-white shadow-sm dark:bg-gray-800">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Document Library
          </h2>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Document
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Type
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Upload Date
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Expiry Date
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {documents.map((document) => (
                  <tr
                    key={document.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
                          {getDocumentTypeIcon(document.type)}
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {document.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {document.fileSize} • {document.fileType}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {getDocumentTypeLabel(document.type)}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(document.status)}
                        <span
                          className={`rounded-full px-2 py-1 text-xs ${getStatusColor(document.status)}`}
                        >
                          {document.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {document.uploadDate}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {document.expiryDate || "N/A"}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlineEye className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlineArrowDownTray className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlinePencil className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-red-400 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-300">
                          <HiOutlineTrash className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Document Categories */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Required Documents */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineDocumentText className="h-5 w-5 text-red-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Required Documents
            </h3>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between rounded-lg border border-gray-200 p-3 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <HiOutlineDocument className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Organization Constitution
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Required for registration
                  </p>
                </div>
              </div>
              <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-600 dark:bg-green-900 dark:text-green-400">
                Uploaded
              </span>
            </div>

            <div className="flex items-center justify-between rounded-lg border border-gray-200 p-3 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <HiOutlineDocument className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Annual Report
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Due by November 15
                  </p>
                </div>
              </div>
              <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-600 dark:bg-green-900 dark:text-green-400">
                Uploaded
              </span>
            </div>

            <div className="flex items-center justify-between rounded-lg border border-gray-200 p-3 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <HiOutlineDocument className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Financial Statements
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Quarterly reports required
                  </p>
                </div>
              </div>
              <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400">
                Pending
              </span>
            </div>
          </div>
        </div>

        {/* Document Upload Guidelines */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineArrowUpTray className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Upload Guidelines
            </h3>
          </div>

          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Supported formats: PDF, DOC, DOCX, JPG, PNG
              </p>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Maximum file size: 10 MB per document
              </p>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Ensure documents are clear and legible
              </p>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
              <p className="text-gray-600 dark:text-gray-400">
                Include version numbers for updated documents
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
