"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineFolderOpen,
  HiOutlineCalendar,
  HiOutlineCurrencyDollar,
  HiOutlineMapPin,
  HiOutlineUserGroup,
  HiOutlinePlus,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineEye,
  HiOutlineCheckCircle,
  HiOutlineClock,
  HiOutlineXCircle,
} from "react-icons/hi2";
import { getNgoProjectsByNgo, createNgoProject, updateNgoProject, deleteNgoProject, INgoProject } from "@/services/projects";
import { getCurrentUser } from "@/utils/auth.utils";

interface Project {
  id: string;
  title: string;
  description: string;
  sector: string;
  district: string;
  budget: number;
  startDate: string;
  endDate: string;
  status: "Upcoming" | "In Progress" | "Completed";
  beneficiaries?: number;
  teamSize?: number;
  fundingSource?: string;
  lastUpdated: string;
  imageUrl?: string;
  urls?: string[];
}

const mapApiToProject = (apiProject: INgoProject): Project => {
  const startDate = apiProject.startDate ? new Date(apiProject.startDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];
  const endDate = apiProject.endDate ? new Date(apiProject.endDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];

  // Determine status based on dates
  let status: "Upcoming" | "In Progress" | "Completed" = "Upcoming";
  const now = new Date();
  const projectStart = new Date(startDate);
  const projectEnd = new Date(endDate);

  if (now < projectStart) {
    status = "Upcoming";
  } else if (now >= projectStart && now <= projectEnd) {
    status = "In Progress";
  } else {
    status = "Completed";
  }

  return {
    id: apiProject._id,
    title: apiProject.name,
    description: apiProject.description,
    sector: "General", // Not in API model, could be added later
    district: apiProject.district || "Unknown",
    budget: apiProject.budget || 0,
    startDate,
    endDate,
    status,
    beneficiaries: 0, // Not in API model
    teamSize: 0, // Not in API model
    fundingSource: "Unknown", // Not in API model
    lastUpdated: apiProject.updatedAt ? new Date(apiProject.updatedAt).toLocaleDateString() : new Date().toLocaleDateString(),
    imageUrl: apiProject.imageUrl || "",
    urls: apiProject.urls || []
  };
};

export default function OrganizationProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newProject, setNewProject] = useState({
    title: "",
    description: "",
    budget: 0,
    district: "",
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    imageFile: null as File | null,
    urls: ""
  });

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Get current user to extract NGO ID
        const currentUser = await getCurrentUser();
        if (!currentUser || !currentUser.ngoId) {
          setError("No organization found for current user");
          return;
        }

        // Get access token
        const token = localStorage.getItem("accessToken");
        if (!token) {
          setError("Authentication required");
          return;
        }

        // Fetch NGO-specific projects
        const response = await getNgoProjectsByNgo(currentUser.ngoId, token);
        
        if (Array.isArray(response)) {
          const mappedProjects = response.map(mapApiToProject);
          setProjects(mappedProjects);
        } else if (response && typeof response === 'object' && 'data' in response) {
          const apiResponse = response as { data: INgoProject[] };
          if (Array.isArray(apiResponse.data)) {
            const mappedProjects = apiResponse.data.map(mapApiToProject);
            setProjects(mappedProjects);
          } else {
            setProjects([]);
          }
        } else {
          setProjects([]);
        }
      } catch (error: any) {
        console.error("Error fetching projects:", error);
        setError(error.message || "Failed to load projects");
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "In Progress":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "Completed":
        return "text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-400";
      case "Upcoming":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "In Progress":
        return <HiOutlineClock className="h-4 w-4 text-green-600" />;
      case "Completed":
        return <HiOutlineCheckCircle className="h-4 w-4 text-blue-600" />;
      case "Upcoming":
        return <HiOutlineCalendar className="h-4 w-4 text-yellow-600" />;
      default:
        return <HiOutlineClock className="h-4 w-4 text-gray-600" />;
    }
  };

  // Handle project creation
  const handleAddProject = async () => {
    if (!newProject.title || !newProject.description || !newProject.imageFile) {
      setError("Please fill all required fields and upload an image.");
      return;
    }
    
    try {
      const currentUser = await getCurrentUser();
      const token = localStorage.getItem("accessToken");
      
      if (!currentUser?.ngoId || !token) {
        setError("Authentication required");
        return;
      }

      const formData = new FormData();
      formData.append("ngo", currentUser.ngoId);
      formData.append("name", newProject.title);
      formData.append("description", newProject.description);
      formData.append("budget", newProject.budget.toString());
      formData.append("district", newProject.district);
      formData.append("startDate", newProject.startDate);
      formData.append("endDate", newProject.endDate);
      formData.append("urls", newProject.urls);
      if (newProject.imageFile) {
        formData.append("image", newProject.imageFile);
      }

      const response = await createNgoProject(formData, token);
      
      if (response && typeof response === 'object' && '_id' in response) {
        // Direct project response
        const mappedProject = mapApiToProject(response as INgoProject);
        setProjects([...projects, mappedProject]);
        setNewProject({
          title: "",
          description: "",
          budget: 0,
          district: "",
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          imageFile: null,
          urls: ""
        });
        setIsModalOpen(false);
        setError(""); // Clear any previous errors
      } else if (response && typeof response === 'object' && 'data' in response) {
        // Wrapped response
        const apiResponse = response as { data: INgoProject };
        const mappedProject = mapApiToProject(apiResponse.data);
        setProjects([...projects, mappedProject]);
        setNewProject({
          title: "",
          description: "",
          budget: 0,
          district: "",
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          imageFile: null,
          urls: ""
        });
        setIsModalOpen(false);
        setError(""); // Clear any previous errors
      } else {
        setError("Failed to create project. Please check all required fields.");
      }
    } catch (error: any) {
      console.error("Project creation error:", error);
      setError(error.message || error.response?.data?.message || "Failed to create project");
    }
  };

  // Handle project deletion
  const handleDeleteProject = async (projectId: string) => {
    if (!window.confirm("Are you sure you want to delete this project?")) return;
    
    try {
      const token = localStorage.getItem("accessToken");
      if (!token) {
        setError("Authentication required");
        return;
      }

      await deleteNgoProject(projectId, token);
      setProjects(projects.filter(p => p.id !== projectId));
    } catch (error: any) {
      setError(error.message || "Failed to delete project");
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            Error Loading Projects
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="rounded-lg bg-primary px-4 py-2 text-white hover:bg-primary/80"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Organization Projects
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage and track your organization&apos;s projects
          </p>
        </div>
        <button 
          onClick={() => setIsModalOpen(true)}
          className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/80"
        >
          <HiOutlinePlus className="h-4 w-4" />
          Add Project
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Projects
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {projects.length}
              </p>
            </div>
            <HiOutlineFolderOpen className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Projects
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {projects.filter((p) => p.status === "In Progress").length}
              </p>
            </div>
            <HiOutlineCheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Beneficiaries
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {projects
                  .reduce((sum, p) => sum + (p.beneficiaries || 0), 0)
                  .toLocaleString()}
              </p>
            </div>
            <HiOutlineUserGroup className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Budget
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                MK{" "}
                {projects
                  .reduce((sum, p) => sum + p.budget, 0)
                  .toLocaleString()}
              </p>
            </div>
            <HiOutlineCurrencyDollar className="h-8 w-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid gap-6 lg:grid-cols-2">
        {projects.map((project) => (
          <div
            key={project.id}
            className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800"
          >
            {project.imageUrl && (
              <img src={project.imageUrl} alt={project.title} className="w-full h-48 object-cover rounded-t-lg" />
            )}
            <div className="mb-4 flex items-start justify-between pt-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {project.title}
                </h3>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  {project.description}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(project.status)}
                <span
                  className={`rounded-full px-2 py-1 text-xs ${getStatusColor(project.status)}`}
                >
                  {project.status}
                </span>
              </div>
            </div>

            <div className="grid gap-3 text-sm">
              <div className="flex items-center gap-2">
                <HiOutlineMapPin className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">
                  Sector:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {project.sector}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <HiOutlineMapPin className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">
                  District:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {project.district}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <HiOutlineCurrencyDollar className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">
                  Budget:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  MK {project.budget.toLocaleString()}
                </span>
              </div>
              {project.beneficiaries && project.beneficiaries > 0 && (
                <div className="flex items-center gap-2">
                  <HiOutlineUserGroup className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600 dark:text-gray-400">
                    Beneficiaries:
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {project.beneficiaries?.toLocaleString()}
                  </span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <HiOutlineCalendar className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">
                  Duration:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {project.startDate} - {project.endDate}
                </span>
              </div>
            </div>

            <div className="mt-4 flex items-center justify-between">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Last updated: {project.lastUpdated}
              </div>
              <div className="flex items-center gap-2">
                <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                  <HiOutlineEye className="h-4 w-4" />
                </button>
                <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                  <HiOutlinePencil className="h-4 w-4" />
                </button>
                <button 
                  onClick={() => handleDeleteProject(project.id)}
                  className="rounded p-1 text-red-400 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-300"
                >
                  <HiOutlineTrash className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Project Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
            <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
              Add New Project
            </h3>
            
            {error && (
              <div className="mb-4 rounded-lg bg-red-50 p-4 text-red-700 dark:bg-red-900/20 dark:text-red-400">
                {error}
              </div>
            )}
            
            <div className="space-y-4">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Project Title
                </label>
                <input
                  type="text"
                  placeholder="Enter project title"
                  value={newProject.title}
                  onChange={(e) => setNewProject({ ...newProject, title: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Description
                </label>
                <textarea
                  rows={3}
                  placeholder="Enter project description"
                  value={newProject.description}
                  onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Budget (MWK)
                </label>
                <input
                  type="number"
                  placeholder="Enter project budget"
                  value={newProject.budget}
                  onChange={(e) => setNewProject({ ...newProject, budget: parseInt(e.target.value) || 0 })}
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  District
                </label>
                <input
                  type="text"
                  placeholder="Enter project district"
                  value={newProject.district}
                  onChange={(e) => setNewProject({ ...newProject, district: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={newProject.startDate}
                    onChange={(e) => setNewProject({ ...newProject, startDate: e.target.value })}
                    className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div className="flex-1">
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={newProject.endDate}
                    onChange={(e) => setNewProject({ ...newProject, endDate: e.target.value })}
                    className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Project URL (Optional)
                </label>
                <input
                  type="url"
                  placeholder="Enter project URL"
                  value={newProject.urls}
                  onChange={(e) => setNewProject({ ...newProject, urls: e.target.value })}
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Project Image
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => setNewProject({ ...newProject, imageFile: e.target.files ? e.target.files[0] : null })}
                  className="w-full rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition-colors focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end gap-4">
              <button
                onClick={() => setIsModalOpen(false)}
                className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleAddProject}
                className="rounded-md bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-70"
                disabled={!newProject.title || !newProject.description || !newProject.imageFile}
              >
                Add Project
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
