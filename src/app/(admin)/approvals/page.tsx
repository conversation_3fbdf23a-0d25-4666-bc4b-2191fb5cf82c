// "use client";

// import ApprovalModal from "@/components/approvalModal";
// import { getNGOsByApprovementStage, INgo } from "@/services/ngo.services";
// import React, { useEffect, useState } from "react";
// import { Download } from "lucide-react";
// import {
//   HiOutlineBuildingOffice,
//   HiOutlineCalendar,
//   HiOutlineClock,
//   HiOutlineDocumentText,
//   HiOutlineExclamationTriangle,
//   HiOutlineEye,
// } from "react-icons/hi2";

// type Payment = {
//   _id: string;
//   ngoName: string;
//   contactPerson: string;
//   contactEmail: string;
//   processingFeeUrl: string;
//   registrationFeeUrl: string;
//   ngoType: string;
//   status: string;
//   createdAt: string;
// };

// function NgoToPayment(ngo: INgo[]): Payment[] {
//   return ngo.map((ngo) => ({
//     _id: ngo._id,
//     ngoName: ngo.name,
//     contactPerson: ngo.chairpersonName || ngo.chiefExecutiveName || "N/A",
//     contactEmail: ngo.chairpersonEmail || ngo.chiefExecutiveEmail || "N/A",
//     processingFeeUrl: ngo.processingFeeUrl || "",
//     registrationFeeUrl: ngo.registrationFeeUrl || "",
//     ngoType: ngo.type,
//     status: ngo.approvementStage || ngo.approveStatus || "pending",
//     createdAt: ngo.createdAt || "",
//   }));
// }
// export default function RegistrationPayments() {
//   const [searchQuery, setSearchQuery] = useState("");
//   const [payments, setPayments] = useState<Payment[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<string | null>(null);
//   const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

//   useEffect(() => {
//     const fetchPayments = async () => {
//       try {
//         const token = localStorage.getItem("accessToken");
//         if (token) {
//           const response = await getNGOsByApprovementStage(stage, token);

//           if (response.status === "success") {
//             setPayments(NgoToPayment(response.data));
//             console.log("Payments:", payments);
//           } else {
//             setError(response.message);
//           }
//         }
//       } catch (err: any) {
//         setError(err.message);
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchPayments();
//   }, []);

//   const user = JSON.parse(localStorage.getItem("user") || "{}");
//   const role = user.role.name;

//   let stage = "";

//   if (role === "staff_admin" || role === "staff_registry") {
//     stage = "documentation";
//   } else if (role === "finance_officer") {
//     stage = "financial";
//   }

//   // Switch labels based on role
//   const sectionTitle =
//     role === "staff_admin" || role === "staff_registry"
//       ? "Pending Document Approvals"
//       : "Pending Proof of Payment Uploads";

//   const sectionDescription =
//     role === "staff_admin" || role === "staff_registry"
//       ? "Track and manage pending document approvals from NGOs"
//       : "Track and manage pending payment proof uploads from NGOs";

//   // Dynamic labels
//   const approvedLabel =
//     role === "staff_admin" || role === "staff_registry"
//       ? "Approved Documents"
//       : "Approved Payments";

//   const declinedLabel =
//     role === "staff_admin" || role === "staff_registry"
//       ? "Declined Documents"
//       : "Declined Payments";

//   // Dynamic filter values
//   const approvedStatuses =
//     role === "staff_admin" || role === "staff_registry"
//       ? ["completed"]
//       : ["documentation", "completed"];

//   // get payment by id
//   const getPaymentById = (id: string) => {
//     const payment = payments.find((p) => p._id === id);
//     setSelectedPayment(payment || null);
//   };

//   // filter payments in financial status
//   const filterPaymentsByStatus = (payments: any[], status: string[]) => {
//     return payments.filter((payment) => status.includes(payment.status));
//   };

//   // filter payments in financial status and days
//   const filterPaymentsByStatusAndDays = (
//     payments: Payment[],
//     statuses: string[],
//     days: number,
//   ) => {
//     const cutoff = Date.now() - days * 24 * 60 * 60 * 1000;
//     return payments.filter(
//       (p) =>
//         p.status &&
//         statuses.includes(p.status) &&
//         new Date(p.createdAt).getTime() < cutoff,
//     );
//   };

//   const getStatusBadge = (status: string | undefined) => {
//     switch (status) {
//       case "financial":
//         return (
//           <span className="inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
//             Pending
//           </span>
//         );
//       case "payment-declined":
//         return (
//           <span className="inline-flex items-center rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-200">
//             Overdue
//           </span>
//         );
//       default:
//         return null;
//     }
//   };

//   const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
//     setSearchQuery(event.target.value);
//   };

//   const handleDownload = () => {
//     // Implement download functionality
//   };

//   if (loading) {
//     return <div>Loading...</div>;
//   }

//   if (error) {
//     return <div>Error: {error}</div>;
//   }

//   return (
//     <div className="space-y-6">
//       <div>
//         <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
//           {sectionTitle}
//         </h1>
//         <p className="text-gray-600 dark:text-gray-400">{sectionDescription}</p>
//       </div>

//       <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
//         {/* Pending */}
//         <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
//           <div className="flex items-center justify-between">
//             <div>
//               <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
//                 Pending Approval
//               </p>
//               <p className="text-2xl font-bold text-gray-900 dark:text-white">
//                 {payments.length}
//               </p>
//             </div>
//             <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
//               <HiOutlineDocumentText className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
//             </div>
//           </div>
//         </div>

//         {/* Approved */}
//         <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
//           <div className="flex items-center justify-between">
//             <div>
//               <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
//                 {approvedLabel}
//               </p>
//               <p className="text-2xl font-bold text-gray-900 dark:text-white">
//                 {filterPaymentsByStatus(payments, approvedStatuses).length}
//               </p>
//             </div>
//           </div>
//         </div>

//         {/* Overdue */}
//         <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
//           <div className="flex items-center justify-between">
//             <div>
//               <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
//                 Overdue Approval
//               </p>
//               <p className="text-2xl font-bold text-gray-900 dark:text-white">
//                 {filterPaymentsByStatusAndDays(payments, [stage], 2).length}
//               </p>
//             </div>
//             <div className="rounded-full bg-red-100 p-3 dark:bg-red-900">
//               <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
//             </div>
//           </div>
//         </div>

//         {/* Declined */}
//         <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
//           <div className="flex items-center justify-between">
//             <div>
//               <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
//                 {declinedLabel}
//               </p>
//               <p className="text-2xl font-bold text-gray-900 dark:text-white">
//                 {
//                   filterPaymentsByStatus(payments, ["documents-declined"])
//                     .length
//                 }
//               </p>
//             </div>
//             <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
//               <HiOutlineClock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
//             </div>
//           </div>
//         </div>
//       </div>

//       <div className="flex flex-col gap-4">
//         <div className="flex items-center justify-between">
//           <input
//             type="text"
//             placeholder="Search..."
//             value={searchQuery}
//             onChange={handleSearch}
//             className="rounded-md border border-gray-300 px-4 py-2"
//           />
//           <button
//             onClick={handleDownload}
//             className="rounded-md bg-blue-500 px-4 py-2 text-white"
//           >
//             <Download className="mr-2 inline-block" />
//             Download
//           </button>
//         </div>
//         <div className="rounded-xl bg-white shadow-lg dark:bg-gray-dark">
//           <div className="overflow-x-auto">
//             <table className="w-full">
//               <thead>
//                 <tr className="border-b border-gray-200 dark:border-gray-700">
//                   <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
//                     NGO Details
//                   </th>
//                   <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
//                     Days Since Registration
//                   </th>
//                   <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
//                     Approval Status
//                   </th>
//                   <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
//                     Actions
//                   </th>
//                 </tr>
//               </thead>
//               <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
//                 {payments.map((payment) => (
//                   <tr key={payment._id}>
//                     <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
//                       <div className="flex items-center space-x-3">
//                         <div className="flex-shrink-0">
//                           <HiOutlineBuildingOffice className="h-8 w-8 text-gray-400" />
//                         </div>
//                         <div>
//                           <div className="text-sm font-medium text-gray-900 dark:text-white">
//                             {payment.ngoName}
//                           </div>
//                           <div className="text-xs text-gray-500 dark:text-gray-400">
//                             {payment.contactPerson}
//                           </div>
//                           <div className="text-xs text-gray-500 dark:text-gray-400">
//                             {payment.contactEmail}
//                           </div>
//                         </div>
//                       </div>
//                     </td>
//                     <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
//                       <div className="flex items-center space-x-2">
//                         <HiOutlineCalendar className="h-4 w-4 text-gray-400" />
//                         <div>
//                           <div className="text-sm font-medium text-gray-900 dark:text-white">
//                             {new Date(payment.createdAt).toLocaleDateString()}
//                           </div>
//                           <div className="text-xs text-red-600 dark:text-red-400">
//                             {Math.floor(
//                               (Date.now() -
//                                 new Date(payment.createdAt).getTime()) /
//                                 (1000 * 60 * 60 * 24),
//                             )}{" "}
//                             days due
//                           </div>
//                         </div>
//                       </div>
//                     </td>
//                     <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
//                       {getStatusBadge(payment.status)}
//                     </td>
//                     <td className="px-6 py-4">
//                       <div className="flex items-center space-x-2">
//                         <button
//                           className="rounded-md bg-blue-100 p-2 text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-400 dark:hover:bg-blue-800"
//                           onClick={() => getPaymentById(payment._id)}
//                         >
//                           <HiOutlineEye className="h-4 w-4" />
//                         </button>
//                       </div>
//                     </td>
//                   </tr>
//                 ))}
//               </tbody>
//             </table>
//           </div>
//         </div>
//         {selectedPayment && (
//           <ApprovalModal
//             payment={selectedPayment}
//             onClose={() => setSelectedPayment(null)}
//           />
//         )}
//       </div>
//     </div>
//   );
// }

"use client";

import ApprovalModal from "@/components/approvalModal";
import { getNGOsByApprovementStage, INgo } from "@/services/ngo.services";
import React, { useEffect, useState } from "react";
import { Download } from "lucide-react";
import {
  HiOutlineBuildingOffice,
  HiOutlineCalendar,
  HiOutlineClock,
  HiOutlineDocumentText,
  HiOutlineExclamationTriangle,
  HiOutlineEye,
} from "react-icons/hi2";

// Type for Payment (finance officer view)
type Payment = {
  _id: string;
  ngoName: string;
  contactPerson: string;
  contactEmail: string;
  processingFeeUrl: string;
  registrationFeeUrl: string;
  ngoType: string;
  status: string;
  createdAt: string;
};

// Convert NGO -> Payment for finance officer
function NgoToPayment(ngo: INgo[]): Payment[] {
  return ngo.map((ngo) => ({
    _id: ngo._id,
    ngoName: ngo.name,
    contactPerson: ngo.chairpersonName || ngo.chiefExecutiveName || "N/A",
    contactEmail: ngo.chairpersonEmail || ngo.chiefExecutiveEmail || "N/A",
    processingFeeUrl: ngo.processingFeeUrl || "",
    registrationFeeUrl: ngo.registrationFeeUrl || "",
    ngoType: ngo.type,
    status:
      ngo.approveStatus === "rejected"
        ? ngo.approveStatus
        : ngo.approvementStage || "pending",
    createdAt: ngo.createdAt || "",
  }));
}

export default function RegistrationPayments() {
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const role = user?.role?.name ?? "";

  // Set approval stage depending on role
  let stage = "";
  if (role === "staff_admin" || role === "staff_registry") {
    stage = "documentation";
  } else if (role === "finance_officer") {
    stage = "financial";
  }

  const [searchQuery, setSearchQuery] = useState("");
  const [ngos, setNgos] = useState<INgo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState<INgo | Payment | null>(null);

  useEffect(() => {
    const fetchNgos = async () => {
      try {
        const token = localStorage.getItem("accessToken");
        if (token && stage) {
          const response = await getNGOsByApprovementStage(stage, token);

          if (response.status === "success") {
            setNgos(response.data);
          } else {
            setError(response.message);
          }
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchNgos();
  }, [stage]);

  // Choose view data: Payments for finance, NGOs for staff
  const displayData: (INgo | Payment)[] =
    role === "finance_officer" ? NgoToPayment(ngos) : ngos;

  // Switch labels based on role
  const sectionTitle =
    role === "staff_admin" || role === "staff_registry"
      ? "Pending Document Approvals"
      : "Pending Proof of Payment Uploads";

  const sectionDescription =
    role === "staff_admin" || role === "staff_registry"
      ? "Track and manage pending document approvals from NGOs"
      : "Track and manage pending payment proof uploads from NGOs";

  const approvedLabel =
    role === "staff_admin" || role === "staff_registry"
      ? "Approved Documents"
      : "Approved Payments";

  const declinedLabel =
    role === "staff_admin" || role === "staff_registry"
      ? "Declined Documents"
      : "Declined Payments";

  const approvedStatuses =
    role === "staff_admin" || role === "staff_registry"
      ? ["completed"]
      : ["documentation", "completed"];

  const getStatusBadge = (status: string | undefined) => {
    switch (status) {
      case "financial":
      case "documentation":
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Pending
          </span>
        );
      case "rejected":
        return (
          <span className="inline-flex items-center rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-200">
            Declined
          </span>
        );
      case "completed":
        return (
          <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
            Approved
          </span>
        );
      default:
        return null;
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Helper: filter items older than X days at a given stage
  function filterByStageAndDays(
    data: (INgo | Payment)[],
    stage: string,
    days: number,
  ) {
    const cutoff = Date.now() - days * 24 * 60 * 60 * 1000;
    return data.filter((d: any) => {
      return d.status === stage && new Date(d.createdAt).getTime() < cutoff;
    });
  }
  const handleDownload = () => {
    // Implement download functionality
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          {sectionTitle}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">{sectionDescription}</p>
      </div>

      {/* Stats Section */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {/* Pending */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Approval
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {displayData.length}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
              <HiOutlineDocumentText className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        {/* Approved */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {approvedLabel}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {
                  displayData.filter((d: any) =>
                    approvedStatuses.includes(d.status),
                  ).length
                }
              </p>
            </div>
          </div>
        </div>

        {/* Overdue */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Overdue Approval
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {filterByStageAndDays(displayData, stage, 2).length}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900">
              <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>

        {/* Declined */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {declinedLabel}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {
                  displayData.filter((d: any) =>
                    ["documents-declined", "payment-declined"].includes(
                      d.status,
                    ),
                  ).length
                }
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <HiOutlineClock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <input
            type="text"
            placeholder="Search..."
            value={searchQuery}
            onChange={handleSearch}
            className="rounded-md border border-gray-300 px-4 py-2"
          />
          <button
            onClick={handleDownload}
            className="rounded-md bg-blue-500 px-4 py-2 text-white"
          >
            <Download className="mr-2 inline-block" />
            Download
          </button>
        </div>

        <div className="rounded-xl bg-white shadow-lg dark:bg-gray-dark">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                    NGO Details
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                    Days Since Registration
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                    Approval Status
                  </th>
                  <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {displayData.map((item: any) => (
                  <tr key={item._id}>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-3">
                        <HiOutlineBuildingOffice className="h-8 w-8 text-gray-400" />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {role === "finance_officer"
                              ? item.ngoName
                              : item.name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {role === "finance_officer"
                              ? item.contactPerson
                              : item.chairpersonName || item.chiefExecutiveName}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {role === "finance_officer"
                              ? item.contactEmail
                              : item.chairpersonEmail ||
                                item.chiefExecutiveEmail}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-2">
                        <HiOutlineCalendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {new Date(item.createdAt).toLocaleDateString()}
                          </div>
                          <div className="text-xs text-red-600 dark:text-red-400">
                            {Math.floor(
                              (Date.now() -
                                new Date(item.createdAt).getTime()) /
                                (1000 * 60 * 60 * 24),
                            )}{" "}
                            days due
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {getStatusBadge(item.status)}
                    </td>
                    <td className="px-6 py-4">
                      <button
                        className="rounded-md bg-blue-100 p-2 text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-400 dark:hover:bg-blue-800"
                        onClick={() => setSelectedItem(item)}
                      >
                        <HiOutlineEye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {selectedItem && (
          <ApprovalModal
            data={selectedItem}
            onClose={() => setSelectedItem(null)}
          />
        )}
      </div>
    </div>
  );
}
