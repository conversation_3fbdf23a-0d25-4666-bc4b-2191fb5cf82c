"use client";
import React, { useState } from "react";
import {
  HiOutlineDocument,
  HiOutlineArrowDownTray,
  HiOutlineEye,
} from "react-icons/hi2";

const ngoDocuments = [
  {
    id: 1,
    title: "Annual Report 2023",
    organization: "Local Org 1",
    documentType: "Report",
    uploadDate: "2024-01-15",
    fileSize: "2.5 MB",
    status: "Approved",
  },
  {
    id: 2,
    title: "Financial Statement Q4",
    organization: "Local Org 2",
    documentType: "Financial",
    uploadDate: "2024-01-10",
    fileSize: "1.8 MB",
    status: "Pending",
  },
  {
    id: 3,
    title: "Project Proposal - Education",
    organization: "International Org 1",
    documentType: "Proposal",
    uploadDate: "2024-01-08",
    fileSize: "3.2 MB",
    status: "Approved",
  },
  {
    id: 4,
    title: "Compliance Certificate",
    organization: "Local Org 3",
    documentType: "Certificate",
    uploadDate: "2024-01-05",
    fileSize: "0.5 MB",
    status: "Approved",
  },
  {
    id: 5,
    title: "Strategic Plan 2024-2026",
    organization: "International Org 2",
    documentType: "Plan",
    uploadDate: "2024-01-03",
    fileSize: "4.1 MB",
    status: "Under Review",
  },
  {
    id: 6,
    title: "Audit Report 2023",
    organization: "Local Org 1",
    documentType: "Audit",
    uploadDate: "2023-12-28",
    fileSize: "2.9 MB",
    status: "Approved",
  },
];

export default function NGODocumentsPage() {
  const [search, setSearch] = useState("");
  const filtered = ngoDocuments.filter((doc) =>
    Object.values(doc).some((val) =>
      String(val).toLowerCase().includes(search.toLowerCase()),
    ),
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Approved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "Pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "Under Review":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "Rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  return (
    <div className="mt-4 w-full">
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {filtered.map((doc) => (
          <div
            key={doc.id}
            className="flex flex-col rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark"
          >
            <div className="mb-4 flex size-16 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
              <HiOutlineDocument className="size-10 text-primary" />
            </div>
            <div className="mb-2 text-xl font-bold text-dark dark:text-white">
              {doc.title}
            </div>
            <div className="mb-2 text-sm text-gray-500 dark:text-gray-400">
              Organization: {doc.organization}
            </div>
            <div className="mb-2 text-sm text-gray-500 dark:text-gray-400">
              Type: {doc.documentType}
            </div>
            <div className="mb-2 text-sm text-gray-500 dark:text-gray-400">
              Upload Date: {doc.uploadDate}
            </div>
            <div className="mb-2 text-sm text-gray-500 dark:text-gray-400">
              File Size: {doc.fileSize}
            </div>
            <div className="mb-4">
              <span
                className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(doc.status)}`}
              >
                {doc.status}
              </span>
            </div>
            <div className="mt-auto flex gap-2">
              <button className="flex flex-1 items-center justify-center gap-1 rounded bg-primary px-3 py-2 text-white transition hover:bg-primary/80">
                <HiOutlineEye className="size-4" />
                View
              </button>
              <button className="flex flex-1 items-center justify-center gap-1 rounded bg-gray-200 px-3 py-2 text-gray-700 transition hover:bg-gray-300 dark:bg-dark-2 dark:text-white dark:hover:bg-dark-3">
                <HiOutlineArrowDownTray className="size-4" />
                Download
              </button>
            </div>
          </div>
        ))}
        {filtered.length === 0 && (
          <div className="col-span-full rounded bg-gray-100 p-8 text-center text-gray-500 dark:bg-dark-2 dark:text-gray-400">
            No documents found.
          </div>
        )}
      </div>
    </div>
  );
}
