"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineDocumentText,
  HiOutlineArrowUpTray,
  HiOutlineArrowDownTray,
  HiOutlineEye,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineCalendar,
  HiOutlineDocument,
  HiOutlineFolder,
  HiOutlinePlus,
  HiOutlineArrowPath,
} from "react-icons/hi2";

interface Document {
  id: string;
  title: string;
  type: string;
  status: string;
  uploadDate: string;
  expiryDate?: string;
  fileSize: string;
  fileType: string;
  url?: string;
}

export default function OrganizationDocumentsPage() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get token and user info from localStorage
  const token = localStorage.getItem("accessToken");
  const userStr = localStorage.getItem("user");
  const user = userStr ? JSON.parse(userStr) : null;
  const ngoId = user?.ngoId;

  useEffect(() => {
    const fetchRealDocuments = async () => {
      setLoading(true);
      
      console.log("=== REAL ORGANIZATION DOCS DEBUG ===");
      console.log("Token:", token ? "Present" : "Missing");
      console.log("User:", user);
      console.log("NGO ID:", ngoId);
      
      if (!token || !ngoId) {
        console.error("Missing authentication data");
        setError("Please log in as an NGO admin to view documents");
        setLoading(false);
        return;
      }

      try {
        // Fetch real NGO data
        const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3009"}/api/ngos/${ngoId}`;
        console.log("API URL:", apiUrl);
        
        const response = await fetch(apiUrl, {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${token}`,
            "Content-Type": "application/json"
          }
        });
        
        console.log("Response status:", response.status);
        
        if (!response.ok) {
          throw new Error(`API call failed: ${response.status}`);
        }
        
        const data = await response.json();
        console.log("NGO API response:", data);
        
        if (data.status === "success" && data.data) {
          const ngo = data.data;
          
          // Map NGO data to documents with actual URLs
          const realDocuments: Document[] = [
            {
              id: "constitution",
              title: "Constitution",
              type: "constitution",
              status: ngo.constitutionUrl ? "approved" : "missing",
              uploadDate: ngo.updatedAt || ngo.createdAt || "2024-01-01",
              fileSize: ngo.constitutionUrl ? "2.3 MB" : "0 MB",
              fileType: "PDF",
              url: ngo.constitutionUrl || "",
            },
            {
              id: "minutes_first_meeting",
              title: "Minutes of First Meeting",
              type: "minutes",
              status: ngo.minutesOfFirstMeetingUrl ? "approved" : "missing",
              uploadDate: ngo.updatedAt || ngo.createdAt || "2024-01-01",
              fileSize: ngo.minutesOfFirstMeetingUrl ? "1.8 MB" : "0 MB",
              fileType: "PDF",
              url: ngo.minutesOfFirstMeetingUrl || "",
            },
            {
              id: "registrar_certificate",
              title: "Certificate from Registrar General",
              type: "certificate",
              status: ngo.certificateFromRegistrarGeneralUrl ? "approved" : "missing",
              uploadDate: ngo.updatedAt || ngo.createdAt || "2024-01-01",
              fileSize: ngo.certificateFromRegistrarGeneralUrl ? "3.1 MB" : "0 MB",
              fileType: "PDF",
              url: ngo.certificateFromRegistrarGeneralUrl || "",
            },
            {
              id: "sworn_affidavit",
              title: "Sworn-in Affidavit",
              type: "affidavit",
              status: ngo.swornInAffidavitUrl ? "approved" : "missing",
              uploadDate: ngo.updatedAt || ngo.createdAt || "2024-01-01",
              fileSize: ngo.swornInAffidavitUrl ? "856 KB" : "0 MB",
              fileType: "PDF",
              url: ngo.swornInAffidavitUrl || "",
            },
            {
              id: "registration_fee",
              title: "Registration Fee Receipt",
              type: "financial",
              status: ngo.registrationFeeUrl ? "approved" : "missing",
              uploadDate: ngo.updatedAt || ngo.createdAt || "2024-01-01",
              fileSize: ngo.registrationFeeUrl ? "1.2 MB" : "0 MB",
              fileType: "PDF",
              url: ngo.registrationFeeUrl || "",
            },
            {
              id: "processing_fee",
              title: "Processing Fee Receipt",
              type: "financial",
              status: ngo.processingFeeUrl ? "approved" : "missing",
              uploadDate: ngo.updatedAt || ngo.createdAt || "2024-01-01",
              fileSize: ngo.processingFeeUrl ? "1.1 MB" : "0 MB",
              fileType: "PDF",
              url: ngo.processingFeeUrl || "",
            },
          ];
          
          console.log("Mapped real documents:", realDocuments);
          setDocuments(realDocuments);
        } else {
          throw new Error("Invalid API response format");
        }
      } catch (error) {
        console.error("Error fetching real documents:", error);
        const errorMessage = error instanceof Error ? error.message : "Unknown error";
        setError(`Failed to load documents: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    fetchRealDocuments();
  }, [token, ngoId]);

  // Document action handlers
  const handleViewDocument = (document: Document) => {
    console.log("Viewing document:", document);
    
    if (document.status === "approved" && document.url) {
      // Open the actual document URL
      window.open(document.url, '_blank');
    } else if (document.status === "missing") {
      alert(`Document "${document.title}" has not been uploaded yet.\nStatus: ${document.status}`);
    } else {
      alert(`Document "${document.title}" is not available for viewing.\nStatus: ${document.status}\nURL: ${document.url || 'Not available'}`);
    }
  };

  const handleDownloadDocument = (doc: Document) => {
    console.log("Downloading document:", doc);
    
    if (doc.status === "approved" && doc.url) {
      // Download the actual document
      const link = document.createElement('a');
      link.href = doc.url;
      link.download = `${doc.title}.${doc.fileType.toLowerCase()}`;
      link.click();
    } else if (doc.status === "missing") {
      alert(`Document "${doc.title}" has not been uploaded yet.\nStatus: ${doc.status}`);
    } else {
      alert(`Document "${doc.title}" is not available for download.\nStatus: ${doc.status}\nURL: ${doc.url || 'Not available'}`);
    }
  };


  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <HiOutlineCheckCircle className="h-4 w-4 text-green-600" />;
      case "pending":
        return <HiOutlineClock className="h-4 w-4 text-yellow-600" />;
      case "rejected":
        return <HiOutlineXCircle className="h-4 w-4 text-red-600" />;
      default:
        return <HiOutlineClock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case "certificate":
        return <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />;
      case "report":
        return <HiOutlineDocument className="h-5 w-5 text-green-600" />;
      case "financial":
        return <HiOutlineDocumentText className="h-5 w-5 text-purple-600" />;
      case "minutes":
        return <HiOutlineDocument className="h-5 w-5 text-orange-600" />;
      case "proposal":
        return <HiOutlineDocumentText className="h-5 w-5 text-indigo-600" />;
      default:
        return <HiOutlineDocument className="h-5 w-5 text-gray-600" />;
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    switch (type) {
      case "certificate":
        return "Certificate";
      case "report":
        return "Report";
      case "financial":
        return "Financial";
      case "minutes":
        return "Minutes";
      case "proposal":
        return "Proposal";
      default:
        return "Document";
    }
  };

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-gray-600 dark:text-gray-400">
            Loading your organization documents...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 p-3">
            <HiOutlineXCircle className="h-6 w-6 text-red-600" />
          </div>
          <p className="text-red-600 font-medium mb-2">Error Loading Documents</p>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            {error}
          </p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Organization Documents
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            View and manage your organization&apos;s submitted documents
          </p>
          {/* Debug info */}
          <div className="mt-2 text-xs text-gray-500 bg-gray-50 dark:bg-gray-800 p-2 rounded">
            <strong>Debug:</strong> User: {user?.email || 'Not logged in'} | 
            NGO ID: {ngoId || 'Missing'} | 
            Token: {token ? 'Present' : 'Missing'} | 
            Documents: {documents.length}
          </div>
        </div>
        <div className="flex gap-2">
          <button 
            onClick={() => window.location.reload()}
            className="flex items-center gap-2 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition hover:bg-gray-50"
          >
            <HiOutlineArrowPath className="h-4 w-4" />
            Refresh
          </button>
          <button className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
            <HiOutlinePlus className="h-4 w-4" />
            Upload Document
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Documents
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {documents.length}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <HiOutlineFolder className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Approved
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {documents.filter((d) => d.status === "approved").length}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <HiOutlineCheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Review
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {documents.filter((d) => d.status === "pending").length}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
              <HiOutlineClock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>


      </div>

      {/* Documents Table */}
      <div className="rounded-lg bg-white shadow-sm dark:bg-gray-800">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Document Library
          </h2>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Document
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Type
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Upload Date
                  </th>

                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {documents.map((document) => (
                  <tr
                    key={document.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
                          {getDocumentTypeIcon(document.type)}
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {document.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {document.fileSize} • {document.fileType}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {getDocumentTypeLabel(document.type)}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(document.status)}
                        <span
                          className={`rounded-full px-2 py-1 text-xs ${getStatusColor(document.status)}`}
                        >
                          {document.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {document.uploadDate}
                      </span>
                    </td>

                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        <button 
                          onClick={() => handleViewDocument(document)}
                          className="rounded p-1 text-blue-400 hover:bg-blue-100 hover:text-blue-600 dark:hover:bg-blue-700 dark:hover:text-blue-300"
                          title="View Document"
                        >
                          <HiOutlineEye className="h-4 w-4" />
                        </button>
                        <button 
                          onClick={() => handleDownloadDocument(document)}
                          className="rounded p-1 text-green-400 hover:bg-green-100 hover:text-green-600 dark:hover:bg-green-700 dark:hover:text-green-300"
                          title="Download Document"
                        >
                          <HiOutlineArrowDownTray className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Document Categories */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Required Documents */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineDocument className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Required Documents
            </h3>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between rounded-lg border border-gray-200 p-3 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <HiOutlineCheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Registration Certificate
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Valid until 2025-01-15
                  </p>
                </div>
              </div>
              <span className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-300">
                Uploaded
              </span>
            </div>

            <div className="flex items-center justify-between rounded-lg border border-gray-200 p-3 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <HiOutlineClock className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Annual Report
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Due by 2024-03-31
                  </p>
                </div>
              </div>
              <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                Pending
              </span>
            </div>

            <div className="flex items-center justify-between rounded-lg border border-gray-200 p-3 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <HiOutlineXCircle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Financial Statement
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Due by 2024-04-30
                  </p>
                </div>
              </div>
              <span className="rounded-full bg-red-100 px-2 py-1 text-xs text-red-800 dark:bg-red-900 dark:text-red-300">
                Missing
              </span>
            </div>
          </div>
        </div>

        {/* Document Upload Guidelines */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineArrowUpTray className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Upload Guidelines
            </h3>
          </div>

          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
              <p>Supported formats: PDF, DOC, DOCX, JPG, PNG</p>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
              <p>Maximum file size: 10MB per document</p>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
              <p>Ensure documents are clear and legible</p>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-1 h-2 w-2 rounded-full bg-blue-600"></div>
              <p>Include all required signatures and stamps</p>
            </div>
          </div>

          <div className="mt-4 rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
            <p className="text-sm text-blue-800 dark:text-blue-300">
              <strong>Note:</strong> All uploaded documents will be reviewed by
              our team within 3-5 business days.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
