"use client";
import React, { useState } from "react";
import { HiOutlineBuildingOffice2 } from "react-icons/hi2";
import {
  FaStethoscope,
  FaGraduationCap,
  FaTractor,
  FaLeaf,
  FaPaperclip,
  FaMicrophone,
  FaSmile,
  FaEllipsisV,
  FaChevronLeft,
  FaCheck,
  FaUserPlus,
} from "react-icons/fa";
import { Modal } from "@/components/ui/modal";
import { useRouter } from "next/navigation";
import { HiOutlineSearch } from "react-icons/hi";

// Updated network data structure with joined status
const sectorNetworks = [
  {
    id: 1,
    name: "Health Network",
    type: "Health",
    region: "North",
    dateFormed: "2012-03-10",
    coordinator: "<PERSON>",
    isFeatured: true,
    lastMessage: "Meeting scheduled for tomorrow at 2pm",
    lastMessageTime: "10:30 AM",
    unreadCount: 3,
    avatar: "H",
    joined: true, // User is a member
  },
  {
    id: 2,
    name: "Education Alliance",
    type: "Education",
    region: "South",
    dateFormed: "2015-07-22",
    coordinator: "<PERSON>",
    isFeatured: false,
    lastMessage: "Please review the new curriculum draft",
    lastMessageTime: "Yesterday",
    unreadCount: 0,
    avatar: "E",
    joined: false, // User is not a member
  },
  {
    id: 3,
    name: "Agri Development",
    type: "Agriculture",
    region: "East",
    dateFormed: "2018-11-05",
    coordinator: "Mike Brown",
    isFeatured: false,
    lastMessage: "Harvest report attached",
    lastMessageTime: "Monday",
    unreadCount: 5,
    avatar: "A",
    joined: false, // User is not a member
  },
  {
    id: 4,
    name: "Eco Warriors",
    type: "Environment",
    region: "West",
    dateFormed: "2020-03-15",
    coordinator: "Sarah Wilson",
    isFeatured: false,
    lastMessage: "Tree planting initiative approved!",
    lastMessageTime: "3/15/24",
    unreadCount: 0,
    avatar: "E",
    joined: true, // User is a member
  },
];

const sampleMessages = [
  {
    id: 1,
    sender: "John Doe",
    text: "Hi team, just checking in on the vaccination drive progress",
    time: "10:30 AM",
    isMe: false,
  },
  {
    id: 2,
    sender: "You",
    text: "We've reached 75% of our target so far",
    time: "10:32 AM",
    isMe: true,
  },
  {
    id: 3,
    sender: "John Doe",
    text: "That's great news! Can you share the detailed report?",
    time: "10:33 AM",
    isMe: false,
  },
  {
    id: 4,
    sender: "You",
    text: "Sure, I'll email it to you by EOD",
    time: "10:35 AM",
    isMe: true,
  },
];

export default function SectorNetworkPage() {
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [networkSubmitted, setNetworkSubmitted] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedNetwork, setSelectedNetwork] = useState<any>(null);
  const [newMessage, setNewMessage] = useState("");
  const [messages, setMessages] = useState(sampleMessages);
  const [form, setForm] = useState({
    name: "",
    type: "",
    region: "",
    dateFormed: "",
    coordinator: "",
    description: "",
    contactEmail: "",
    contactPhone: "",
  });
  const [joinRequests, setJoinRequests] = useState<number[]>([]);
  const [showJoinSuccess, setShowJoinSuccess] = useState(false);

  const filtered = sectorNetworks.filter((net) =>
    Object.values(net).some((val) =>
      String(val).toLowerCase().includes(search.toLowerCase()),
    ),
  );

  const getNetworkTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "health":
        return <FaStethoscope className="text-white" />;
      case "education":
        return <FaGraduationCap className="text-white" />;
      case "agriculture":
        return <FaTractor className="text-white" />;
      case "environment":
        return <FaLeaf className="text-white" />;
      default:
        return <HiOutlineBuildingOffice2 className="text-white" />;
    }
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    setForm({
      ...form,
      [e.target.name]: e.target.value,
    });
  };

  const handleNext = () => {
    if (currentStep === 1 && isStep1Valid) {
      setCurrentStep(2);
    }
  };

  const handlePrevious = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    }
  };

  const isStep1Valid = form.name && form.type && form.region && form.dateFormed;
  const isStep2Valid =
    form.coordinator && form.contactEmail && form.contactPhone;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Sector network submitted:", form);
    setNetworkSubmitted(true);
    setShowAddModal(false);
    setCurrentStep(1);
    setTimeout(() => setNetworkSubmitted(false), 5000);
    setForm({
      name: "",
      type: "",
      region: "",
      dateFormed: "",
      coordinator: "",
      description: "",
      contactEmail: "",
      contactPhone: "",
    });
  };

  const openAddModal = () => {
    setShowAddModal(true);
  };

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const newMsg = {
        id: messages.length + 1,
        sender: "You",
        text: newMessage,
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        isMe: true,
      };
      setMessages([...messages, newMsg]);
      setNewMessage("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleJoinRequest = (networkId: number) => {
    // In a real app, this would send a request to the server
    setJoinRequests([...joinRequests, networkId]);
    setShowJoinSuccess(true);
    setTimeout(() => setShowJoinSuccess(false), 3000);
  };

  const canAccessNetwork = (network: any) => {
    return network.joined || joinRequests.includes(network.id);
  };

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-lg dark:bg-gray-dark">
      <div className="flex h-screen bg-gray-50 font-sans">
        {/* Sidebar - Conversation List */}
        <div className={`${selectedNetwork ? 'hidden md:block' : 'block'} w-full md:w-1/3 lg:w-1/4 bg-white border-r border-gray-200 shadow-sm`}>
          <div className="p-6 bg-gradient-to-r from-blue-600 to-blue-500 text-white">
            <h1 className="text-2xl font-bold">Sector Networks</h1>
            <p className="text-sm opacity-90 mt-1">Collaborate with your sector peers</p>
          </div>
          
          {/* Search Bar */}
          <div className="p-4 bg-white border-b border-gray-100">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <HiOutlineSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="Search networks..."
                className="w-full bg-gray-50 py-2 pl-10 pr-4 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Network List */}
          <div className="overflow-y-auto h-[calc(100%-130px)]">
            {filtered.map((net) => (
              <div
                key={net.id}
                className={`flex items-center p-4 border-b border-gray-100 cursor-pointer transition-colors duration-200 ${selectedNetwork?.id === net.id ? 'bg-blue-50' : 'hover:bg-gray-50'}`}
                onClick={() => setSelectedNetwork(net)}
              >
                <div className={`flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center text-white shadow-md ${
                  net.type === 'Health' ? 'bg-red-400' : 
                  net.type === 'Education' ? 'bg-blue-400' : 
                  net.type === 'Agriculture' ? 'bg-green-500' : 
                  'bg-purple-400'
                }`}>
                  {getNetworkTypeIcon(net.type)}
                </div>
                <div className="ml-4 flex-1 overflow-hidden">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-semibold text-gray-900 truncate">{net.name}</h3>
                    <span className="text-xs text-gray-500 whitespace-nowrap ml-2">{net.lastMessageTime}</span>
                  </div>
                  <p className="text-xs text-gray-500 truncate mt-1">{net.type} • {net.region}</p>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-xs text-gray-600 truncate">
                      {net.joined ? net.lastMessage : "Join to view messages"}
                    </p>
                    {net.unreadCount > 0 && net.joined && (
                      <span className="bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center ml-2">
                        {net.unreadCount}
                      </span>
                    )}
                    {!net.joined && !joinRequests.includes(net.id) && (
                      <span className="text-xs text-gray-400">Not joined</span>
                    )}
                    {joinRequests.includes(net.id) && (
                      <span className="text-xs text-blue-500">Request sent</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Add Network Button */}
          <div className="absolute bottom-6 right-6">
            <button
              onClick={openAddModal}
              className="bg-gradient-to-r from-blue-500 to-blue-500 text-white rounded-xl p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <HiOutlineBuildingOffice2 className="text-xl" />
            </button>
          </div>
        </div>

        {/* Chat Area */}
        {selectedNetwork ? (
          canAccessNetwork(selectedNetwork) ? (
            <div className="flex flex-col w-full md:w-2/3 lg:w-3/4 bg-white">
              {/* Chat Header */}
              <div className="flex items-center p-4 border-b border-gray-200 bg-white shadow-sm">
                <button 
                  onClick={() => setSelectedNetwork(null)} 
                  className="md:hidden mr-3 text-gray-500 hover:text-gray-700"
                >
                  <FaChevronLeft />
                </button>
                <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center text-white shadow-md ${
                  selectedNetwork.type === 'Health' ? 'bg-red-400' : 
                  selectedNetwork.type === 'Education' ? 'bg-blue-400' : 
                  selectedNetwork.type === 'Agriculture' ? 'bg-green-500' : 
                  'bg-purple-400'
                }`}>
                  {getNetworkTypeIcon(selectedNetwork.type)}
                </div>
                <div className="ml-4 flex-1">
                  <h2 className="font-semibold text-gray-800">{selectedNetwork.name}</h2>
                  <div className="flex items-center">
                    <p className="text-xs text-gray-500">{selectedNetwork.type} Network</p>
                    <span className="mx-2 text-gray-300">•</span>
                    <p className="text-xs text-gray-500">{selectedNetwork.region} Region</p>
                  </div>
                </div>
                <button className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100">
                  <FaEllipsisV />
                </button>
              </div>

              {/* Messages Area */}
              <div 
                className="flex-1 p-6 overflow-y-auto bg-gray-50"
                style={{ backgroundImage: "linear-gradient(rgba(245, 245, 245, 0.8), rgba(245, 245, 245, 0.8))" }}
              >
                <div className="max-w-3xl mx-auto space-y-4">
                  {messages.map((msg) => (
                    <div
                      key={msg.id}
                      className={`flex ${msg.isMe ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-xs md:max-w-md rounded-xl px-4 py-3 shadow-sm ${msg.isMe ? 'bg-blue-100 rounded-tr-none' : 'bg-white rounded-tl-none border border-gray-200'}`}
                      >
                        {!msg.isMe && (
                          <p className="text-xs font-semibold text-blue-600 mb-1">{msg.sender}</p>
                        )}
                        <p className={`text-sm ${msg.isMe ? 'text-gray-800' : 'text-gray-700'}`}>{msg.text}</p>
                        <div className="flex justify-end items-center mt-1 space-x-1">
                          <span className="text-xs text-gray-500">{msg.time}</span>
                          {msg.isMe && <FaCheck className="text-xs text-gray-500" />}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Message Input */}
              <div className="bg-white p-4 border-t border-gray-200 shadow-sm">
                <div className="flex items-center bg-gray-50 rounded-xl px-4 py-2">
                  <button className="p-2 text-gray-500 hover:text-blue-500 rounded-full hover:bg-gray-100">
                    <FaSmile />
                  </button>
                  <button className="p-2 text-gray-500 hover:text-blue-500 rounded-full hover:bg-gray-100">
                    <FaPaperclip />
                  </button>
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type your message..."
                    className="flex-1 py-2 px-4 mx-2 bg-transparent focus:outline-none"
                  />
                  <button
                    onClick={handleSendMessage}
                    className={`p-2 rounded-full ${newMessage.trim() ? 'text-blue-500 hover:bg-blue-50' : 'text-gray-500 hover:bg-gray-100'}`}
                  >
                    {newMessage.trim() ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                    ) : (
                      <FaMicrophone />
                    )}
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center w-full md:w-2/3 lg:w-3/4 bg-gray-50 p-6">
              <div className="max-w-md text-center bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FaUserPlus className="text-blue-500 text-2xl" />
                </div>
                <h2 className="text-xl font-bold text-gray-800 mb-3">Join {selectedNetwork.name}</h2>
                <p className="text-gray-600 mb-6">
                  You need to be a member of this network to view and participate in discussions.
                  Request to join and the network coordinator will review your request.
                </p>
                {joinRequests.includes(selectedNetwork.id) ? (
                  <div className="bg-blue-50 text-blue-700 px-4 py-3 rounded-lg">
                    Your join request has been sent to the coordinator
                  </div>
                ) : (
                  <button
                    onClick={() => handleJoinRequest(selectedNetwork.id)}
                    className="bg-gradient-to-r from-blue-500 to-blue-500 text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all duration-300 w-full"
                  >
                    Request to Join
                  </button>
                )}
                <button
                  onClick={() => setSelectedNetwork(null)}
                  className="mt-4 text-blue-500 hover:text-blue-700 text-sm font-medium"
                >
                  Back to networks
                </button>
              </div>
            </div>
          )
        ) : (
          <div className="hidden md:flex flex-col items-center justify-center w-2/3 lg:w-3/4 bg-gradient-to-br from-gray-50 to-gray-100">
            <div className="text-center p-8 max-w-md bg-white rounded-xl shadow-lg border border-gray-100">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-md">
                <HiOutlineBuildingOffice2 className="text-white text-3xl" />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-3">Sector Networks</h2>
              <p className="text-gray-600 mb-6">
                Connect with professionals in your sector. Select a network to view discussions, share resources, and collaborate on initiatives.
              </p>
              <button
                onClick={openAddModal}
                className="bg-gradient-to-r from-blue-500 to-blue-500 text-white px-8 py-3 rounded-xl hover:shadow-lg transition-all duration-300"
              >
                Create New Network
              </button>
            </div>
          </div>
        )}

        {/* Add Sector Network Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          title="Create New Network"
        >
          <div className="p-6">
            {/* Step Indicator */}
            <div className="mb-8">
              <div className="flex items-center">
                <div className="flex-1 h-1 rounded-full bg-gray-200">
                  <div 
                    className={`h-1 rounded-full ${currentStep >= 1 ? 'bg-blue-500' : 'bg-gray-200'}`}
                    style={{ width: currentStep === 2 ? '100%' : '50%' }}
                  ></div>
                </div>
                <div className={`mx-2 flex flex-col items-center ${currentStep >= 1 ? 'text-blue-500' : 'text-gray-400'}`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? 'bg-blue-100' : 'bg-gray-100'}`}>
                    {currentStep >= 1 ? (
                      <FaCheck className="text-blue-500 text-sm" />
                    ) : (
                      <span>1</span>
                    )}
                  </div>
                  <span className="text-xs mt-1">Basic Info</span>
                </div>
                <div className="flex-1 h-1 rounded-full bg-gray-200">
                  <div 
                    className={`h-1 rounded-full ${currentStep === 2 ? 'bg-blue-500' : 'bg-gray-200'}`}
                    style={{ width: currentStep === 2 ? '100%' : '0%' }}
                  ></div>
                </div>
                <div className={`ml-2 flex flex-col items-center ${currentStep === 2 ? 'text-blue-500' : 'text-gray-400'}`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 2 ? 'bg-blue-100' : 'bg-gray-100'}`}>
                    <span>2</span>
                  </div>
                  <span className="text-xs mt-1">Contact Info</span>
                </div>
              </div>
            </div>

            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-5">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Network Details
                </h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Network Name
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={form.name}
                    onChange={handleChange}
                    placeholder="e.g. Health Professionals Network"
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sector Type
                    </label>
                    <select
                      name="type"
                      value={form.type}
                      onChange={handleChange}
                      className="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                      required
                    >
                      <option value="">Select sector</option>
                      <option value="Health">Health</option>
                      <option value="Education">Education</option>
                      <option value="Agriculture">Agriculture</option>
                      <option value="Environment">Environment</option>
                      <option value="Human Rights">Human Rights</option>
                      <option value="Women Empowerment">Women Empowerment</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Region
                    </label>
                    <select
                      name="region"
                      value={form.region}
                      onChange={handleChange}
                      className="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                      required
                    >
                      <option value="">Select region</option>
                      <option value="North">North</option>
                      <option value="South">South</option>
                      <option value="East">East</option>
                      <option value="West">West</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Formation Date
                  </label>
                  <input
                    type="date"
                    name="dateFormed"
                    value={form.dateFormed}
                    onChange={handleChange}
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                    required
                  />
                </div>

                <div className="flex justify-between pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddModal(false)}
                    className="px-6 py-2.5 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleNext}
                    disabled={!isStep1Valid}
                    className={`px-6 py-2.5 rounded-lg text-white transition ${isStep1Valid ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-300 cursor-not-allowed'}`}
                  >
                    Continue
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Contact Information */}
            {currentStep === 2 && (
              <form onSubmit={handleSubmit} className="space-y-5">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Contact Information
                </h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Coordinator Name
                  </label>
                  <input
                    type="text"
                    name="coordinator"
                    value={form.coordinator}
                    onChange={handleChange}
                    placeholder="e.g. John Smith"
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contact Email
                    </label>
                    <input
                      type="email"
                      name="contactEmail"
                      value={form.contactEmail}
                      onChange={handleChange}
                      placeholder="e.g. <EMAIL>"
                      className="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contact Phone
                    </label>
                    <input
                      type="tel"
                      name="contactPhone"
                      value={form.contactPhone}
                      onChange={handleChange}
                      placeholder="e.g. +1234567890"
                      className="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description (Optional)
                  </label>
                  <textarea
                    name="description"
                    value={form.description}
                    onChange={handleChange}
                    placeholder="Brief description about the network..."
                    className="w-full px-4 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                    rows={3}
                  />
                </div>

                <div className="flex justify-between pt-4">
                  <button
                    type="button"
                    onClick={handlePrevious}
                    className="px-6 py-2.5 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition"
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={!isStep2Valid}
                    className={`px-6 py-2.5 rounded-lg text-white transition ${isStep2Valid ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-300 cursor-not-allowed'}`}
                  >
                    Create Network
                  </button>
                </div>
              </form>
            )}
          </div>
        </Modal>

        {/* Success Messages */}
        {networkSubmitted && (
          <div className="fixed bottom-6 right-6 z-50">
            <div className="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center animate-fade-in-up">
              <div className="bg-white bg-opacity-20 rounded-full p-1 mr-3">
                <FaCheck className="text-white" />
              </div>
              <div>
                <p className="font-medium">Network created successfully!</p>
                <p className="text-xs opacity-90 mt-1">Your new sector network is now active.</p>
              </div>
            </div>
          </div>
        )}

        {showJoinSuccess && (
          <div className="fixed bottom-6 right-6 z-50">
            <div className="bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center animate-fade-in-up">
              <div className="bg-white bg-opacity-20 rounded-full p-1 mr-3">
                <FaCheck className="text-white" />
              </div>
              <div>
                <p className="font-medium">Join request sent!</p>
                <p className="text-xs opacity-90 mt-1">The network coordinator will review your request.</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}