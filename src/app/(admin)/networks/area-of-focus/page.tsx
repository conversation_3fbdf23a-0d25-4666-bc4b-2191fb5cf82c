"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineUsers,
  HiOutlineCalendar,
  HiOutlineMapPin,
  HiOutlineDocumentText,
  HiOutlinePlus,
  HiOutlineMagnifyingGlass,
  HiOutlineFunnel,
  HiOutlineEye,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineGlobeAlt,
  HiOutlineBuildingOffice,
} from "react-icons/hi2";

interface AreaOfFocusNetwork {
  id: string;
  name: string;
  description: string;
  focusArea: string;
  memberCount: number;
  location: string;
  status: "active" | "inactive" | "pending";
  createdDate: string;
  lastActivity: string;
  coordinator: string;
}

export default function AreaOfFocusNetworkPage() {
  const [networks, setNetworks] = useState<AreaOfFocusNetwork[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterFocusArea, setFilterFocusArea] = useState<string>("all");

  useEffect(() => {
    // Simulate API call
    const fetchNetworks = async () => {
      setLoading(true);
      // Mock data
      const mockNetworks: AreaOfFocusNetwork[] = [
        {
          id: "1",
          name: "Health & Nutrition Network",
          description:
            "Network focused on health and nutrition initiatives across Malawi",
          focusArea: "Health",
          memberCount: 45,
          location: "Lilongwe, Malawi",
          status: "active",
          createdDate: "2023-01-15",
          lastActivity: "2024-02-25",
          coordinator: "Dr. Sarah Johnson",
        },
        {
          id: "2",
          name: "Education & Skills Development",
          description:
            "Network promoting education and skills development programs",
          focusArea: "Education",
          memberCount: 32,
          location: "Blantyre, Malawi",
          status: "active",
          createdDate: "2023-03-20",
          lastActivity: "2024-02-24",
          coordinator: "John Banda",
        },
        {
          id: "3",
          name: "Agriculture & Food Security",
          description:
            "Network focused on sustainable agriculture and food security",
          focusArea: "Agriculture",
          memberCount: 28,
          location: "Mzuzu, Malawi",
          status: "active",
          createdDate: "2023-02-10",
          lastActivity: "2024-02-20",
          coordinator: "Mary Phiri",
        },
        {
          id: "4",
          name: "Environmental Conservation",
          description:
            "Network dedicated to environmental protection and conservation",
          focusArea: "Environment",
          memberCount: 18,
          location: "Zomba, Malawi",
          status: "inactive",
          createdDate: "2023-04-05",
          lastActivity: "2024-01-15",
          coordinator: "David Mhango",
        },
        {
          id: "5",
          name: "Youth Empowerment Network",
          description: "Network focused on youth development and empowerment",
          focusArea: "Youth",
          memberCount: 55,
          location: "Lilongwe, Malawi",
          status: "active",
          createdDate: "2023-01-30",
          lastActivity: "2024-02-26",
          coordinator: "Michael Chen",
        },
      ];

      setTimeout(() => {
        setNetworks(mockNetworks);
        setLoading(false);
      }, 1000);
    };

    fetchNetworks();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "inactive":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getFocusAreaColor = (focusArea: string) => {
    switch (focusArea) {
      case "Health":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "Education":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "Agriculture":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "Environment":
        return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300";
      case "Youth":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const filteredNetworks = networks.filter((network) => {
    const matchesSearch =
      network.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      network.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      network.coordinator.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || network.status === filterStatus;
    const matchesFocusArea =
      filterFocusArea === "all" || network.focusArea === filterFocusArea;

    return matchesSearch && matchesStatus && matchesFocusArea;
  });

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-gray-600 dark:text-gray-400">
            Loading networks...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Area of Focus Networks
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage networks organized by focus areas and specializations
          </p>
        </div>
        <button className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
          <HiOutlinePlus className="h-4 w-4" />
          Create Network
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Networks
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {networks.length}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <HiOutlineGlobeAlt className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Networks
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {networks.filter((n) => n.status === "active").length}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <HiOutlineUsers className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Members
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {networks.reduce(
                  (sum, network) => sum + network.memberCount,
                  0,
                )}
              </p>
            </div>
            <div className="rounded-full bg-purple-100 p-3 dark:bg-purple-900">
              <HiOutlineUsers className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Focus Areas
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {new Set(networks.map((n) => n.focusArea)).size}
              </p>
            </div>
            <div className="rounded-full bg-orange-100 p-3 dark:bg-orange-900">
              <HiOutlineBuildingOffice className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="relative max-w-md flex-1">
          <HiOutlineMagnifyingGlass className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search networks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          />
        </div>

        <div className="flex gap-2">
          <select
            value={filterFocusArea}
            onChange={(e) => setFilterFocusArea(e.target.value)}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            <option value="all">All Focus Areas</option>
            <option value="Health">Health</option>
            <option value="Education">Education</option>
            <option value="Agriculture">Agriculture</option>
            <option value="Environment">Environment</option>
            <option value="Youth">Youth</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
          </select>
        </div>
      </div>

      {/* Networks Table */}
      <div className="rounded-lg bg-white shadow-sm dark:bg-gray-800">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Focus Area Networks
          </h2>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Network
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Focus Area
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Members
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Coordinator
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Last Activity
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredNetworks.map((network) => (
                  <tr
                    key={network.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
                          <HiOutlineGlobeAlt className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {network.name}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {network.description}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span
                        className={`rounded-full px-2 py-1 text-xs ${getFocusAreaColor(network.focusArea)}`}
                      >
                        {network.focusArea}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        <HiOutlineUsers className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-900 dark:text-white">
                          {network.memberCount}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span
                        className={`rounded-full px-2 py-1 text-xs ${getStatusColor(network.status)}`}
                      >
                        {network.status}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {network.coordinator}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {network.lastActivity}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlineEye className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlinePencil className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-red-400 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-300">
                          <HiOutlineTrash className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredNetworks.length === 0 && (
            <div className="py-8 text-center">
              <HiOutlineGlobeAlt className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                No networks found matching your criteria.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
