"use client";
import React, { useState } from "react";
import { HiOutlineBuildingOffice2 } from "react-icons/hi2";
import {
  FaStethoscope,
  FaGraduationCap,
  FaTractor,
  FaLeaf,
  FaEye,
} from "react-icons/fa";
import { Modal } from "@/components/ui/modal";
import { useRouter } from "next/navigation";

const districtNetworks = [
  {
    id: 1,
    name: "District Network 1",
    type: "Health",
    district: "District A",
    dateFormed: "2013-02-14",
    coordinator: "<PERSON>",
    isFeatured: true,
  },
  {
    id: 2,
    name: "District Network 2",
    type: "Education",
    district: "District B",
    dateFormed: "2016-06-30",
    coordinator: "<PERSON>",
    isFeatured: false,
  },
  {
    id: 3,
    name: "District Network 3",
    type: "Agriculture",
    district: "District C",
    dateFormed: "2019-09-18",
    coordinator: "<PERSON>",
    isFeatured: false,
  },
  {
    id: 4,
    name: "District Network 4",
    type: "Environment",
    district: "District D",
    dateFormed: "2020-03-10",
    coordinator: "<PERSON>",
    isFeatured: false,
  },
];

export default function DistrictNetworkPage() {
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [networkSubmitted, setNetworkSubmitted] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [form, setForm] = useState({
    name: "",
    type: "",
    district: "",
    dateFormed: "",
    coordinator: "",
    description: "",
    contactEmail: "",
    contactPhone: "",
  });

  const filtered = districtNetworks.filter((net) =>
    Object.values(net).some((val) =>
      String(val).toLowerCase().includes(search.toLowerCase()),
    ),
  );

  const featuredNetwork = filtered.find((net) => net.isFeatured);
  const regularNetworks = filtered.filter((net) => !net.isFeatured);

  // Function to get icon based on network type
  const getNetworkTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "health":
        return <FaStethoscope className="text-white" />;
      case "education":
        return <FaGraduationCap className="text-white" />;
      case "agriculture":
        return <FaTractor className="text-white" />;
      case "environment":
        return <FaLeaf className="text-white" />;
      default:
        return <HiOutlineBuildingOffice2 className="text-white" />;
    }
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    setForm({
      ...form,
      [e.target.name]: e.target.value,
    });
  };

  const handleNext = () => {
    if (currentStep === 1 && isStep1Valid) {
      setCurrentStep(2);
    }
  };

  const handlePrevious = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    }
  };

  const isStep1Valid =
    form.name && form.type && form.district && form.dateFormed;
  const isStep2Valid =
    form.coordinator && form.contactEmail && form.contactPhone;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the network data to your backend
    console.log("District network submitted:", form);
    setNetworkSubmitted(true);
    setShowAddModal(false);
    setCurrentStep(1);
    setTimeout(() => setNetworkSubmitted(false), 5000);
    setForm({
      name: "",
      type: "",
      district: "",
      dateFormed: "",
      coordinator: "",
      description: "",
      contactEmail: "",
      contactPhone: "",
    });
  };

  const openAddModal = () => {
    setShowAddModal(true);
  };

  const navigateToMessageCentre = (network: any) => {
    // Navigate to message centre with network context
    router.push(
      `/message-centre?network=${network.id}&type=district&name=${encodeURIComponent(network.name)}`,
    );
  };

  return (
    <div className="mt-2 w-full">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex justify-start"></div>
      </div>

      {/* Search and Add Button Row */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex justify-start">
          <input
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="Search district networks..."
            className="w-full max-w-xs rounded-lg border border-gray-300 bg-white px-4 py-2 text-base text-dark placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none dark:bg-gray-700 dark:text-white dark:placeholder-gray-500"
          />
        </div>
        <button
          onClick={openAddModal}
          className="rounded-lg bg-primary px-6 py-3 font-medium text-white transition hover:bg-opacity-90"
        >
          Add District Network
        </button>
      </div>

      {/* Featured Network Card */}
      {featuredNetwork && (
        <div className="mb-6">
          <div className="rounded-xl bg-[#5750f1] p-6 shadow-lg transition-transform duration-200 hover:scale-[1.025]">
            <div className="flex items-start space-x-4">
              <div className="relative">
                <div className="flex size-16 items-center justify-center rounded-full bg-white/20">
                  <span className="text-xl font-bold text-white">1</span>
                </div>
                <div className="absolute -bottom-1 -right-1 size-3 rounded-full bg-green-400"></div>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h3 className="text-xl font-bold text-white">
                    {featuredNetwork.name}
                  </h3>
                  <div className="flex items-center justify-center rounded-full bg-white/20 p-1">
                    {getNetworkTypeIcon(featuredNetwork.type)}
                  </div>
                </div>
                <p className="mt-1 text-sm text-white/80">
                  Type: {featuredNetwork.type}, {featuredNetwork.district}
                </p>
                <p className="mt-1 text-sm text-white/80">
                  Coordinator: {featuredNetwork.coordinator}
                </p>
                <p className="mt-1 text-sm text-white/80">
                  Formed: {featuredNetwork.dateFormed}
                </p>
                <button
                  onClick={() => navigateToMessageCentre(featuredNetwork)}
                  className="mt-3 flex items-center space-x-1 text-sm font-medium text-white hover:text-white/80"
                  title="Click to view detailed information about this network"
                >
                  <FaEye className="text-sm" />
                  <span>View</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Regular Network Cards Grid */}
      <div className="grid gap-4 sm:grid-cols-2">
        {regularNetworks.map((net, index) => (
          <div
            key={net.id}
            className={`rounded-xl p-6 shadow-lg transition-transform duration-200 hover:scale-[1.025] ${
              index % 2 === 0 ? "bg-[#5750f1]" : "bg-gray-100 dark:bg-gray-700"
            }`}
          >
            <div className="flex items-start space-x-4">
              <div className="flex size-12 items-center justify-center rounded-full bg-white/20">
                <span className="text-lg font-bold text-white">{net.id}</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h3
                    className={`text-lg font-bold ${
                      index % 2 === 0
                        ? "text-white"
                        : "text-gray-900 dark:text-white"
                    }`}
                  >
                    {net.name}
                  </h3>
                  <div
                    className={`flex items-center justify-center rounded-full p-1 ${
                      index % 2 === 0
                        ? "bg-white/20"
                        : "bg-gray-200 dark:bg-gray-600"
                    }`}
                  >
                    {getNetworkTypeIcon(net.type)}
                  </div>
                </div>
                <p
                  className={`mt-1 text-sm ${
                    index % 2 === 0
                      ? "text-white/80"
                      : "text-gray-600 dark:text-gray-300"
                  }`}
                >
                  Type: {net.type}, {net.district}
                </p>
                <p
                  className={`mt-1 text-sm ${
                    index % 2 === 0
                      ? "text-white/80"
                      : "text-gray-600 dark:text-gray-300"
                  }`}
                >
                  Coordinator: {net.coordinator}
                </p>
                <p
                  className={`mt-1 text-sm ${
                    index % 2 === 0
                      ? "text-white/80"
                      : "text-gray-600 dark:text-gray-300"
                  }`}
                >
                  Formed: {net.dateFormed}
                </p>
                <button
                  onClick={() => navigateToMessageCentre(net)}
                  className={`mt-3 flex items-center space-x-1 text-sm font-medium ${
                    index % 2 === 0
                      ? "text-white hover:text-white/80"
                      : "text-gray-900 hover:text-gray-700 dark:text-white dark:hover:text-gray-300"
                  }`}
                  title="Click to view detailed information about this network"
                >
                  <FaEye className="text-sm" />
                  <span>View</span>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filtered.length === 0 && (
        <div className="rounded-xl bg-gray-100 p-8 text-center text-gray-500 dark:bg-gray-700 dark:text-gray-400">
          No district networks found.
        </div>
      )}

      {/* Add District Network Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add District Network"
      >
        <div>
          {/* Step Indicator */}
          <div className="mb-6 flex items-center justify-center space-x-4">
            <div
              className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
                currentStep >= 1
                  ? "bg-primary text-white"
                  : "bg-gray-200 text-gray-500"
              }`}
            >
              1
            </div>
            <div
              className={`h-1 w-12 rounded ${
                currentStep >= 2 ? "bg-primary" : "bg-gray-200"
              }`}
            ></div>
            <div
              className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
                currentStep >= 2
                  ? "bg-primary text-white"
                  : "bg-gray-200 text-gray-500"
              }`}
            >
              2
            </div>
          </div>

          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
                Basic Information
              </h3>
              <div>
                <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
                  Network Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={form.name}
                  onChange={handleChange}
                  placeholder="Enter network name"
                  className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
                  required
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
                  Sector Type
                </label>
                <select
                  name="type"
                  value={form.type}
                  onChange={handleChange}
                  className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
                  required
                >
                  <option value="">Select sector type</option>
                  <option value="Health">Health</option>
                  <option value="Education">Education</option>
                  <option value="Agriculture">Agriculture</option>
                  <option value="Environment">Environment</option>
                  <option value="Human Rights">Human Rights</option>
                  <option value="Women Empowerment">Women Empowerment</option>
                  <option value="Youth Development">Youth Development</option>
                  <option value="Disability">Disability</option>
                  <option value="Water & Sanitation">Water & Sanitation</option>
                  <option value="Economic Development">
                    Economic Development
                  </option>
                </select>
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
                  District
                </label>
                <select
                  name="district"
                  value={form.district}
                  onChange={handleChange}
                  className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
                  required
                >
                  <option value="">Select district</option>
                  <option value="District A">District A</option>
                  <option value="District B">District B</option>
                  <option value="District C">District C</option>
                  <option value="District D">District D</option>
                  <option value="District E">District E</option>
                  <option value="District F">District F</option>
                </select>
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
                  Date Formed
                </label>
                <input
                  type="date"
                  name="dateFormed"
                  value={form.dateFormed}
                  onChange={handleChange}
                  className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
                  required
                />
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="dark:border-strokedark flex-1 rounded-lg border border-stroke px-4 py-3 font-medium text-black transition hover:bg-gray-50 dark:text-white dark:hover:bg-gray-dark"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleNext}
                  disabled={!isStep1Valid}
                  className="flex-1 rounded-lg bg-primary px-4 py-3 font-medium text-white transition hover:bg-opacity-90 disabled:opacity-50"
                >
                  Next Step
                </button>
              </div>
            </div>
          )}

          {/* Step 2: Contact Information */}
          {currentStep === 2 && (
            <form onSubmit={handleSubmit} className="space-y-4">
              <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
                Contact Information
              </h3>
              <div>
                <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
                  Coordinator Name
                </label>
                <input
                  type="text"
                  name="coordinator"
                  value={form.coordinator}
                  onChange={handleChange}
                  placeholder="Enter coordinator name"
                  className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
                  required
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
                  Contact Email
                </label>
                <input
                  type="email"
                  name="contactEmail"
                  value={form.contactEmail}
                  onChange={handleChange}
                  placeholder="Enter contact email"
                  className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
                  required
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
                  Contact Phone
                </label>
                <input
                  type="tel"
                  name="contactPhone"
                  value={form.contactPhone}
                  onChange={handleChange}
                  placeholder="Enter contact phone"
                  className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
                  required
                />
              </div>

              <div>
                <label className="mb-2.5 block text-sm font-medium text-black dark:text-white">
                  Description
                </label>
                <textarea
                  name="description"
                  value={form.description}
                  onChange={handleChange}
                  placeholder="Enter network description"
                  className="disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input w-full rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default dark:focus:border-primary"
                  rows={3}
                />
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={handlePrevious}
                  className="dark:border-strokedark flex-1 rounded-lg border border-stroke px-4 py-3 font-medium text-black transition hover:bg-gray-50 dark:text-white dark:hover:bg-gray-dark"
                >
                  Previous
                </button>
                <button
                  type="submit"
                  disabled={!isStep2Valid}
                  className="flex-1 rounded-lg bg-primary px-4 py-3 font-medium text-white transition hover:bg-opacity-90 disabled:opacity-50"
                >
                  Add Network
                </button>
              </div>
            </form>
          )}
        </div>
      </Modal>

      {/* Success Message */}
      {networkSubmitted && (
        <div className="fixed bottom-4 right-4 z-50 rounded-lg bg-green-500 px-6 py-3 text-white shadow-lg">
          <div className="flex items-center space-x-2">
            <span className="text-xl">✓</span>
            <span>
              Thank you! Your district network has been added successfully.
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
