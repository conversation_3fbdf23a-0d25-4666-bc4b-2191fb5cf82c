"use client";

import { useEffect, useState } from "react";
import { getCurrentUser, hasRole, hasAnyRole } from "@/utils/auth.utils";
import { User, UserRole } from "@/services/auth.services";
import { SuperAdminOverview } from "./_components/super-admin-overview";
import { Suspense } from "react";
import { ChatsCard } from "./_components/chats-card";
import { OverviewCardsGroup } from "./_components/overview-cards";
import { NGOAdminOverview } from "./_components/ngo-admin-overview";
import { NGODashboardWidgets } from "./_components/ngo-dashboard-widgets";
import { CSOChairOverview } from "./_components/cso-chair-overview";
import { StaffRegistryOverview } from "./_components/staff-registry-overview";
import { StaffAdminOverview } from "./_components/staff-admin-overview";
import { ProgrammesOfficerOverview } from "./_components/programmes-officer-overview";
import { RegionLabels } from "./_components/region-labels";
import { FinancialMetrics } from "./_components/financial-metrics";
import { FinancialMetricsSkeleton } from "./_components/financial-metrics/skeleton";
import { FinancialTables } from "./_components/financial-tables";
import { FinancialTablesSkeleton } from "./_components/financial-tables/skeleton";
import { NGOsPerformance } from "@/components/Charts/payments-overview";
import { ApplicationStatus } from "@/components/Charts/used-devices";
import { UserAlerts } from "@/components/Charts/weeks-profit";
import { TopChannels } from "@/components/Tables/top-channels";
import { TopChannelsSkeleton } from "@/components/Tables/top-channels/skeleton";

// Role-based component configuration
const ROLE_COMPONENTS = {
  super_admin: {
    cards: false,
    ngoAdminOverview: false,
    ngoDashboardWidgets: false,
    csoChairOverview: false,
    staffRegistryOverview: false,
    programmesOfficerOverview: false,
    superAdminOverview: true,
    ngosPerformance: false,
    userAlerts: false,
    applicationStatus: false,
    regionLabels: false,
    topChannels: false,
    chatsCard: false,
    financialMetrics: false,
    financialTables: false,
    staffAdminOverview: false,
  },
  finance_officer: {
    cards: true,
    ngoAdminOverview: false,
    ngoDashboardWidgets: false,
    csoChairOverview: false,
    staffRegistryOverview: false,
    programmesOfficerOverview: false,
    superAdminOverview: false,
    ngosPerformance: false,
    userAlerts: false,
    applicationStatus: false,
    regionLabels: false,
    topChannels: false,
    chatsCard: false,
    financialMetrics: true,
    financialTables: true,
    staffAdminOverview: false,
  },
  ngo_admin: {
    cards: false,
    ngoAdminOverview: true,
    ngoDashboardWidgets: true,
    csoChairOverview: false,
    staffRegistryOverview: false,
    programmesOfficerOverview: false,
    superAdminOverview: false,
    ngosPerformance: false,
    userAlerts: false,
    applicationStatus: false,
    regionLabels: false,
    topChannels: false,
    chatsCard: false,
    financialMetrics: false,
    financialTables: false,
    staffAdminOverview: false,
  },
  staff_registry: {
    cards: false,
    ngoAdminOverview: false,
    ngoDashboardWidgets: false,
    csoChairOverview: false,
    staffRegistryOverview: true,
    programmesOfficerOverview: false,
    superAdminOverview: false,
    ngosPerformance: false,
    userAlerts: false,
    applicationStatus: false,
    regionLabels: false,
    topChannels: false,
    chatsCard: false,
    financialMetrics: false,
    financialTables: false,
    staffAdminOverview: false,
  },
  staff_admin: {
    cards: false,
    ngoAdminOverview: false,
    ngoDashboardWidgets: false,
    csoChairOverview: false,
    staffRegistryOverview: false,
    programmesOfficerOverview: false,
    superAdminOverview: false,
    ngosPerformance: false,
    userAlerts: false,
    applicationStatus: false,
    regionLabels: false,
    topChannels: false,
    chatsCard: false,
    financialMetrics: false,
    financialTables: false,
    staffAdminOverview: true,
  },
  programmes_officer: {
    cards: false,
    ngoAdminOverview: false,
    ngoDashboardWidgets: false,
    csoChairOverview: false,
    staffRegistryOverview: false,
    programmesOfficerOverview: true,
    superAdminOverview: false,
    ngosPerformance: false,
    userAlerts: false,
    applicationStatus: false,
    regionLabels: false,
    topChannels: false,
    chatsCard: false,
    financialMetrics: false,
    financialTables: false,
    staffAdminOverview: false,
  },
  cso_chair: {
    cards: false,
    ngoAdminOverview: false,
    ngoDashboardWidgets: false,
    csoChairOverview: true,
    staffRegistryOverview: false,
    programmesOfficerOverview: false,
    superAdminOverview: false,
    ngosPerformance: false,
    userAlerts: false,
    applicationStatus: false,
    regionLabels: false,
    topChannels: false,
    chatsCard: false,
    financialMetrics: false,
    financialTables: false,
    staffAdminOverview: false,
  },
} as const;

export default function OverviewPage() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
      setIsLoading(false);
    };

    fetchUser();
  }, []);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            Access Denied
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Please log in to access the overview.
          </p>
        </div>
      </div>
    );
  }

  // Get components for user's role
  const userRole = user.role.name;
  const allowedComponents =
    ROLE_COMPONENTS[userRole] || ROLE_COMPONENTS.ngo_admin;

  return (
    <div className="space-y-4 md:space-y-6 2xl:space-y-7.5">
      {/* Overview Cards - Available to all roles */}
      {allowedComponents.cards && <OverviewCardsGroup />}

      {/* Super Admin Overview - Specific to super_admin role */}
      {allowedComponents.superAdminOverview && <SuperAdminOverview />}

      {/* NGO Admin Overview - Specific to ngo_admin role */}
      {allowedComponents.ngoAdminOverview && <NGOAdminOverview />}

      {/* NGO Dashboard Widgets - Additional components for ngo_admin */}
      {allowedComponents.ngoDashboardWidgets && <NGODashboardWidgets />}

      {/* CSO Chair Overview - Specific to cso_chair role */}
      {allowedComponents.csoChairOverview && <CSOChairOverview />}

      {/* Staff Registry Overview - Specific to staff_registry role */}
      {allowedComponents.staffRegistryOverview && <StaffRegistryOverview />}

      {/* Staff Admin Overview - Specific to staff_admin role */}
      {allowedComponents.staffAdminOverview && <StaffAdminOverview />}

      {/* Programmes Officer Overview - Specific to programmes_officer role */}
      {allowedComponents.programmesOfficerOverview && (
        <ProgrammesOfficerOverview />
      )}

      {/* Financial Metrics - Finance Officer specific */}
      {allowedComponents.financialMetrics && (
        <Suspense fallback={<FinancialMetricsSkeleton />}>
          <FinancialMetrics />
        </Suspense>
      )}

      {/* Only show this grid for non-super-admin roles that need individual components */}
      {!allowedComponents.superAdminOverview && (
        <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
          {/* NGOs Performance Chart */}
          {allowedComponents.ngosPerformance && (
            <NGOsPerformance className="col-span-12 xl:col-span-7" />
          )}

          {/* User Alerts */}
          {allowedComponents.userAlerts && (
            <UserAlerts className="col-span-12 xl:col-span-5" />
          )}

          {/* Application Status */}
          {allowedComponents.applicationStatus && (
            <ApplicationStatus className="col-span-12 xl:col-span-5" />
          )}

          {/* Region Labels */}
          {allowedComponents.regionLabels && <RegionLabels />}

          {/* Top Channels Table */}
          {allowedComponents.topChannels && (
            <div className="col-span-12 grid xl:col-span-8">
              <Suspense fallback={<TopChannelsSkeleton />}>
                <TopChannels />
              </Suspense>
            </div>
          )}

          {/* Chats Card - Super Admin only */}
          {allowedComponents.chatsCard && (
            <Suspense fallback={null}>
              <ChatsCard />
            </Suspense>
          )}

          {/* Financial Tables - Finance Officer specific */}
          {allowedComponents.financialTables && (
            <div className="col-span-12">
              <Suspense fallback={<FinancialTablesSkeleton />}>
                <FinancialTables />
              </Suspense>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
