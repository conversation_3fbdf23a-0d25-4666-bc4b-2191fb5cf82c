"use client";

import { useState, useEffect } from "react";
import { getCurrentUser } from "@/utils/auth.utils";
import {
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineUserGroup,
  HiOutlineBell,
  HiOutlineCalendar,
  HiOutlineChatBubbleLeftRight,
  HiOutlineMegaphone,
  HiOutlineDocumentText,
  HiOutlineChartBar,
  HiOutlineUsers,
  HiOutlineBuildingOffice2,
  HiOutlineExclamationTriangle,
  HiOutlineEye,
} from "react-icons/hi2";
import dynamic from "next/dynamic";
import { motion } from "framer-motion";

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface CSOChairStatus {
  networkStats: {
    totalNGOs: number;
    pendingRequests: number;
    approvedNGOs: number;
    rejectedNGOs: number;
  };
  recentActivity: Array<{
    ngoName: string;
    action: string;
    timestamp: string;
    type: "join" | "update" | "message" | "meeting";
  }>;
  sharedOpportunities: Array<{
    title: string;
    description: string;
    deadline: string;
    participants: number;
    status: "active" | "closed" | "upcoming";
  }>;
  messagesReceived: Array<{
    from: string;
    subject: string;
    timestamp: string;
    isRead: boolean;
  }>;
  networkMeetings: Array<{
    title: string;
    date: string;
    time: string;
    type: "general" | "thematic" | "emergency";
    attendees: number;
  }>;
  notifications: Array<{
    title: string;
    message: string;
    timestamp: string;
    priority: "high" | "medium" | "low";
  }>;
  ngoByThematicArea: Array<{
    area: string;
    count: number;
    percentage: number;
  }>;
}

export function CSOChairOverview() {
  const [status, setStatus] = useState<CSOChairStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching CSO Chair data
    const fetchCSOData = async () => {
      try {
        // In a real app, this would be an API call
        const mockData: CSOChairStatus = {
          networkStats: {
            totalNGOs: 45,
            pendingRequests: 8,
            approvedNGOs: 37,
            rejectedNGOs: 3,
          },
          recentActivity: [
            {
              ngoName: "Youth Empowerment Initiative",
              action: "Submitted join request",
              timestamp: "2 hours ago",
              type: "join",
            },
            {
              ngoName: "Community Health Partners",
              action: "Updated profile information",
              timestamp: "4 hours ago",
              type: "update",
            },
            {
              ngoName: "Education for All",
              action: "Sent message to network",
              timestamp: "1 day ago",
              type: "message",
            },
            {
              ngoName: "Environmental Conservation Group",
              action: "Attended monthly meeting",
              timestamp: "2 days ago",
              type: "meeting",
            },
          ],
          sharedOpportunities: [
            {
              title: "Capacity Building Workshop",
              description: "Training on project management and fundraising",
              deadline: "2024-11-30",
              participants: 12,
              status: "active",
            },
            {
              title: "Grant Application Support",
              description: "Assistance with international grant applications",
              deadline: "2024-12-15",
              participants: 8,
              status: "upcoming",
            },
            {
              title: "Resource Sharing Program",
              description: "Sharing of equipment and expertise among members",
              deadline: "2024-10-25",
              participants: 15,
              status: "closed",
            },
          ],
          messagesReceived: [
            {
              from: "Women's Rights Network",
              subject: "Request for collaboration on gender equality project",
              timestamp: "1 hour ago",
              isRead: false,
            },
            {
              from: "Rural Development Association",
              subject: "Meeting schedule for next month",
              timestamp: "3 hours ago",
              isRead: true,
            },
            {
              from: "Youth Empowerment Initiative",
              subject: "Feedback on network guidelines",
              timestamp: "1 day ago",
              isRead: true,
            },
          ],
          networkMeetings: [
            {
              title: "Monthly Network Meeting",
              date: "2024-11-15",
              time: "10:00 AM",
              type: "general",
              attendees: 25,
            },
            {
              title: "Thematic Area Discussion - Health",
              date: "2024-11-20",
              time: "2:00 PM",
              type: "thematic",
              attendees: 12,
            },
            {
              title: "Emergency Response Coordination",
              date: "2024-11-25",
              time: "9:00 AM",
              type: "emergency",
              attendees: 8,
            },
          ],
          notifications: [
            {
              title: "New NGO Application",
              message:
                "Youth Empowerment Initiative has submitted a join request",
              timestamp: "2 hours ago",
              priority: "high",
            },
            {
              title: "Network Meeting Reminder",
              message: "Monthly network meeting scheduled for next week",
              timestamp: "1 day ago",
              priority: "medium",
            },
            {
              title: "Resource Sharing Opportunity",
              message: "New equipment available for sharing among members",
              timestamp: "2 days ago",
              priority: "low",
            },
          ],
          ngoByThematicArea: [
            { area: "Education", count: 12, percentage: 26.7 },
            { area: "Health", count: 10, percentage: 22.2 },
            { area: "Environment", count: 8, percentage: 17.8 },
            { area: "Youth Development", count: 7, percentage: 15.6 },
            { area: "Women Empowerment", count: 5, percentage: 11.1 },
            { area: "Rural Development", count: 3, percentage: 6.7 },
          ],
        };

        setStatus(mockData);
      } catch (error) {
        console.error("Error fetching CSO Chair data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCSOData();
  }, []);

  if (isLoading || !status) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
        {Array.from({ length: 10 }).map((_, i) => (
          <div
            key={i}
            className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
          >
            <div className="animate-pulse">
              <div className="h-4 w-24 rounded bg-gray-300 dark:bg-gray-600"></div>
              <div className="mt-2 h-8 w-16 rounded bg-gray-300 dark:bg-gray-600"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
      case "approved":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "pending":
      case "upcoming":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "rejected":
      case "closed":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      case "medium":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "low":
        return "text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "join":
        return <HiOutlineUserGroup className="h-4 w-4 text-blue-600" />;
      case "update":
        return <HiOutlineDocumentText className="h-4 w-4 text-green-600" />;
      case "message":
        return (
          <HiOutlineChatBubbleLeftRight className="h-4 w-4 text-purple-600" />
        );
      case "meeting":
        return <HiOutlineCalendar className="h-4 w-4 text-orange-600" />;
      default:
        return <HiOutlineClock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Network Statistics - replaced with new metrics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* Active Opportunities */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Opportunities
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {
                  status.sharedOpportunities.filter(
                    (o) => o.status === "active",
                  ).length
                }
              </p>
            </div>
            <HiOutlineMegaphone className="h-8 w-8 text-green-600" />
          </div>
        </motion.div>
        {/* Upcoming Meetings */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
          className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Upcoming Meetings
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {
                  status.networkMeetings.filter(
                    (m) => new Date(m.date) > new Date(),
                  ).length
                }
              </p>
            </div>
            <HiOutlineCalendar className="h-8 w-8 text-orange-600" />
          </div>
        </motion.div>
        {/* Unread Messages */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
          className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Unread Messages
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.messagesReceived.filter((m) => !m.isRead).length}
              </p>
            </div>
            <HiOutlineChatBubbleLeftRight className="h-8 w-8 text-purple-600" />
          </div>
        </motion.div>
        {/* Network Growth */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Network Growth
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.networkStats.totalNGOs}
              </p>
            </div>
            <HiOutlineChartBar className="h-8 w-8 text-indigo-600" />
          </div>
        </motion.div>
      </div>

      {/* User-Friendly Graphs */}
      <div className="mt-6 grid gap-6 lg:grid-cols-2">
        {/* NGOs by Thematic Area Pie Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
        >
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            NGOs by Thematic Area
          </h3>
          <ApexChart
            type="pie"
            width="100%"
            height={320}
            options={{
              labels: status.ngoByThematicArea.map((a) => a.area),
              legend: { position: "bottom" },
              colors: [
                "#6366F1",
                "#10B981",
                "#F59E0B",
                "#EF4444",
                "#3B82F6",
                "#8B5CF6",
              ],
              chart: { fontFamily: "inherit" },
            }}
            series={status.ngoByThematicArea.map((a) => a.count)}
          />
        </motion.div>
        {/* Network Growth Line Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
        >
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Network Growth
          </h3>
          <ApexChart
            type="line"
            width="100%"
            height={320}
            options={{
              xaxis: {
                categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
                title: { text: "Month" },
              },
              yaxis: { title: { text: "Total NGOs" } },
              chart: { fontFamily: "inherit" },
              colors: ["#6366F1"],
              stroke: { curve: "smooth", width: 3 },
              markers: { size: 5 },
              legend: { show: false },
            }}
            series={[
              {
                name: "Total NGOs",
                data: [30, 32, 35, 38, 42, status.networkStats.totalNGOs],
              },
            ]}
          />
        </motion.div>
      </div>

      {/* Recent Activity and Messages */}
      <div className="grid gap-4 lg:grid-cols-2">
        {/* Recent NGO Activity */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineEye className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Recent NGO Activity
            </h3>
          </div>
          <div className="space-y-3">
            {status.recentActivity.map((activity, index) => (
              <div
                key={index}
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                {getActivityIcon(activity.type)}
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {activity.ngoName}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {activity.action}
                  </p>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {activity.timestamp}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Messages Received */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineChatBubbleLeftRight className="h-5 w-5 text-purple-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Messages Received
            </h3>
          </div>
          <div className="space-y-3">
            {status.messagesReceived.map((message, index) => (
              <div
                key={index}
                className={`rounded-lg border p-3 ${
                  message.isRead
                    ? "border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800"
                    : "border-blue-200 bg-blue-50 dark:border-blue-700 dark:bg-blue-900"
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {message.from}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {message.subject}
                    </p>
                  </div>
                  {!message.isRead && (
                    <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                  )}
                </div>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {message.timestamp}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Shared Opportunities */}
      <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-2">
          <HiOutlineMegaphone className="h-5 w-5 text-green-600" />
          <h3 className="font-medium text-gray-900 dark:text-white">
            Shared Opportunities
          </h3>
        </div>
        <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
          {status.sharedOpportunities.map((opportunity, index) => (
            <div
              key={index}
              className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {opportunity.title}
                  </h4>
                  <p className="mt-1 text-xs text-gray-600 dark:text-gray-400">
                    {opportunity.description}
                  </p>
                  <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    Deadline: {opportunity.deadline}
                  </p>
                </div>
                <span
                  className={`ml-2 rounded-full px-2 py-1 text-xs ${getStatusColor(opportunity.status)}`}
                >
                  {opportunity.status}
                </span>
              </div>
              <div className="mt-2 flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                <HiOutlineUsers className="h-3 w-3" />
                <span>{opportunity.participants} participants</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Network Meetings and Notifications */}
      <div className="grid gap-4 lg:grid-cols-2">
        {/* Network Meeting Schedules */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineCalendar className="h-5 w-5 text-orange-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Network Meeting Schedules
            </h3>
          </div>
          <div className="space-y-3">
            {status.networkMeetings.map((meeting, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {meeting.title}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {meeting.date} at {meeting.time}
                    </p>
                  </div>
                  <span
                    className={`ml-2 rounded-full px-2 py-1 text-xs ${
                      meeting.type === "general"
                        ? "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400"
                        : meeting.type === "thematic"
                          ? "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400"
                          : "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400"
                    }`}
                  >
                    {meeting.type}
                  </span>
                </div>
                <div className="mt-2 flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                  <HiOutlineUsers className="h-3 w-3" />
                  <span>{meeting.attendees} attendees</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Network Notifications */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineBell className="h-5 w-5 text-red-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Network Notifications
            </h3>
          </div>
          <div className="space-y-3">
            {status.notifications.map((notification, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {notification.title}
                    </h4>
                    <p className="mt-1 text-xs text-gray-600 dark:text-gray-400">
                      {notification.message}
                    </p>
                  </div>
                  <span
                    className={`ml-2 rounded-full px-2 py-1 text-xs ${getPriorityColor(notification.priority)}`}
                  >
                    {notification.priority}
                  </span>
                </div>
                <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  {notification.timestamp}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* NGOs by Thematic Area */}
      <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-2">
          <HiOutlineChartBar className="h-5 w-5 text-indigo-600" />
          <h3 className="font-medium text-gray-900 dark:text-white">
            NGOs by Thematic Area
          </h3>
        </div>
        <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
          {status.ngoByThematicArea.map((area, index) => (
            <div
              key={index}
              className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {area.area}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {area.count} NGOs
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {area.percentage}%
                  </p>
                  <div className="mt-1 h-2 w-16 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full bg-indigo-600"
                      style={{ width: `${area.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
