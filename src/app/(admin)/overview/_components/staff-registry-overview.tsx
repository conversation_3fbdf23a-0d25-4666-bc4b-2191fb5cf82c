"use client";

import { useState, useEffect } from "react";
import { getStaffRegistryStatistics } from "@/services/statistics.services";
import { StaffRegistryStatistics } from "@/types/staff-registry-statistics.types";
import { ApiResponse } from "@/services/role.services";
import {
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineUserGroup,
  HiOutlineBell,
  HiOutlineCalendar,
  HiOutlineChatBubbleLeftRight,
  HiOutlineDocumentText,
  HiOutlineChartBar,
  HiOutlineUsers,
  HiOutlineBuildingOffice2,
  HiOutlineExclamationTriangle,
  HiOutlineEye,
  HiOutlineMagnifyingGlass,
  HiOutlineGlobeAlt,
  HiOutlineHome,
  HiOutlineClipboardDocumentList,
  HiOutlineCalendarDays,
  HiOutlineDocumentCheck,
  HiOutlineUser,
  HiOutlineFlag,
} from "react-icons/hi2";

export function StaffRegistryOverview() {
  const [status, setStatus] = useState<StaffRegistryStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    const fetchRegistryData = async () => {
      try {
        const token = localStorage.getItem("accessToken")!;
        if (token) {
          const response: ApiResponse<StaffRegistryStatistics> =
            await getStaffRegistryStatistics(token);
          if (response.status && response.data) {
            setStatus(response.data);
          } else {
            console.error(
              "Failed to fetch staff registry data:",
              response.message,
            );
          }
        }
      } catch (error) {
        console.error("Error fetching Staff Registry data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRegistryData();
  }, []);

  if (isLoading || !status) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
        {Array.from({ length: 13 }).map((_, i) => (
          <div
            key={i}
            className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
          >
            <div className="animate-pulse">
              <div className="h-4 w-24 rounded bg-gray-300 dark:bg-gray-600"></div>
              <div className="mt-2 h-8 w-16 rounded bg-gray-300 dark:bg-gray-600"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
      case "approved":
      case "resolved":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "pending":
      case "underReview":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "rejected":
      case "inactive":
      case "escalated":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const filteredRegistrySnapshot = status.registrySnapshot.filter((ngo) =>
    ngo.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <div className="space-y-6">
      {/* Application Statistics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* New Applications */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                New Applications
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.totalNgoApplicants.currentYear}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                This Year • {status.totalNgoApplicants.currentWeek} this week
              </p>
            </div>
            <HiOutlineDocumentText className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        {/* Under Review */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Under Review
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.ngoStatus.pending}
              </p>
            </div>
            <HiOutlineClock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        {/* Approved NGOs */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Approved NGOs
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.ngoStatus.approved}
              </p>
            </div>
            <HiOutlineCheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        {/* Rejected Applications */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Rejected
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.ngoStatus.rejected}
              </p>
            </div>
            <HiOutlineXCircle className="h-8 w-8 text-red-600" />
          </div>
        </div>
      </div>

      {/* Additional Statistics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {/* Dormant NGOs */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Dormant NGOs
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.dormantNgos}
              </p>
            </div>
            <HiOutlineFlag className="h-8 w-8 text-orange-600" />
          </div>
        </div>

        {/* NGOs by Type */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineChartBar className="h-5 w-5 text-indigo-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              NGOs by Type
            </h3>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <HiOutlineHome className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Local
                </span>
              </div>
              <span className="font-medium text-gray-900 dark:text-white">
                {status.ngoTypes.local}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <HiOutlineGlobeAlt className="h-4 w-4 text-green-600" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  International
                </span>
              </div>
              <span className="font-medium text-gray-900 dark:text-white">
                {status.ngoTypes.international}
              </span>
            </div>
          </div>
        </div>

        {/* Total Applications */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Applications
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.totalNgoApplicants.currentYear}
              </p>
            </div>
            <HiOutlineClipboardDocumentList className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Top Communication Threads */}
      <div className="grid gap-4 lg:grid-cols-1">
        {/* Top Communication Threads */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineChatBubbleLeftRight className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Top Communication Threads
            </h3>
          </div>
          <div className="space-y-3">
            {status.topCommunicationThreads.map((thread, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {thread.title}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {thread.group} • {thread.type}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {thread.lastActivity}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {thread.participants}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Participants
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {thread.messages} messages
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-2">
          <HiOutlineCalendarDays className="h-5 w-5 text-green-600" />
          <h3 className="font-medium text-gray-900 dark:text-white">
            Recent Activity
          </h3>
        </div>
        <div className="space-y-3">
          {status.recentActivity.map((activity, index) => (
            <div
              key={index}
              className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 dark:border-gray-700"
            >
              <HiOutlineEye className="h-4 w-4 text-blue-600" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {activity.action}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {activity.description}
                </p>
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {activity.timeAgo}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Searchable Registry Snapshot */}
      <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <HiOutlineMagnifyingGlass className="h-5 w-5 text-indigo-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Registry Snapshot
            </h3>
          </div>
          <div className="relative">
            <input
              type="text"
              placeholder="Search NGOs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-white">
                  NGO Name
                </th>
                <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-white">
                  Status
                </th>
                <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-white">
                  Registration Date
                </th>
                <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-white">
                  Last Activity
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredRegistrySnapshot.map((ngo, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-200 dark:border-gray-700"
                >
                  <td className="px-3 py-2 text-gray-900 dark:text-white">
                    {ngo.name}
                  </td>
                  <td className="px-3 py-2">
                    <span
                      className={`rounded-full px-2 py-1 text-xs ${getStatusColor(
                        ngo.status,
                      )}`}
                    >
                      {ngo.status}
                    </span>
                  </td>
                  <td className="px-3 py-2 text-gray-600 dark:text-gray-400">
                    {ngo.registrationDate}
                  </td>
                  <td className="px-3 py-2 text-gray-600 dark:text-gray-400">
                    {ngo.lastActionDate}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
