"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineUserGroup,
  HiOutlineClock,
  HiOutlineShieldCheck,
  HiOutlineExclamationTriangle,
  HiOutlineChartBar,
  HiOutlineCalendar,
  HiOutlineGlobeAlt,
  HiOutlineDevicePhoneMobile,
  HiOutlineComputerDesktop,
  HiOutlineArrowUp,
  HiOutlineArrowDown,
} from "react-icons/hi2";

interface LoginActivity {
  id: string;
  userId: string;
  userName: string;
  userType:
    | "super_admin"
    | "finance_officer"
    | "ngo_admin"
    | "staff_registry"
    | "staff_admin"
    | "cso_chair"
    | "programmes_officer";
  loginTime: string;
  logoutTime?: string;
  duration?: number;
  ipAddress: string;
  device: string;
  browser: string;
  location: string;
  status: "success" | "failed" | "suspicious";
  sessionId: string;
}

interface LoginAnalytics {
  totalLogins: number;
  activeUsers: number;
  failedAttempts: number;
  suspiciousActivity: number;
  averageSessionDuration: number;
  loginsByUserType: Array<{
    type: string;
    count: number;
    percentage: number;
    color: string;
  }>;
  loginsByDevice: Array<{ device: string; count: number; percentage: number }>;
  loginsByLocation: Array<{
    location: string;
    count: number;
    percentage: number;
  }>;
  recentActivity: LoginActivity[];
  topActiveUsers: Array<{
    name: string;
    userType: string;
    logins: number;
    lastLogin: string;
  }>;
  securityAlerts: Array<{
    type: string;
    message: string;
    severity: "high" | "medium" | "low";
    timestamp: string;
  }>;
}

export default function LoginActivityAnalytics() {
  const [analytics, setAnalytics] = useState<LoginAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchAnalytics = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const mockAnalytics: LoginAnalytics = {
        totalLogins: 2847,
        activeUsers: 156,
        failedAttempts: 23,
        suspiciousActivity: 5,
        averageSessionDuration: 45.2,
        loginsByUserType: [
          {
            type: "NGO Admin",
            count: 892,
            percentage: 31.3,
            color: "bg-blue-500",
          },
          {
            type: "Staff Registry",
            count: 567,
            percentage: 19.9,
            color: "bg-green-500",
          },
          {
            type: "Finance Officer",
            count: 445,
            percentage: 15.6,
            color: "bg-purple-500",
          },
          {
            type: "Super Admin",
            count: 234,
            percentage: 8.2,
            color: "bg-red-500",
          },
          {
            type: "CSO Chair",
            count: 345,
            percentage: 12.1,
            color: "bg-orange-500",
          },
          {
            type: "Programmes Officer",
            count: 234,
            percentage: 8.2,
            color: "bg-indigo-500",
          },
          {
            type: "Staff Admin",
            count: 130,
            percentage: 4.6,
            color: "bg-pink-500",
          },
        ],
        loginsByDevice: [
          { device: "Desktop", count: 1890, percentage: 66.4 },
          { device: "Mobile", count: 712, percentage: 25.0 },
          { device: "Tablet", count: 245, percentage: 8.6 },
        ],
        loginsByLocation: [
          { location: "Lilongwe", count: 890, percentage: 31.3 },
          { location: "Blantyre", count: 567, percentage: 19.9 },
          { location: "Mzuzu", count: 445, percentage: 15.6 },
          { location: "Zomba", count: 234, percentage: 8.2 },
          { location: "Other", count: 711, percentage: 25.0 },
        ],
        recentActivity: [
          {
            id: "1",
            userId: "user001",
            userName: "John Doe",
            userType: "ngo_admin",
            loginTime: "2024-01-15T10:30:00Z",
            logoutTime: "2024-01-15T12:45:00Z",
            duration: 135,
            ipAddress: "*************",
            device: "Desktop",
            browser: "Chrome",
            location: "Lilongwe",
            status: "success",
            sessionId: "sess_001",
          },
          {
            id: "2",
            userId: "user002",
            userName: "Jane Smith",
            userType: "finance_officer",
            loginTime: "2024-01-15T09:15:00Z",
            logoutTime: "2024-01-15T11:30:00Z",
            duration: 135,
            ipAddress: "************",
            device: "Mobile",
            browser: "Safari",
            location: "Blantyre",
            status: "success",
            sessionId: "sess_002",
          },
          {
            id: "3",
            userId: "user003",
            userName: "Mike Johnson",
            userType: "staff_registry",
            loginTime: "2024-01-15T08:45:00Z",
            ipAddress: "********",
            device: "Desktop",
            browser: "Firefox",
            location: "Mzuzu",
            status: "success",
            sessionId: "sess_003",
          },
          {
            id: "4",
            userId: "user004",
            userName: "Unknown User",
            userType: "ngo_admin",
            loginTime: "2024-01-15T10:25:00Z",
            ipAddress: "************",
            device: "Desktop",
            browser: "Chrome",
            location: "Unknown",
            status: "failed",
            sessionId: "sess_004",
          },
          {
            id: "5",
            userId: "user005",
            userName: "Sarah Wilson",
            userType: "super_admin",
            loginTime: "2024-01-15T07:30:00Z",
            logoutTime: "2024-01-15T10:15:00Z",
            duration: 165,
            ipAddress: "*************",
            device: "Desktop",
            browser: "Edge",
            location: "Lilongwe",
            status: "success",
            sessionId: "sess_005",
          },
        ],
        topActiveUsers: [
          {
            name: "John Doe",
            userType: "NGO Admin",
            logins: 45,
            lastLogin: "2024-01-15T10:30:00Z",
          },
          {
            name: "Jane Smith",
            userType: "Finance Officer",
            logins: 38,
            lastLogin: "2024-01-15T09:15:00Z",
          },
          {
            name: "Mike Johnson",
            userType: "Staff Registry",
            logins: 32,
            lastLogin: "2024-01-15T08:45:00Z",
          },
          {
            name: "Sarah Wilson",
            userType: "Super Admin",
            logins: 28,
            lastLogin: "2024-01-15T07:30:00Z",
          },
          {
            name: "David Brown",
            userType: "CSO Chair",
            logins: 25,
            lastLogin: "2024-01-15T06:45:00Z",
          },
        ],
        securityAlerts: [
          {
            type: "Failed Login Attempt",
            message: "Multiple failed login attempts from IP ************",
            severity: "high",
            timestamp: "2024-01-15T10:25:00Z",
          },
          {
            type: "Suspicious Activity",
            message: "Login from unusual location for user account",
            severity: "medium",
            timestamp: "2024-01-15T09:45:00Z",
          },
          {
            type: "Long Session",
            message: "Session duration exceeds normal limits",
            severity: "low",
            timestamp: "2024-01-15T08:30:00Z",
          },
        ],
      };

      setAnalytics(mockAnalytics);
      setIsLoading(false);
    };

    fetchAnalytics();
  }, []);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getUserTypeColor = (type: string) => {
    switch (type) {
      case "super_admin":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "finance_officer":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
      case "ngo_admin":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "staff_registry":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "cso_chair":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      case "programmes_officer":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400";
      case "staff_admin":
        return "bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "suspicious":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "low":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  if (isLoading) {
    return (
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Loading login activity...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="text-center">
          <HiOutlineUserGroup className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No data available
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Login activity data will appear here.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Login Activity by User Type
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Track user login patterns and security insights
          </p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Logins
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.totalLogins)}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900/20">
              <HiOutlineUserGroup className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              +15.3%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Users
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.activeUsers)}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900/20">
              <HiOutlineClock className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              +8.2%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Failed Attempts
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.failedAttempts)}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900/20">
              <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowDown className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              -12.5%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Avg Session Duration
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatDuration(analytics.averageSessionDuration)}
              </p>
            </div>
            <div className="rounded-full bg-purple-100 p-3 dark:bg-purple-900/20">
              <HiOutlineChartBar className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              +5.2%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Logins by User Type */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Logins by User Type
          </h3>
          <div className="space-y-3">
            {analytics.loginsByUserType.map((type, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className={`mr-3 h-3 w-3 rounded-full ${type.color}`}
                  ></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {type.type}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${type.percentage}%`,
                        backgroundColor: type.color
                          .replace("bg-", "")
                          .includes("500")
                          ? `var(--${type.color.replace("bg-", "").replace("-500", "")}-500)`
                          : type.color,
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {type.count} ({type.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Logins by Device */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Logins by Device
          </h3>
          <div className="space-y-3">
            {analytics.loginsByDevice.map((device, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3 h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {device.device}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full bg-green-600"
                      style={{ width: `${device.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {device.count} ({device.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Login Activity */}
      <div className="rounded-xl bg-white shadow-lg dark:bg-gray-dark">
        <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Login Activity
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  User Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Login Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Device
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-dark">
              {analytics.recentActivity.map((activity) => (
                <tr
                  key={activity.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <td className="px-6 py-4">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.userName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {activity.ipAddress}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getUserTypeColor(activity.userType)}`}
                    >
                      {activity.userType
                        .replace("_", " ")
                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {new Date(activity.loginTime).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {activity.duration
                      ? formatDuration(activity.duration)
                      : "Active"}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {activity.device}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {activity.location}
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(activity.status)}`}
                    >
                      {activity.status.charAt(0).toUpperCase() +
                        activity.status.slice(1)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Security Alerts */}
      {analytics.securityAlerts.length > 0 && (
        <div className="rounded-xl bg-yellow-50 p-6 dark:bg-yellow-900/20">
          <div className="flex items-center">
            <HiOutlineShieldCheck className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            <h3 className="ml-3 text-lg font-semibold text-yellow-900 dark:text-yellow-300">
              Security Alerts
            </h3>
          </div>
          <div className="mt-4 space-y-3">
            {analytics.securityAlerts.map((alert, index) => (
              <div
                key={index}
                className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-dark"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {alert.type}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {alert.message}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(alert.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <span
                    className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getSeverityColor(alert.severity)}`}
                  >
                    {alert.severity.charAt(0).toUpperCase() +
                      alert.severity.slice(1)}{" "}
                    Priority
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Top Active Users */}
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Top Active Users
        </h3>
        <div className="space-y-3">
          {analytics.topActiveUsers.map((user, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="mr-3 rounded-full bg-gray-100 p-2 dark:bg-gray-700">
                  <HiOutlineUserGroup className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {user.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {user.userType}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {user.logins} logins
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Last: {new Date(user.lastLogin).toLocaleDateString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
