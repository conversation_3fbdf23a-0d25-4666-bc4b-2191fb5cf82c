"use client";

import { useEffect, useState } from "react";
import { compactFormat } from "@/lib/format-number";
import { OverviewCard } from "./card";
import * as icons from "./icons";
import { getCurrentUser } from "@/utils/auth.utils";
import { User } from "@/services/auth.services";
import { getSuperAdminStatistics } from "@/services/statistics.services";
import { SuperAdminStatistics } from "@/types/super-admin-statistics.types";

export function OverviewCardsGroup() {
  const [data, setData] = useState<SuperAdminStatistics | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const token = localStorage.getItem("accessToken")!;
      try {
        const [statisticsResponse, currentUser] = await Promise.all([
          getSuperAdminStatistics(token),
          getCurrentUser(),
        ]);
        if (statisticsResponse.status === "success") {
          setData(statisticsResponse.data);
        }
        setUser(currentUser);
      } catch (error) {
        console.error("Error fetching overview data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const isFinanceOfficer = user?.role.name === "finance_officer";

  if (isLoading || !data) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5">
        {Array.from({ length: 4 }).map((_, i) => (
          <div
            key={i}
            className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark"
          >
            <div className="animate-pulse">
              <div className="h-12 w-12 rounded-full bg-gray-300 dark:bg-gray-600"></div>
              <div className="mt-6 flex items-end justify-between">
                <div>
                  <div className="mb-1.5 h-7 w-18 rounded bg-gray-300 dark:bg-gray-600"></div>
                  <div className="h-5 w-20 rounded bg-gray-300 dark:bg-gray-600"></div>
                </div>
                <div className="h-5 w-15 rounded bg-gray-300 dark:bg-gray-600"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (isFinanceOfficer) {
    return null;
  }

  const { totalNetworks, ngos, users, activeMembers } = data;

  return (
    <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5">
      <OverviewCard
        label="Total Networks"
        data={{
          value: compactFormat(totalNetworks.total),
          growthRate: parseFloat(totalNetworks.growthRatePercentage),
        }}
        Icon={icons.Network}
      />

      <OverviewCard
        label="Total Organizations"
        data={{
          value: compactFormat(ngos.total),
          growthRate: parseFloat(ngos.growthRatePercentage),
        }}
        Icon={icons.Organization}
      />

      <OverviewCard
        label="Total Users"
        data={{
          value: compactFormat(users.total),
          growthRate: parseFloat(users.growthRatePercentage),
        }}
        Icon={icons.Users}
      />

      <OverviewCard
        label="Active Members"
        data={{
          value: compactFormat(activeMembers.total),
          growthRate: parseFloat(activeMembers.growthRatePercentage),
        }}
        Icon={icons.Documents}
      />
    </div>
  );
}
