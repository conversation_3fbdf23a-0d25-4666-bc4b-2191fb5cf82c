import { Skeleton } from "@/components/ui/skeleton";

export function FinancialMetricsSkeleton() {
  return (
    <div className="space-y-6">
      {/* First Row Skeleton */}
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={`row1-${i}`}
            className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800"
          >
            <div className="space-y-4">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-16" />
              <div className="flex items-center">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="ml-2 h-3 w-3 rounded-full" />
              </div>
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        ))}
      </div>

      {/* Second Row Skeleton */}
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={`row2-${i}`}
            className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800"
          >
            <div className="space-y-4">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-20" />
              <div className="flex items-center">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="ml-2 h-3 w-3 rounded-full" />
              </div>
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        ))}
      </div>

      {/* Third Row Skeleton */}
      <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={`row3-${i}`}
            className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800"
          >
            <div className="space-y-4">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-16" />
              <div className="flex items-center">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="ml-2 h-3 w-3 rounded-full" />
              </div>
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row Skeleton */}
      <div className="grid gap-4 md:grid-cols-2">
        <div className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800">
          <Skeleton className="mb-4 h-6 w-32" />
          <Skeleton className="h-64 w-full rounded-lg" />
          <div className="mt-4 flex justify-center space-x-4">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
        <div className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800">
          <Skeleton className="mb-4 h-6 w-32" />
          <Skeleton className="h-64 w-full rounded-lg" />
        </div>
      </div>

      {/* Table Skeleton */}
      <div className="rounded-lg border bg-white p-6 shadow-sm dark:bg-gray-800">
        <Skeleton className="mb-6 h-6 w-32" />
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={`table-${i}`} className="grid grid-cols-4 gap-4">
              <Skeleton className="h-4" />
              <Skeleton className="h-4" />
              <Skeleton className="h-4" />
              <Skeleton className="h-4" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}