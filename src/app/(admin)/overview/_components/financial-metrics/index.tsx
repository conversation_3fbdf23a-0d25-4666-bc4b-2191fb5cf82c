"use client";

import React, { useEffect, useState } from "react";
import {
  DollarSign,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  Users,
} from "lucide-react";
import { motion } from "framer-motion";
import { HiOutlineBuildingOffice } from "react-icons/hi2";
import dynamic from "next/dynamic";
import { formatToMWK } from "@/lib/format-number";
import { FinanceOfficerStatistics } from "@/types/finance-officer-statistics.types";
import { getFinanceOfficerStatistics } from "@/services/statistics.services";

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface FinancialMetric {
  title: string;
  value: string;
  change: string;
  changeType: "positive" | "negative" | "neutral";
  icon: React.ComponentType<any>;
  description: string;
}

export function FinancialMetrics() {
  const [statistics, setStatistics] = useState<FinanceOfficerStatistics | null>(
    null,
  );
  const token = localStorage.getItem("accessToken")!;

  useEffect(() => {
    const fetchStatistics = async () => {
      const stats = await getFinanceOfficerStatistics(token);
      if (stats.data) {
        setStatistics(stats.data);
      }
    };

    fetchStatistics();
  }, []);

  if (!statistics || !statistics.revenue) {
    return <div>Loading...</div>;
  }

  const { revenue, ratio, monthlyRevenue } = statistics;

  const financialMetrics: FinancialMetric[] = [
    {
      title: "Total Revenue This Year",
      value: formatToMWK(revenue.total),
      change: "+12.5%",
      changeType: "positive",
      icon: DollarSign,
      description: "Total membership revenue collected",
    },
    {
      title: "Outstanding Invoices",
      value: "MWK 8,450",
      change: "-2.3%",
      changeType: "negative",
      icon: AlertCircle,
      description: "Invoices awaiting payment",
    },
    {
      title: "Payments This Month",
      value: "MWK 12,340",
      change: "+8.1%",
      changeType: "positive",
      icon: TrendingUp,
      description: "Payments received this month",
    },
    {
      title: "Overdue Payments",
      value: "MWK 3,120",
      change: "+15.2%",
      changeType: "negative",
      icon: Clock,
      description: "Payments past due date",
    },
    {
      title: "Invoices Generated",
      value: "156",
      change: "+5.7%",
      changeType: "positive",
      icon: FileText,
      description: "Total invoices created",
    },
    {
      title: "Receipts Issued",
      value: "142",
      change: "+3.2%",
      changeType: "positive",
      icon: CheckCircle,
      description: "Receipts generated for payments",
    },
    {
      title: "Penalties Collected",
      value: "MWK 1,850",
      change: "+22.1%",
      changeType: "positive",
      icon: AlertCircle,
      description: "Late payment penalties",
    },
    {
      title: "NGOs Awaiting Invoice",
      value: "23",
      change: "-8.5%",
      changeType: "positive",
      icon: Users,
      description: "NGOs pending invoice generation",
    },
    {
      title: "Revenue by NGO Category",
      value: formatToMWK(revenue.total),
      change: `L: ${ratio.local.toFixed(2)}% | I: ${ratio.international.toFixed(2)}%`,
      changeType: "positive",
      icon: HiOutlineBuildingOffice,
      description: `Local: ${formatToMWK(revenue.local)} | International: ${formatToMWK(revenue.international)}`,
    },
  ];

  // Remove Outstanding Invoices card from metrics
  const filteredMetrics = financialMetrics.filter(
    (metric) => metric.title !== "Outstanding Invoices",
  );

  return (
    <>
      {/* User-Friendly Graphs - now at the top */}
      <div className="mb-6 grid gap-6 lg:grid-cols-2">
        {/* Revenue by NGO Category Pie Chart */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Revenue by NGO Category
          </h3>
          <ApexChart
            type="pie"
            width="100%"
            height={320}
            options={{
              labels: ["Local NGOs", "International NGOs"],
              legend: { position: "bottom" },
              colors: ["#10B981", "#6366F1"],
              chart: { fontFamily: "inherit" },
            }}
            series={[revenue.local, revenue.international]}
          />
        </div>
        {/* Outstanding Invoices Pie Chart (replaces card) */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Outstanding Invoices Breakdown
          </h3>
          <ApexChart
            type="pie"
            width="100%"
            height={320}
            options={{
              labels: ["Paid", "Pending", "Overdue"],
              legend: { position: "bottom" },
              colors: ["#10B981", "#F59E0B", "#EF4444"],
              chart: { fontFamily: "inherit" },
            }}
            series={[60, 25, 15]} // mock data: 60 paid, 25 pending, 15 overdue
          />
        </div>
        {/* Revenue Growth Line Chart (mocked as revenue over months) */}
        <div className="col-span-2 rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Revenue Growth
          </h3>
          <ApexChart
            type="line"
            width="100%"
            height={320}
            options={{
              xaxis: {
                categories: monthlyRevenue.map((item) => item.month),
                title: { text: "Month" },
              },
              yaxis: { title: { text: "Revenue (MWK)" } },
              chart: { fontFamily: "inherit" },
              colors: ["#10B981"],
              stroke: { curve: "smooth", width: 3 },
              markers: { size: 5 },
              legend: { show: false },
            }}
            series={[
              {
                name: "Revenue",
                data: monthlyRevenue.map((item) => item.revenue),
              },
            ]}
          />
        </div>
      </div>

      {/* Metrics Cards - now below the graphs, without Outstanding Invoices */}
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-6 xl:grid-cols-4 2xl:gap-7.5">
        {filteredMetrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark"
          >
            <div className="flex items-center justify-between">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
                <metric.icon className="h-6 w-6 text-primary" />
              </div>
              <span
                className={`text-sm font-medium ${
                  metric.changeType === "positive"
                    ? "text-success"
                    : metric.changeType === "negative"
                      ? "text-danger"
                      : "text-gray-500"
                }`}
              >
                {metric.change}
              </span>
            </div>

            <div className="mt-4">
              <h3 className="text-lg font-semibold text-dark dark:text-white">
                {metric.value}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {metric.title}
              </p>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                {metric.description}
              </p>
            </div>
          </motion.div>
        ))}
      </div>
    </>
  );
}
