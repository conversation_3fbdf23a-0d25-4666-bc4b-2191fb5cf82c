"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineDocumentText,
  HiOutlineClock,
  HiOutlineExclamationTriangle,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineUserGroup,
  HiOutlineBuildingOffice,
  HiOutlineCalendar,
  HiOutlineArrowUp,
  HiOutlineArrowDown,
} from "react-icons/hi2";

interface UnfinishedApplication {
  id: string;
  organizationName: string;
  applicationType: "local" | "international" | "renewal" | "amendment";
  currentStep: string;
  progress: number;
  daysIncomplete: number;
  lastActivity: string;
  assignedTo?: string;
  priority: "high" | "medium" | "low";
  missingDocuments: string[];
  estimatedCompletion: string;
}

interface UnfinishedAnalytics {
  totalIncomplete: number;
  averageDaysIncomplete: number;
  applicationsByType: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
  applicationsByStep: Array<{
    step: string;
    count: number;
    percentage: number;
  }>;
  applicationsByPriority: Array<{
    priority: string;
    count: number;
    color: string;
  }>;
  recentIncomplete: UnfinishedApplication[];
  stuckApplications: UnfinishedApplication[];
}

export default function UnfinishedApplications() {
  const [analytics, setAnalytics] = useState<UnfinishedAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchAnalytics = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const mockAnalytics: UnfinishedAnalytics = {
        totalIncomplete: 234,
        averageDaysIncomplete: 18.5,
        applicationsByType: [
          { type: "Local Organization", count: 89, percentage: 38.0 },
          { type: "International Organization", count: 67, percentage: 28.6 },
          { type: "Renewal", count: 45, percentage: 19.2 },
          { type: "Amendment", count: 33, percentage: 14.1 },
        ],
        applicationsByStep: [
          { step: "Document Upload", count: 78, percentage: 33.3 },
          { step: "Payment Processing", count: 56, percentage: 23.9 },
          { step: "Review & Verification", count: 45, percentage: 19.2 },
          { step: "Final Approval", count: 34, percentage: 14.5 },
          { step: "Registration", count: 21, percentage: 9.0 },
        ],
        applicationsByPriority: [
          { priority: "High", count: 67, color: "bg-red-500" },
          { priority: "Medium", count: 98, color: "bg-yellow-500" },
          { priority: "Low", count: 69, color: "bg-green-500" },
        ],
        recentIncomplete: [
          {
            id: "1",
            organizationName: "Malawi Health Initiative",
            applicationType: "local",
            currentStep: "Document Upload",
            progress: 65,
            daysIncomplete: 25,
            lastActivity: "2024-01-10",
            assignedTo: "John Doe",
            priority: "high",
            missingDocuments: [
              "Certificate of Registration",
              "Financial Statements",
            ],
            estimatedCompletion: "2024-01-25",
          },
          {
            id: "2",
            organizationName: "Education for All Malawi",
            applicationType: "international",
            currentStep: "Payment Processing",
            progress: 80,
            daysIncomplete: 12,
            lastActivity: "2024-01-12",
            assignedTo: "Jane Smith",
            priority: "medium",
            missingDocuments: ["Payment Receipt"],
            estimatedCompletion: "2024-01-18",
          },
          {
            id: "3",
            organizationName: "Sustainable Agriculture Network",
            applicationType: "renewal",
            currentStep: "Review & Verification",
            progress: 45,
            daysIncomplete: 8,
            lastActivity: "2024-01-14",
            assignedTo: "Mike Johnson",
            priority: "low",
            missingDocuments: ["Updated Constitution"],
            estimatedCompletion: "2024-01-20",
          },
        ],
        stuckApplications: [
          {
            id: "4",
            organizationName: "Youth Empowerment Foundation",
            applicationType: "local",
            currentStep: "Document Upload",
            progress: 30,
            daysIncomplete: 45,
            lastActivity: "2023-12-01",
            priority: "high",
            missingDocuments: ["All Required Documents"],
            estimatedCompletion: "TBD",
          },
          {
            id: "5",
            organizationName: "Women's Rights Initiative",
            applicationType: "international",
            currentStep: "Payment Processing",
            progress: 70,
            daysIncomplete: 38,
            lastActivity: "2023-12-10",
            priority: "high",
            missingDocuments: ["Payment Confirmation"],
            estimatedCompletion: "TBD",
          },
        ],
      };

      setAnalytics(mockAnalytics);
      setIsLoading(false);
    };

    fetchAnalytics();
  }, []);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "low":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getApplicationTypeColor = (type: string) => {
    switch (type) {
      case "local":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "international":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
      case "renewal":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "amendment":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  if (isLoading) {
    return (
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Loading unfinished applications...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="text-center">
          <HiOutlineDocumentText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No data available
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Unfinished applications data will appear here.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Unfinished Applications
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Track incomplete applications and identify bottlenecks
          </p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Incomplete
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.totalIncomplete)}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900/20">
              <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-red-500" />
            <span className="ml-1 text-sm text-red-600 dark:text-red-400">
              +12.5%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last month
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Avg Days Incomplete
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.averageDaysIncomplete}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900/20">
              <HiOutlineClock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowDown className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              -2.3 days
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last month
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                High Priority
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.applicationsByPriority.find(
                  (p) => p.priority === "High",
                )?.count || 0}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900/20">
              <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-red-500" />
            <span className="ml-1 text-sm text-red-600 dark:text-red-400">
              +8.2%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last month
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Stuck Applications
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.stuckApplications.length}
              </p>
            </div>
            <div className="rounded-full bg-orange-100 p-3 dark:bg-orange-900/20">
              <HiOutlineXCircle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-red-500" />
            <span className="ml-1 text-sm text-red-600 dark:text-red-400">
              +15.3%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last month
            </span>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Applications by Type */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Applications by Type
          </h3>
          <div className="space-y-3">
            {analytics.applicationsByType.map((type, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3 h-3 w-3 rounded-full bg-blue-500"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {type.type}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full bg-blue-600"
                      style={{ width: `${type.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {type.count} ({type.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Applications by Step */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Applications by Step
          </h3>
          <div className="space-y-3">
            {analytics.applicationsByStep.map((step, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3 h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {step.step}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full bg-green-600"
                      style={{ width: `${step.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {step.count} ({step.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Incomplete Applications */}
      <div className="rounded-xl bg-white shadow-lg dark:bg-gray-dark">
        <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Incomplete Applications
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Organization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Current Step
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Progress
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Days Incomplete
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Assigned To
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-dark">
              {analytics.recentIncomplete.map((app) => (
                <tr
                  key={app.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <td className="px-6 py-4">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {app.organizationName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Last activity:{" "}
                        {new Date(app.lastActivity).toLocaleDateString()}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getApplicationTypeColor(app.applicationType)}`}
                    >
                      {app.applicationType.charAt(0).toUpperCase() +
                        app.applicationType.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {app.currentStep}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="mr-2 h-2 w-16 rounded-full bg-gray-200 dark:bg-gray-700">
                        <div
                          className="h-2 rounded-full bg-blue-600"
                          style={{ width: `${app.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {app.progress}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {app.daysIncomplete} days
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getPriorityColor(app.priority)}`}
                    >
                      {app.priority.charAt(0).toUpperCase() +
                        app.priority.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {app.assignedTo || "Unassigned"}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Stuck Applications Alert */}
      {analytics.stuckApplications.length > 0 && (
        <div className="rounded-xl bg-red-50 p-6 dark:bg-red-900/20">
          <div className="flex items-center">
            <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            <h3 className="ml-3 text-lg font-semibold text-red-900 dark:text-red-300">
              Stuck Applications Requiring Attention
            </h3>
          </div>
          <div className="mt-4 space-y-3">
            {analytics.stuckApplications.map((app) => (
              <div
                key={app.id}
                className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-dark"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {app.organizationName}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Stuck at: {app.currentStep} • {app.daysIncomplete} days
                      incomplete
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Missing: {app.missingDocuments.join(", ")}
                    </p>
                  </div>
                  <span
                    className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getPriorityColor(app.priority)}`}
                  >
                    {app.priority.charAt(0).toUpperCase() +
                      app.priority.slice(1)}{" "}
                    Priority
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
