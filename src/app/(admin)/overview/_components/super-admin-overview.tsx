"use client";

import { useEffect, useState } from "react";
import { Suspense } from "react";
import { getSuperAdminStatistics } from "@/services/statistics.services";
import { SuperAdminStatistics } from "@/types/super-admin-statistics.types";
import { NGOsPerformance } from "@/components/Charts/payments-overview";
import { ApplicationStatus } from "@/components/Charts/used-devices";
import { UserAlerts } from "@/components/Charts/weeks-profit";
import { TopChannels } from "@/components/Tables/top-channels";
import { TopChannelsSkeleton } from "@/components/Tables/top-channels/skeleton";
import { RegionLabels } from "./region-labels";
import { ChatsCard } from "./chats-card";
import { OverviewCardsGroup } from "./overview-cards";

export function SuperAdminOverview() {
  const [data, setData] = useState<SuperAdminStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const token = localStorage.getItem("accessToken")!;
      try {
        const statisticsResponse = await getSuperAdminStatistics(token);
        if (statisticsResponse.status === "success") {
          setData(statisticsResponse.data);
        }
      } catch (error) {
        console.error("Error fetching super admin data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-4 md:space-y-6 2xl:space-y-7.5">
        <div className="animate-pulse">
          <OverviewCardsGroup />
          <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
            <div className="col-span-12 h-64 rounded-lg bg-gray-200 dark:bg-gray-700 xl:col-span-7"></div>
            <div className="col-span-12 h-64 rounded-lg bg-gray-200 dark:bg-gray-700 xl:col-span-5"></div>
            <div className="col-span-12 h-64 rounded-lg bg-gray-200 dark:bg-gray-700 xl:col-span-5"></div>
            <div className="col-span-12 h-64 rounded-lg bg-gray-200 dark:bg-gray-700 xl:col-span-7"></div>
            <div className="col-span-12 h-64 rounded-lg bg-gray-200 dark:bg-gray-700 xl:col-span-8"></div>
            <div className="col-span-12 h-64 rounded-lg bg-gray-200 dark:bg-gray-700 xl:col-span-4"></div>
          </div>
        </div>
      </div>
    );
  }

  const dateRange = data?.dateRange;
  const formattedDateRange = dateRange
    ? `${new Date(dateRange.from).toLocaleDateString()} - ${new Date(dateRange.to).toLocaleDateString()}`
    : '';

  return (
    <div className="space-y-4 md:space-y-6 2xl:space-y-7.5">
      {/* Overview Cards - Statistics Summary */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Network Statistics</h2>
          {formattedDateRange && (
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Period: {formattedDateRange}
            </div>
          )}
        </div>
        <OverviewCardsGroup />
      </div>

      <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
        {/* NGOs Performance Chart */}
        <NGOsPerformance className="col-span-12 xl:col-span-7" />

        {/* User Alerts */}
        <UserAlerts className="col-span-12 xl:col-span-5" />

        {/* Application Status */}
        <ApplicationStatus className="col-span-12 xl:col-span-5" />

        {/* Region Labels */}
        <RegionLabels />

        {/* Top Channels Table */}
        <div className="col-span-12 grid xl:col-span-8">
          <Suspense fallback={<TopChannelsSkeleton />}>
            <TopChannels />
          </Suspense>
        </div>

        {/* Chats Card - Network Analytics */}
        <Suspense fallback={null}>
          <ChatsCard />
        </Suspense>
      </div>

      {data && (
        <div className="text-xs text-right text-gray-500 dark:text-gray-400">
          Last updated: {new Date(data.lastUpdated).toLocaleString()}
        </div>
      )}
    </div>
  );
}
