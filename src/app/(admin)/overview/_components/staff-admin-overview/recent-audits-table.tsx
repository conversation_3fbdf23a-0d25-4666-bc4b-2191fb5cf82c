import React from 'react';

interface AuditLog {
  id: string;
  userName: string;
  userEmail: string;
  userRole: string;
  action: string;
  details: string;
  status: string;
  timeAgo: string;
  exactTime: string;
  ipAddress: string;
  actionFormatted: string;
}

interface RecentAuditsTableProps {
  audits: AuditLog[];
}

export function RecentAuditsTable({ audits }: RecentAuditsTableProps) {
  if (!audits || audits.length === 0) {
    return (
      <div className="rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
        <h2 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
          Recent Audit Logs
        </h2>
        <p className="text-gray-600 dark:text-gray-400">No recent audit logs found.</p>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-white shadow-md dark:bg-gray-800">
      <div className="p-6">
        <h2 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
          Recent Audit Logs
        </h2>
      </div>
      <div className="overflow-hidden">
        <div className="overflow-y-auto max-h-[400px]">
          <table className="w-full table-auto">
            <thead className="sticky top-0 border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Time
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {audits.map((audit) => (
                <tr key={audit.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="whitespace-nowrap px-6 py-4">
                    <div className="flex flex-col">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {audit.userName}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {audit.userRole}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-col">
                      <span
                        className={`inline-flex rounded-full px-2.5 py-0.5 text-xs font-semibold ${
                          audit.status === 'success'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                            : audit.status === 'warning'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                            : audit.status === 'error'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                            : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                        }`}
                      >
                        {audit.actionFormatted}
                      </span>
                      <span className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        {audit.details}
                      </span>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col">
                      <span>{audit.timeAgo}</span>
                      <span className="text-xs text-gray-400 dark:text-gray-500" title={audit.exactTime}>
                        {audit.ipAddress}
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
