import { cn } from "@/lib/utils";
import React from "react";

interface OverviewCardProps {
  title: string;
  value: number;
  percentage?: string;
  isNegative?: boolean;
}

export function OverviewCard({
  title,
  value,
  percentage,
  isNegative = false,
}: OverviewCardProps) {
  return (
    <div className="rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
      <h3 className="mb-2 text-sm font-medium text-gray-600 dark:text-gray-400">
        {title}
      </h3>
      <div className="flex items-end justify-between">
        <p className="text-2xl font-bold text-gray-900 dark:text-white">
          {value}
        </p>
        {percentage && (
          <span
            className={cn(
              "flex items-center text-sm font-medium",
              isNegative
                ? "text-red-600 dark:text-red-400"
                : "text-green-600 dark:text-green-400"
            )}
          >
            {percentage}
            {isNegative ? (
              <svg
                className="ml-1 h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            ) : (
              <svg
                className="ml-1 h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 15l7-7 7 7"
                ></path>
              </svg>
            )}
          </span>
        )}
      </div>
    </div>
  );
}
