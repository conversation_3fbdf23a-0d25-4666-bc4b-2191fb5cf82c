"use client";

import React from "react";

export default function ProjectUploadsAnalytics() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Project Uploads Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Track project uploads, views, and engagement across NGOs
          </p>
        </div>
      </div>

      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Project Uploads Analytics
          </h3>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Analytics component will be implemented here.
          </p>
        </div>
      </div>
    </div>
  );
}
