"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineEnvelope,
  HiOutlineDevicePhoneMobile,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineChartBar,
  HiOutlineUserGroup,
  HiOutlineArrowUp,
  HiOutlineArrowDown,
  HiOutlineBell,
} from "react-icons/hi2";

interface Notification {
  id: string;
  type: "sms" | "email";
  recipient: string;
  subject: string;
  content: string;
  sentTime: string;
  deliveredTime?: string;
  status: "sent" | "delivered" | "failed" | "pending";
  deliveryAttempts: number;
  category: "system" | "marketing" | "reminder" | "alert" | "newsletter";
  userType: string;
  campaignId?: string;
}

interface NotificationAnalytics {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  deliveryRate: number;
  averageDeliveryTime: number;
  notificationsByType: Array<{
    type: string;
    count: number;
    percentage: number;
    color: string;
  }>;
  notificationsByCategory: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  notificationsByUserType: Array<{
    userType: string;
    count: number;
    percentage: number;
  }>;
  recentNotifications: Notification[];
  topCampaigns: Array<{
    name: string;
    type: string;
    sent: number;
    delivered: number;
    rate: number;
  }>;
  deliveryTrends: Array<{
    date: string;
    sent: number;
    delivered: number;
    failed: number;
  }>;
  hourlyDistribution: Array<{ hour: string; count: number }>;
}

export default function NotificationAnalytics() {
  const [analytics, setAnalytics] = useState<NotificationAnalytics | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [selectedType, setSelectedType] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");

  useEffect(() => {
    const fetchAnalytics = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const mockAnalytics: NotificationAnalytics = {
        totalSent: 15420,
        totalDelivered: 14280,
        totalFailed: 1140,
        deliveryRate: 92.6,
        averageDeliveryTime: 2.3,
        notificationsByType: [
          {
            type: "Email",
            count: 8920,
            percentage: 57.9,
            color: "bg-blue-500",
          },
          { type: "SMS", count: 6500, percentage: 42.1, color: "bg-green-500" },
        ],
        notificationsByCategory: [
          { category: "System", count: 4560, percentage: 29.6 },
          { category: "Marketing", count: 3890, percentage: 25.2 },
          { category: "Reminder", count: 3240, percentage: 21.0 },
          { category: "Alert", count: 2340, percentage: 15.2 },
          { category: "Newsletter", count: 1390, percentage: 9.0 },
        ],
        notificationsByUserType: [
          { userType: "NGO Admin", count: 5670, percentage: 36.8 },
          { userType: "Staff Registry", count: 3890, percentage: 25.2 },
          { userType: "Finance Officer", count: 2340, percentage: 15.2 },
          { userType: "Super Admin", count: 1890, percentage: 12.3 },
          { userType: "CSO Chair", count: 1230, percentage: 8.0 },
          { userType: "Other", count: 400, percentage: 2.6 },
        ],
        recentNotifications: [
          {
            id: "1",
            type: "email",
            recipient: "<EMAIL>",
            subject: "Application Status Update",
            content: "Your application has been reviewed and approved.",
            sentTime: "2024-01-15T10:30:00Z",
            deliveredTime: "2024-01-15T10:30:05Z",
            status: "delivered",
            deliveryAttempts: 1,
            category: "system",
            userType: "NGO Admin",
            campaignId: "camp_001",
          },
          {
            id: "2",
            type: "sms",
            recipient: "+265888123456",
            subject: "Payment Reminder",
            content: "Your membership fee is due. Please make payment.",
            sentTime: "2024-01-15T09:15:00Z",
            deliveredTime: "2024-01-15T09:15:02Z",
            status: "delivered",
            deliveryAttempts: 1,
            category: "reminder",
            userType: "Finance Officer",
            campaignId: "camp_002",
          },
          {
            id: "3",
            type: "email",
            recipient: "<EMAIL>",
            subject: "Monthly Newsletter",
            content: "Stay updated with the latest CONGOMA news and events.",
            sentTime: "2024-01-15T08:45:00Z",
            deliveredTime: "2024-01-15T08:45:08Z",
            status: "delivered",
            deliveryAttempts: 1,
            category: "newsletter",
            userType: "Staff Registry",
            campaignId: "camp_003",
          },
          {
            id: "4",
            type: "sms",
            recipient: "+265999789012",
            subject: "System Alert",
            content: "System maintenance scheduled for tonight at 2 AM.",
            sentTime: "2024-01-15T08:30:00Z",
            status: "failed",
            deliveryAttempts: 3,
            category: "alert",
            userType: "Super Admin",
            campaignId: "camp_004",
          },
          {
            id: "5",
            type: "email",
            recipient: "<EMAIL>",
            subject: "Welcome to CONGOMA",
            content: "Thank you for joining CONGOMA. Welcome aboard!",
            sentTime: "2024-01-15T07:30:00Z",
            deliveredTime: "2024-01-15T07:30:12Z",
            status: "delivered",
            deliveryAttempts: 1,
            category: "marketing",
            userType: "NGO Admin",
            campaignId: "camp_005",
          },
        ],
        topCampaigns: [
          {
            name: "Application Status Updates",
            type: "Email",
            sent: 2340,
            delivered: 2210,
            rate: 94.4,
          },
          {
            name: "Payment Reminders",
            type: "SMS",
            sent: 1890,
            delivered: 1750,
            rate: 92.6,
          },
          {
            name: "Monthly Newsletter",
            type: "Email",
            sent: 1560,
            delivered: 1480,
            rate: 94.9,
          },
          {
            name: "System Alerts",
            type: "SMS",
            sent: 890,
            delivered: 820,
            rate: 92.1,
          },
          {
            name: "Welcome Series",
            type: "Email",
            sent: 670,
            delivered: 640,
            rate: 95.5,
          },
        ],
        deliveryTrends: [
          { date: "2024-01-09", sent: 1200, delivered: 1110, failed: 90 },
          { date: "2024-01-10", sent: 1350, delivered: 1250, failed: 100 },
          { date: "2024-01-11", sent: 1180, delivered: 1090, failed: 90 },
          { date: "2024-01-12", sent: 1420, delivered: 1320, failed: 100 },
          { date: "2024-01-13", sent: 1280, delivered: 1190, failed: 90 },
          { date: "2024-01-14", sent: 1560, delivered: 1450, failed: 110 },
          { date: "2024-01-15", sent: 1380, delivered: 1280, failed: 100 },
        ],
        hourlyDistribution: [
          { hour: "00:00", count: 45 },
          { hour: "04:00", count: 23 },
          { hour: "08:00", count: 156 },
          { hour: "12:00", count: 234 },
          { hour: "16:00", count: 189 },
          { hour: "20:00", count: 98 },
        ],
      };

      setAnalytics(mockAnalytics);
      setIsLoading(false);
    };

    fetchAnalytics();
  }, []);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "sent":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "email":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "sms":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "system":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
      case "marketing":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      case "reminder":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "alert":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "newsletter":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  if (isLoading) {
    return (
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Loading notification analytics...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="text-center">
          <HiOutlineEnvelope className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No data available
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Notification analytics data will appear here.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            SMS/Email Notifications Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Track communication metrics and delivery performance
          </p>
        </div>

        <div className="flex gap-2">
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="rounded border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
          >
            <option value="all">All Types</option>
            <option value="email">Email</option>
            <option value="sms">SMS</option>
          </select>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="rounded border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
          >
            <option value="all">All Categories</option>
            <option value="system">System</option>
            <option value="marketing">Marketing</option>
            <option value="reminder">Reminder</option>
            <option value="alert">Alert</option>
            <option value="newsletter">Newsletter</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Sent
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.totalSent)}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900/20">
              <HiOutlineEnvelope className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              +12.5%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Delivered
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.totalDelivered)}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900/20">
              <HiOutlineCheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              +8.2%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Delivery Rate
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.deliveryRate}%
              </p>
            </div>
            <div className="rounded-full bg-purple-100 p-3 dark:bg-purple-900/20">
              <HiOutlineChartBar className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              +2.1%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Avg Delivery Time
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.averageDeliveryTime}s
              </p>
            </div>
            <div className="rounded-full bg-orange-100 p-3 dark:bg-orange-900/20">
              <HiOutlineClock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowDown className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              -0.5s
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Notifications by Type */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Notifications by Type
          </h3>
          <div className="space-y-3">
            {analytics.notificationsByType.map((type, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className={`mr-3 h-3 w-3 rounded-full ${type.color}`}
                  ></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {type.type}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${type.percentage}%`,
                        backgroundColor: type.color
                          .replace("bg-", "")
                          .includes("500")
                          ? `var(--${type.color.replace("bg-", "").replace("-500", "")}-500)`
                          : type.color,
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {type.count} ({type.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Notifications by Category */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Notifications by Category
          </h3>
          <div className="space-y-3">
            {analytics.notificationsByCategory.map((category, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3 h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {category.category}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full bg-green-600"
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {category.count} ({category.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Notifications */}
      <div className="rounded-xl bg-white shadow-lg dark:bg-gray-dark">
        <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Notifications
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Recipient
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Subject
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Sent Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Attempts
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-dark">
              {analytics.recentNotifications.map((notification) => (
                <tr
                  key={notification.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <td className="px-6 py-4">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {notification.recipient}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {notification.userType}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getTypeColor(notification.type)}`}
                    >
                      {notification.type.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getCategoryColor(notification.category)}`}
                    >
                      {notification.category.charAt(0).toUpperCase() +
                        notification.category.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {notification.subject}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {new Date(notification.sentTime).toLocaleString()}
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(notification.status)}`}
                    >
                      {notification.status.charAt(0).toUpperCase() +
                        notification.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {notification.deliveryAttempts}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Top Campaigns */}
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Top Campaigns
        </h3>
        <div className="space-y-3">
          {analytics.topCampaigns.map((campaign, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="mr-3 rounded-full bg-gray-100 p-2 dark:bg-gray-700">
                  <HiOutlineBell className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {campaign.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {campaign.type}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatNumber(campaign.sent)} sent
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {campaign.rate}% delivery rate
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Delivery Trends Chart */}
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Delivery Trends (Last 7 Days)
        </h3>
        <div className="h-64">
          <div className="flex h-full items-end justify-between space-x-2">
            {analytics.deliveryTrends.map((trend, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="mb-2 text-xs text-gray-500 dark:text-gray-400">
                  {trend.delivered}
                </div>
                <div
                  className="w-8 rounded-t bg-green-500"
                  style={{
                    height: `${(trend.delivered / Math.max(...analytics.deliveryTrends.map((t) => t.delivered))) * 200}px`,
                  }}
                ></div>
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  {new Date(trend.date).toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
