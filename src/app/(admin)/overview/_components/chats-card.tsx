import { useEffect, useState } from "react";
import {
  HiOutlineUsers,
  HiOutlineBuildingOffice2,
  HiOutlineChartBar,
  HiOutlineGlobeAlt,
  HiOutlineArrowTrendingUp,
  HiOutlineArrowTrendingDown,
  HiOutlineInformationCircle,
} from "react-icons/hi2";
import { getSuperAdminStatistics } from "@/services/statistics.services";
import { SuperAdminStatistics } from "@/types/super-admin-statistics.types";

interface NetworkMetric {
  name: string;
  value: number;
  change: number;
  icon: React.ReactNode;
  color: string;
  description: string;
  trend: "up" | "down";
}

export function ChatsCard() {
  const [data, setData] = useState<SuperAdminStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      const token = localStorage.getItem("accessToken")!;
      try {
        const statisticsResponse = await getSuperAdminStatistics(token);
        if (statisticsResponse.status === "success") {
          setData(statisticsResponse.data);
        }
      } catch (error) {
        console.error("Error fetching network analytics data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Create network metrics from the fetched data
  const getNetworkMetrics = (): NetworkMetric[] => {
    if (!data) {
      return [];
    }

    return [
      {
        name: "Total Networks",
        value: data.totalNetworks.total,
        change: parseFloat(data.totalNetworks.growthRatePercentage),
        icon: <HiOutlineBuildingOffice2 className="size-5" />,
        color: "text-blue-600",
        description: "Active network organizations",
        trend:
          parseFloat(data.totalNetworks.growthRatePercentage) >= 0
            ? "up"
            : "down",
      },
      {
        name: "Active Members",
        value: data.activeMembers.total,
        change: parseFloat(data.activeMembers.growthRatePercentage),
        icon: <HiOutlineUsers className="size-5" />,
        color: "text-green-600",
        description: "Registered network participants",
        trend:
          parseFloat(data.activeMembers.growthRatePercentage) >= 0
            ? "up"
            : "down",
      },
      {
        name: "Sector Networks",
        value: data.sectorNetworks.total,
        change: parseFloat(data.sectorNetworks.growthRatePercentage),
        icon: <HiOutlineGlobeAlt className="size-5" />,
        color: "text-blue-600",
        description: "Specialized sector groups",
        trend:
          parseFloat(data.sectorNetworks.growthRatePercentage) >= 0
            ? "up"
            : "down",
      },
      {
        name: "District Networks",
        value: data.districtNetworks.total,
        change: parseFloat(data.districtNetworks.growthRatePercentage),
        icon: <HiOutlineChartBar className="size-5" />,
        color: "text-orange-600",
        description: "Geographic network coverage",
        trend:
          parseFloat(data.districtNetworks.growthRatePercentage) >= 0
            ? "up"
            : "down",
      },
    ];
  };
  if (isLoading) {
    return (
      <div className="col-span-12 rounded-[10px] bg-white py-6 shadow-1 dark:bg-gray-dark dark:shadow-card xl:col-span-4">
        <div className="mb-5.5 px-7.5">
          <div className="flex items-center justify-between">
            <div className="h-6 w-40 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-5 w-5 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700"></div>
          </div>
          <div className="mt-1 h-4 w-64 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
        </div>
        <div className="px-7.5">
          <div className="grid grid-cols-2 gap-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <div
                key={index}
                className="animate-pulse rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800"
              >
                <div className="flex items-center justify-between">
                  <div className="h-5 w-5 rounded-full bg-gray-300 dark:bg-gray-600"></div>
                  <div className="h-4 w-10 rounded bg-gray-300 dark:bg-gray-600"></div>
                </div>
                <div className="mt-2">
                  <div className="h-6 w-20 rounded bg-gray-300 dark:bg-gray-600"></div>
                  <div className="mt-1 h-4 w-24 rounded bg-gray-300 dark:bg-gray-600"></div>
                </div>
                <div className="mt-2 h-1 w-full rounded-full bg-gray-200 dark:bg-gray-700"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const networkMetrics = getNetworkMetrics();

  return (
    <div className="col-span-12 rounded-[10px] bg-white py-6 shadow-1 dark:bg-gray-dark dark:shadow-card xl:col-span-4">
      <div className="mb-5.5 px-7.5">
        <div className="flex items-center justify-between">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            Network Analytics
          </h2>
          <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <HiOutlineInformationCircle className="size-5" />
          </button>
        </div>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Real-time network performance metrics
          {data && (
            <span className="ml-1 text-xs text-gray-400">
              (Last updated: {new Date(data.lastUpdated).toLocaleDateString()})
            </span>
          )}
        </p>
      </div>

      {/* Network Metrics Grid */}
      <div className="px-7.5">
        <div className="grid grid-cols-2 gap-3">
          {networkMetrics.map((metric, index) => (
            <div
              key={metric.name}
              className="group cursor-pointer rounded-lg border border-gray-200 bg-gray-50 p-3 transition-all hover:scale-105 hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
            >
              <div className="flex items-center justify-between">
                <div className={`${metric.color}`}>{metric.icon}</div>
                <div className="flex items-center gap-1">
                  {metric.trend === "up" ? (
                    <HiOutlineArrowTrendingUp className="size-3 text-green-500" />
                  ) : (
                    <HiOutlineArrowTrendingDown className="size-3 text-red-500" />
                  )}
                  <span
                    className={`text-xs font-medium ${
                      metric.change >= 0 ? "text-green-600" : "text-red-600"
                    }`}
                  >
                    {metric.change >= 0 ? "+" : ""}
                    {metric.change}%
                  </span>
                </div>
              </div>
              <div className="mt-2">
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  {metric.value.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {metric.name}
                </div>
                <div className="mt-1 text-xs text-gray-400 opacity-0 transition-opacity group-hover:opacity-100 dark:text-gray-500">
                  {metric.description}
                </div>
              </div>
              <div className="mt-2 h-1 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className={`h-1 rounded-full transition-all ${
                    metric.trend === "up" ? "bg-green-500" : "bg-red-500"
                  }`}
                  style={{
                    width: `${Math.min(Math.abs(metric.change) * 2, 100)}%`,
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 px-7.5">
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800">
          <h3 className="mb-2 text-sm font-semibold text-gray-900 dark:text-white">
            Quick Actions
          </h3>
          <div className="flex gap-2">
            <button className="flex-1 rounded-md bg-primary px-3 py-2 text-xs font-medium text-white transition-colors hover:bg-primary/90">
              View All Networks
            </button>
            <button className="flex-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
              Export Data
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
