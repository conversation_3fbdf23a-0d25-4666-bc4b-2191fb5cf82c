"use client";

import { useState, useEffect } from "react";
import { getCurrentUser } from "@/utils/auth.utils";
import {
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineUserGroup,
  HiOutlineBell,
  HiOutlineCalendar,
  HiOutlineDocumentText,
  HiOutlineChartBar,
  HiOutlineUsers,
  HiOutlineExclamationTriangle,
  HiOutlineEye,
  HiOutlineMagnifyingGlass,
  HiOutlineClipboardDocumentList,
  HiOutlineCalendarDays,
  HiOutlineDocumentCheck,
  HiOutlineUser,
  HiOutlineFlag,
  HiOutlineShieldCheck,
  HiOutlineClipboardDocument,
  HiOutlineBuildingOffice2,
  HiOutlineGlobeAlt,
  HiOutlineHome,
} from "react-icons/hi2";
import dynamic from "next/dynamic";

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface ProgrammesOfficerStatus {
  assessmentStats: {
    pendingDetailedAssessment: number;
    reviewedThisWeek: number;
    returnedForCorrections: number;
  };
  complianceStatus: Array<{
    ngoName: string;
    status: "compliant" | "non_compliant" | "pending_review";
    lastReviewDate: string;
    issues: string[];
  }>;
  missingDocuments: Array<{
    ngoName: string;
    missingDocs: string[];
    daysOverdue: number;
    priority: "high" | "medium" | "low";
  }>;
  backgroundChecks: Array<{
    ngoName: string;
    status: "pending" | "complete" | "failed";
    initiatedDate: string;
    completedDate?: string;
  }>;
  certificatesIssued: Array<{
    ngoName: string;
    certificateType: string;
    issueDate: string;
    expiryDate: string;
    status: "active" | "expired" | "suspended";
  }>;
  constitutionReviews: Array<{
    ngoName: string;
    status: "reviewed" | "pending" | "rejected";
    reviewDate?: string;
    reviewer: string;
    comments: string;
  }>;
  sectorAllocation: Array<{
    ngoName: string;
    currentSector: string;
    requestedSector: string;
    status: "approved" | "pending" | "rejected";
    allocationDate?: string;
  }>;
  flaggedNGOs: Array<{
    ngoName: string;
    flagType:
      | "suspicious_docs"
      | "non_compliance"
      | "incomplete_info"
      | "expired_certificates";
    flagDate: string;
    description: string;
    severity: "high" | "medium" | "low";
  }>;
}

export function ProgrammesOfficerOverview() {
  const [status, setStatus] = useState<ProgrammesOfficerStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching Programmes Officer data
    const fetchProgrammesData = async () => {
      try {
        // In a real app, this would be an API call
        const mockData: ProgrammesOfficerStatus = {
          assessmentStats: {
            pendingDetailedAssessment: 15,
            reviewedThisWeek: 23,
            returnedForCorrections: 8,
          },
          complianceStatus: [
            {
              ngoName: "Youth Empowerment Initiative",
              status: "compliant",
              lastReviewDate: "2024-10-15",
              issues: [],
            },
            {
              ngoName: "Community Health Partners",
              status: "non_compliant",
              lastReviewDate: "2024-10-10",
              issues: ["Missing annual report", "Incomplete board member info"],
            },
            {
              ngoName: "Education for All",
              status: "pending_review",
              lastReviewDate: "2024-10-18",
              issues: ["Under review"],
            },
          ],
          missingDocuments: [
            {
              ngoName: "Youth Empowerment Initiative",
              missingDocs: [
                "Affidavit of Board Members",
                "Financial Statement 2023",
              ],
              daysOverdue: 5,
              priority: "high",
            },
            {
              ngoName: "Community Health Partners",
              missingDocs: ["Annual Report 2023"],
              daysOverdue: 3,
              priority: "medium",
            },
            {
              ngoName: "Education for All",
              missingDocs: ["Registration Certificate"],
              daysOverdue: 7,
              priority: "high",
            },
          ],
          backgroundChecks: [
            {
              ngoName: "Youth Empowerment Initiative",
              status: "complete",
              initiatedDate: "2024-09-15",
              completedDate: "2024-10-01",
            },
            {
              ngoName: "Community Health Partners",
              status: "pending",
              initiatedDate: "2024-10-10",
            },
            {
              ngoName: "Education for All",
              status: "failed",
              initiatedDate: "2024-09-20",
              completedDate: "2024-10-05",
            },
          ],
          certificatesIssued: [
            {
              ngoName: "Youth Empowerment Initiative",
              certificateType: "Registration Certificate",
              issueDate: "2024-01-15",
              expiryDate: "2025-01-15",
              status: "active",
            },
            {
              ngoName: "Community Health Partners",
              certificateType: "Compliance Certificate",
              issueDate: "2023-06-10",
              expiryDate: "2024-06-10",
              status: "expired",
            },
            {
              ngoName: "Education for All",
              certificateType: "Program Certificate",
              issueDate: "2024-03-20",
              expiryDate: "2025-03-20",
              status: "active",
            },
          ],
          constitutionReviews: [
            {
              ngoName: "Youth Empowerment Initiative",
              status: "reviewed",
              reviewDate: "2024-10-15",
              reviewer: "John Doe",
              comments: "Constitution meets all requirements",
            },
            {
              ngoName: "Community Health Partners",
              status: "pending",
              reviewer: "Jane Smith",
              comments: "Awaiting submission",
            },
            {
              ngoName: "Education for All",
              status: "rejected",
              reviewDate: "2024-10-12",
              reviewer: "Mike Johnson",
              comments: "Multiple sections need revision",
            },
          ],
          sectorAllocation: [
            {
              ngoName: "Youth Empowerment Initiative",
              currentSector: "Youth Development",
              requestedSector: "Youth Development",
              status: "approved",
              allocationDate: "2024-01-15",
            },
            {
              ngoName: "Community Health Partners",
              currentSector: "Health",
              requestedSector: "Health",
              status: "pending",
            },
            {
              ngoName: "Education for All",
              currentSector: "Education",
              requestedSector: "Education",
              status: "approved",
              allocationDate: "2024-02-20",
            },
          ],
          flaggedNGOs: [
            {
              ngoName: "Community Health Partners",
              flagType: "suspicious_docs",
              flagDate: "2024-10-15",
              description: "Inconsistent financial statements",
              severity: "high",
            },
            {
              ngoName: "Education for All",
              flagType: "non_compliance",
              flagDate: "2024-10-12",
              description: "Failed to submit required reports",
              severity: "medium",
            },
            {
              ngoName: "Youth Empowerment Initiative",
              flagType: "expired_certificates",
              flagDate: "2024-10-10",
              description: "Registration certificate expired",
              severity: "low",
            },
          ],
        };

        setStatus(mockData);
      } catch (error) {
        console.error("Error fetching Programmes Officer data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProgrammesData();
  }, []);

  if (isLoading || !status) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
        {Array.from({ length: 10 }).map((_, i) => (
          <div
            key={i}
            className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
          >
            <div className="animate-pulse">
              <div className="h-4 w-24 rounded bg-gray-300 dark:bg-gray-600"></div>
              <div className="mt-2 h-8 w-16 rounded bg-gray-300 dark:bg-gray-600"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "compliant":
      case "complete":
      case "reviewed":
      case "approved":
      case "active":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "pending":
      case "pending_review":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "non_compliant":
      case "failed":
      case "rejected":
      case "expired":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      case "medium":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "low":
        return "text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      case "medium":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "low":
        return "text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  return (
    <div className="space-y-6">
      {/* User-Friendly Graphs - at the top */}
      <div className="mb-6 grid gap-6 lg:grid-cols-2">
        {/* Compliance Status Pie Chart */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Compliance Status
          </h3>
          <ApexChart
            type="pie"
            width="100%"
            height={320}
            options={{
              labels: ["Compliant", "Non-Compliant", "Pending Review"],
              legend: { position: "bottom" },
              colors: ["#10B981", "#EF4444", "#F59E0B"],
              chart: { fontFamily: "inherit" },
            }}
            series={[
              status.complianceStatus.filter((c) => c.status === "compliant")
                .length,
              status.complianceStatus.filter(
                (c) => c.status === "non_compliant",
              ).length,
              status.complianceStatus.filter(
                (c) => c.status === "pending_review",
              ).length,
            ]}
          />
        </div>
        {/* Assessments Bar Chart */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Assessments Overview
          </h3>
          <ApexChart
            type="bar"
            width="100%"
            height={320}
            options={{
              xaxis: {
                categories: [
                  "Pending Assessment",
                  "Reviewed This Week",
                  "Returned for Corrections",
                ],
                title: { text: "Assessment Status" },
              },
              yaxis: { title: { text: "Count" } },
              chart: { fontFamily: "inherit" },
              colors: ["#6366F1"],
              legend: { show: false },
            }}
            series={[
              {
                name: "Assessments",
                data: [
                  status.assessmentStats.pendingDetailedAssessment,
                  status.assessmentStats.reviewedThisWeek,
                  status.assessmentStats.returnedForCorrections,
                ],
              },
            ]}
          />
        </div>
      </div>
      {/* Assessment Statistics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {/* Pending Detailed Assessment */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending Assessment
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.assessmentStats.pendingDetailedAssessment}
              </p>
            </div>
            <HiOutlineClock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>

        {/* Reviewed This Week */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Reviewed This Week
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.assessmentStats.reviewedThisWeek}
              </p>
            </div>
            <HiOutlineCheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        {/* Returned for Corrections */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Returned for Corrections
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {status.assessmentStats.returnedForCorrections}
              </p>
            </div>
            <HiOutlineXCircle className="h-8 w-8 text-red-600" />
          </div>
        </div>
      </div>

      {/* Compliance Status and Missing Documents */}
      <div className="grid gap-4 lg:grid-cols-2">
        {/* Compliance Status by NGO */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineShieldCheck className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Compliance Status
            </h3>
          </div>
          <div className="space-y-3">
            {status.complianceStatus.map((compliance, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {compliance.ngoName}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Last review: {compliance.lastReviewDate}
                    </p>
                    {compliance.issues.length > 0 && (
                      <div className="mt-1">
                        {compliance.issues.map((issue, i) => (
                          <p
                            key={i}
                            className="text-xs text-red-600 dark:text-red-400"
                          >
                            • {issue}
                          </p>
                        ))}
                      </div>
                    )}
                  </div>
                  <span
                    className={`ml-2 rounded-full px-2 py-1 text-xs ${getStatusColor(compliance.status)}`}
                  >
                    {compliance.status.replace("_", " ")}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Missing Documents */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineDocumentCheck className="h-5 w-5 text-red-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Missing Documents
            </h3>
          </div>
          <div className="space-y-3">
            {status.missingDocuments.map((doc, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {doc.ngoName}
                    </p>
                    <div className="mt-1 space-y-1">
                      {doc.missingDocs.map((missingDoc, i) => (
                        <p
                          key={i}
                          className="text-xs text-gray-600 dark:text-gray-400"
                        >
                          • {missingDoc}
                        </p>
                      ))}
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {doc.daysOverdue} days overdue
                    </p>
                  </div>
                  <span
                    className={`ml-2 rounded-full px-2 py-1 text-xs ${getPriorityColor(doc.priority)}`}
                  >
                    {doc.priority}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Background Checks and Certificates */}
      <div className="grid gap-4 lg:grid-cols-2">
        {/* Background Check Status */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineUser className="h-5 w-5 text-purple-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Background Checks
            </h3>
          </div>
          <div className="space-y-3">
            {status.backgroundChecks.map((check, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {check.ngoName}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Initiated: {check.initiatedDate}
                    </p>
                    {check.completedDate && (
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Completed: {check.completedDate}
                      </p>
                    )}
                  </div>
                  <span
                    className={`ml-2 rounded-full px-2 py-1 text-xs ${getStatusColor(check.status)}`}
                  >
                    {check.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Certificates Issued */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineClipboardDocument className="h-5 w-5 text-green-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Certificates Issued
            </h3>
          </div>
          <div className="space-y-3">
            {status.certificatesIssued.map((cert, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {cert.ngoName}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {cert.certificateType}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Issued: {cert.issueDate} • Expires: {cert.expiryDate}
                    </p>
                  </div>
                  <span
                    className={`ml-2 rounded-full px-2 py-1 text-xs ${getStatusColor(cert.status)}`}
                  >
                    {cert.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Constitution Reviews and Sector Allocation */}
      <div className="grid gap-4 lg:grid-cols-2">
        {/* Constitution Reviews */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineDocumentText className="h-5 w-5 text-indigo-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Constitution Reviews
            </h3>
          </div>
          <div className="space-y-3">
            {status.constitutionReviews.map((review, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {review.ngoName}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Reviewer: {review.reviewer}
                    </p>
                    {review.reviewDate && (
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Reviewed: {review.reviewDate}
                      </p>
                    )}
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {review.comments}
                    </p>
                  </div>
                  <span
                    className={`ml-2 rounded-full px-2 py-1 text-xs ${getStatusColor(review.status)}`}
                  >
                    {review.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Sector Allocation Status */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineBuildingOffice2 className="h-5 w-5 text-orange-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Sector Allocation
            </h3>
          </div>
          <div className="space-y-3">
            {status.sectorAllocation.map((allocation, index) => (
              <div
                key={index}
                className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {allocation.ngoName}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Current: {allocation.currentSector}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Requested: {allocation.requestedSector}
                    </p>
                    {allocation.allocationDate && (
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Allocated: {allocation.allocationDate}
                      </p>
                    )}
                  </div>
                  <span
                    className={`ml-2 rounded-full px-2 py-1 text-xs ${getStatusColor(allocation.status)}`}
                  >
                    {allocation.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Flagged NGOs */}
      <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-2">
          <HiOutlineFlag className="h-5 w-5 text-red-600" />
          <h3 className="font-medium text-gray-900 dark:text-white">
            Flagged NGOs
          </h3>
        </div>
        <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
          {status.flaggedNGOs.map((flag, index) => (
            <div
              key={index}
              className="rounded-lg border border-gray-200 p-3 dark:border-gray-700"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {flag.ngoName}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {flag.flagType.replace("_", " ")}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {flag.description}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Flagged: {flag.flagDate}
                  </p>
                </div>
                <span
                  className={`ml-2 rounded-full px-2 py-1 text-xs ${getSeverityColor(flag.severity)}`}
                >
                  {flag.severity}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
