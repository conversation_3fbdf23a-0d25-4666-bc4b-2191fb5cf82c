"use client";

import { getStaffAdminStatistics } from "@/services/statistics.services";
import { StaffAdminStatistics } from "@/types/staff-admin-statistics.types";
import { useEffect, useState } from "react";
import { OverviewCard } from "./staff-admin-overview/overview-card";
import { RecentNgosTable } from "./staff-admin-overview/recent-ngos-table";
import { RecentAuditsTable } from "./staff-admin-overview/recent-audits-table";

export function StaffAdminOverview() {
  const [statistics, setStatistics] = useState<StaffAdminStatistics | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStatistics = async () => {
      const token = localStorage.getItem("accessToken")!;
      try {
        const response = await getStaffAdminStatistics(token);
        if (response.status === "success") {
          setStatistics(response.data);
        } else {
          setError(response.message || "Failed to fetch statistics");
        }
      } catch (error) {
        setError("An error occurred while fetching statistics");
      } finally {
        setIsLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5">
        <div className="h-32 animate-pulse rounded-lg bg-gray-200 dark:bg-gray-800"></div>
        <div className="h-32 animate-pulse rounded-lg bg-gray-200 dark:bg-gray-800"></div>
        <div className="h-32 animate-pulse rounded-lg bg-gray-200 dark:bg-gray-800"></div>
        <div className="h-32 animate-pulse rounded-lg bg-gray-200 dark:bg-gray-800"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center rounded-lg bg-white px-6 py-8 shadow-md dark:bg-gray-800">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600">Error</h2>
          <p className="text-gray-600 dark:text-gray-400">{error}</p>
        </div>
      </div>
    );
  }

  if (!statistics) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:gap-7.5">
        <OverviewCard
          title="Total NGOs"
          value={statistics.totalNgos}
          // percentage={statistics.percentages.totalNgosPercentage}
        />
        <OverviewCard
          title="Active NGOs"
          value={statistics.activeNgos}
          // percentage={statistics.percentages.activeNgosPercentage}
        />
        <OverviewCard
          title="Pending NGOs"
          value={statistics.pendingNgos}
          percentage={statistics.percentages.pendingPercentage}
        />
        <OverviewCard
          title="Approved NGOs"
          value={statistics.approvedNgos}
          percentage={statistics.percentages.approvedPercentage}
        />
        <OverviewCard
          title="Rejected NGOs"
          value={statistics.rejectedNgos}
          percentage={statistics.percentages.rejectedPercentage}
        />
      </div>

      <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
        <div className="col-span-12 xl:col-span-8">
          <RecentNgosTable ngos={statistics.recentNgos} />
        </div>
        <div className="col-span-12 xl:col-span-4">
          <RecentAuditsTable audits={statistics.recentAuditLogs} />
        </div>
      </div>
    </div>
  );
}
