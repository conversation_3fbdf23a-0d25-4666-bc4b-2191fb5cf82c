"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineDocumentText,
  HiOutlineExclamationTriangle,
  HiOutlineXCircle,
  HiOutlineCheckCircle,
  HiOutlineClock,
  HiOutlineChartBar,
  HiOutlineUserGroup,
  HiOutlineArrowUp,
  HiOutlineArrowDown,
  HiOutlineCloud,
  HiOutlineShieldExclamation,
} from "react-icons/hi2";

interface UploadError {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  organizationName: string;
  userType: string;
  errorType:
    | "validation"
    | "size_limit"
    | "format"
    | "virus"
    | "network"
    | "server"
    | "permission";
  errorMessage: string;
  uploadTime: string;
  resolvedTime?: string;
  status: "pending" | "resolved" | "failed" | "retry";
  retryCount: number;
  fileCategory:
    | "application"
    | "financial"
    | "constitution"
    | "certificate"
    | "other";
  priority: "high" | "medium" | "low";
}

interface UploadErrorAnalytics {
  totalErrors: number;
  resolvedErrors: number;
  pendingErrors: number;
  averageResolutionTime: number;
  errorsByType: Array<{
    type: string;
    count: number;
    percentage: number;
    color: string;
  }>;
  errorsByCategory: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  errorsByUserType: Array<{
    userType: string;
    count: number;
    percentage: number;
  }>;
  recentErrors: UploadError[];
  topErrorTypes: Array<{ type: string; count: number; resolutionRate: number }>;
  resolutionTrends: Array<{
    date: string;
    errors: number;
    resolved: number;
    pending: number;
  }>;
  fileTypeErrors: Array<{
    fileType: string;
    count: number;
    percentage: number;
  }>;
}

export default function DocumentUploadErrors() {
  const [analytics, setAnalytics] = useState<UploadErrorAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchAnalytics = async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const mockAnalytics: UploadErrorAnalytics = {
        totalErrors: 234,
        resolvedErrors: 189,
        pendingErrors: 45,
        averageResolutionTime: 2.3,
        errorsByType: [
          {
            type: "Validation",
            count: 89,
            percentage: 38.0,
            color: "bg-red-500",
          },
          {
            type: "Size Limit",
            count: 67,
            percentage: 28.6,
            color: "bg-orange-500",
          },
          {
            type: "Format",
            count: 45,
            percentage: 19.2,
            color: "bg-yellow-500",
          },
          { type: "Network", count: 23, percentage: 9.8, color: "bg-blue-500" },
          {
            type: "Server",
            count: 10,
            percentage: 4.3,
            color: "bg-purple-500",
          },
        ],
        errorsByCategory: [
          { category: "Application", count: 98, percentage: 41.9 },
          { category: "Financial", count: 67, percentage: 28.6 },
          { category: "Constitution", count: 45, percentage: 19.2 },
          { category: "Certificate", count: 24, percentage: 10.3 },
        ],
        errorsByUserType: [
          { userType: "NGO Admin", count: 123, percentage: 52.6 },
          { userType: "Staff Registry", count: 67, percentage: 28.6 },
          { userType: "Finance Officer", count: 34, percentage: 14.5 },
          { userType: "Super Admin", count: 10, percentage: 4.3 },
        ],
        recentErrors: [
          {
            id: "1",
            fileName: "financial_statement_2024.pdf",
            fileType: "pdf",
            fileSize: 5242880,
            organizationName: "Malawi Health Initiative",
            userType: "NGO Admin",
            errorType: "validation",
            errorMessage:
              "Document format not supported. Please upload PDF or DOCX format.",
            uploadTime: "2024-01-15T10:30:00Z",
            resolvedTime: "2024-01-15T10:45:00Z",
            status: "resolved",
            retryCount: 1,
            fileCategory: "financial",
            priority: "high",
          },
          {
            id: "2",
            fileName: "constitution_draft.docx",
            fileType: "docx",
            fileSize: 2097152,
            organizationName: "Education for All Malawi",
            userType: "NGO Admin",
            errorType: "size_limit",
            errorMessage:
              "File size exceeds maximum limit of 5MB. Please compress the file.",
            uploadTime: "2024-01-15T09:15:00Z",
            status: "pending",
            retryCount: 2,
            fileCategory: "constitution",
            priority: "medium",
          },
          {
            id: "3",
            fileName: "certificate_of_registration.jpg",
            fileType: "jpg",
            fileSize: 1048576,
            organizationName: "Sustainable Agriculture Network",
            userType: "Staff Registry",
            errorType: "format",
            errorMessage:
              "Image format not supported. Please upload PNG or JPEG format.",
            uploadTime: "2024-01-15T08:45:00Z",
            resolvedTime: "2024-01-15T09:00:00Z",
            status: "resolved",
            retryCount: 1,
            fileCategory: "certificate",
            priority: "low",
          },
          {
            id: "4",
            fileName: "application_form.pdf",
            fileType: "pdf",
            fileSize: 3145728,
            organizationName: "Youth Empowerment Foundation",
            userType: "NGO Admin",
            errorType: "network",
            errorMessage:
              "Network connection lost during upload. Please try again.",
            uploadTime: "2024-01-15T08:30:00Z",
            status: "failed",
            retryCount: 3,
            fileCategory: "application",
            priority: "high",
          },
          {
            id: "5",
            fileName: "annual_report_2023.docx",
            fileType: "docx",
            fileSize: 4194304,
            organizationName: "Women's Rights Initiative",
            userType: "Finance Officer",
            errorType: "server",
            errorMessage:
              "Server temporarily unavailable. Please try again later.",
            uploadTime: "2024-01-15T07:30:00Z",
            status: "pending",
            retryCount: 1,
            fileCategory: "financial",
            priority: "medium",
          },
        ],
        topErrorTypes: [
          { type: "Validation", count: 89, resolutionRate: 85.4 },
          { type: "Size Limit", count: 67, resolutionRate: 92.5 },
          { type: "Format", count: 45, resolutionRate: 88.9 },
          { type: "Network", count: 23, resolutionRate: 78.3 },
          { type: "Server", count: 10, resolutionRate: 90.0 },
        ],
        resolutionTrends: [
          { date: "2024-01-09", errors: 15, resolved: 12, pending: 3 },
          { date: "2024-01-10", errors: 18, resolved: 15, pending: 3 },
          { date: "2024-01-11", errors: 12, resolved: 10, pending: 2 },
          { date: "2024-01-12", errors: 22, resolved: 18, pending: 4 },
          { date: "2024-01-13", errors: 16, resolved: 14, pending: 2 },
          { date: "2024-01-14", errors: 25, resolved: 20, pending: 5 },
          { date: "2024-01-15", errors: 20, resolved: 16, pending: 4 },
        ],
        fileTypeErrors: [
          { fileType: "PDF", count: 89, percentage: 38.0 },
          { fileType: "DOCX", count: 67, percentage: 28.6 },
          { fileType: "JPG", count: 45, percentage: 19.2 },
          { fileType: "PNG", count: 23, percentage: 9.8 },
          { fileType: "Other", count: 10, percentage: 4.3 },
        ],
      };

      setAnalytics(mockAnalytics);
      setIsLoading(false);
    };

    fetchAnalytics();
  }, []);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatFileSize = (bytes: number) => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const getErrorTypeColor = (type: string) => {
    switch (type) {
      case "validation":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "size_limit":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      case "format":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "network":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "server":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
      case "virus":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "permission":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "resolved":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "retry":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "low":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getFileCategoryColor = (category: string) => {
    switch (category) {
      case "application":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "financial":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "constitution":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";
      case "certificate":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  if (isLoading) {
    return (
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Loading upload error analytics...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="text-center">
          <HiOutlineDocumentText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No data available
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Upload error analytics data will appear here.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Document Upload Errors & Issues
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Track upload problems and system issues
          </p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Errors
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.totalErrors)}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900/20">
              <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowDown className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              -8.2%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Resolved
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.resolvedErrors)}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900/20">
              <HiOutlineCheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowUp className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              +12.5%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatNumber(analytics.pendingErrors)}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900/20">
              <HiOutlineClock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowDown className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              -15.3%
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Avg Resolution Time
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analytics.averageResolutionTime}h
              </p>
            </div>
            <div className="rounded-full bg-purple-100 p-3 dark:bg-purple-900/20">
              <HiOutlineChartBar className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <HiOutlineArrowDown className="h-4 w-4 text-green-500" />
            <span className="ml-1 text-sm text-green-600 dark:text-green-400">
              -0.5h
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              from last period
            </span>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Errors by Type */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Errors by Type
          </h3>
          <div className="space-y-3">
            {analytics.errorsByType.map((type, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className={`mr-3 h-3 w-3 rounded-full ${type.color}`}
                  ></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {type.type}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full"
                      style={{
                        width: `${type.percentage}%`,
                        backgroundColor: type.color
                          .replace("bg-", "")
                          .includes("500")
                          ? `var(--${type.color.replace("bg-", "").replace("-500", "")}-500)`
                          : type.color,
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {type.count} ({type.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Errors by Category */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Errors by File Category
          </h3>
          <div className="space-y-3">
            {analytics.errorsByCategory.map((category, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="mr-3 h-3 w-3 rounded-full bg-green-500"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {category.category}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-24 rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full bg-green-600"
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {category.count} ({category.percentage}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Upload Errors */}
      <div className="rounded-xl bg-white shadow-lg dark:bg-gray-dark">
        <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Upload Errors
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  File
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Organization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Error Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Upload Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Priority
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-dark">
              {analytics.recentErrors.map((error) => (
                <tr
                  key={error.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <td className="px-6 py-4">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {error.fileName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {error.fileType.toUpperCase()} •{" "}
                        {formatFileSize(error.fileSize)}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {error.organizationName}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {error.userType}
                      </p>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getErrorTypeColor(error.errorType)}`}
                    >
                      {error.errorType
                        .replace("_", " ")
                        .replace(/\b\w/g, (l) => l.toUpperCase())}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getFileCategoryColor(error.fileCategory)}`}
                    >
                      {error.fileCategory.charAt(0).toUpperCase() +
                        error.fileCategory.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {new Date(error.uploadTime).toLocaleString()}
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(error.status)}`}
                    >
                      {error.status.charAt(0).toUpperCase() +
                        error.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getPriorityColor(error.priority)}`}
                    >
                      {error.priority.charAt(0).toUpperCase() +
                        error.priority.slice(1)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Top Error Types */}
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Top Error Types
        </h3>
        <div className="space-y-3">
          {analytics.topErrorTypes.map((errorType, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="mr-3 rounded-full bg-gray-100 p-2 dark:bg-gray-700">
                  <HiOutlineShieldExclamation className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {errorType.type}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {errorType.count} occurrences
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {errorType.resolutionRate}%
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  resolution rate
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Resolution Trends Chart */}
      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Resolution Trends (Last 7 Days)
        </h3>
        <div className="h-64">
          <div className="flex h-full items-end justify-between space-x-2">
            {analytics.resolutionTrends.map((trend, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="mb-2 text-xs text-gray-500 dark:text-gray-400">
                  {trend.resolved}
                </div>
                <div
                  className="w-8 rounded-t bg-green-500"
                  style={{
                    height: `${(trend.resolved / Math.max(...analytics.resolutionTrends.map((t) => t.resolved))) * 200}px`,
                  }}
                ></div>
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  {new Date(trend.date).toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
