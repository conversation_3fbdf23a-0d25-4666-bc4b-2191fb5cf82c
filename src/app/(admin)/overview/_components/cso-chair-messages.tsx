"use client";

import { useState, useEffect } from "react";
import { HiOutlineChatBubbleLeftRight } from "react-icons/hi2";

interface Message {
  from: string;
  subject: string;
  timestamp: string;
  isRead: boolean;
}

export function CSOChairMessages() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching messages
    const fetchMessages = async () => {
      try {
        // In a real app, this would be an API call
        const mockMessages: Message[] = [
          {
            from: "Women's Rights Network",
            subject: "Request for collaboration on gender equality project",
            timestamp: "1 hour ago",
            isRead: false,
          },
          {
            from: "Rural Development Association",
            subject: "Meeting schedule for next month",
            timestamp: "3 hours ago",
            isRead: true,
          },
          {
            from: "Youth Empowerment Initiative",
            subject: "Feedback on network guidelines",
            timestamp: "1 day ago",
            isRead: true,
          },
        ];
        setMessages(mockMessages);
      } catch (error) {
        console.error("Error fetching messages:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchMessages();
  }, []);

  if (isLoading) {
    return <div>Loading messages...</div>;
  }

  return (
    <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
      <div className="mb-4 flex items-center gap-2">
        <HiOutlineChatBubbleLeftRight className="h-5 w-5 text-purple-600" />
        <h3 className="font-medium text-gray-900 dark:text-white">
          Messages Received
        </h3>
      </div>
      <div className="space-y-3">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`rounded-lg border p-3 ${
              message.isRead
                ? "border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800"
                : "border-blue-200 bg-blue-50 dark:border-blue-700 dark:bg-blue-900"
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {message.from}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {message.subject}
                </p>
              </div>
              {!message.isRead && (
                <div className="h-2 w-2 rounded-full bg-blue-600"></div>
              )}
            </div>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              {message.timestamp}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
