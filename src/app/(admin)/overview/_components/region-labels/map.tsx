"use client";

import { useState, useEffect } from "react";
import {
  HiOutlineArrowTrendingUp,
  HiOutlineArrowTrendingDown,
  HiOutlineInformationCircle,
} from "react-icons/hi2";
import { getSuperAdminStatistics } from "@/services/statistics.services";
import {
  SuperAdminStatistics,
  AreaOfFocus,
  MonthlyGrowth,
} from "@/types/super-admin-statistics.types";

// We'll replace this with actual data from the API
const defaultNgoPerformance = [
  { month: "Jan", ngos: 18, documents: 45, trend: "up", change: 12 },
  { month: "Feb", ngos: 20, documents: 52, trend: "up", change: 15 },
  { month: "Mar", ngos: 22, documents: 58, trend: "up", change: 10 },
  { month: "Apr", ngos: 21, documents: 55, trend: "down", change: -5 },
  { month: "May", ngos: 23, documents: 62, trend: "up", change: 18 },
  { month: "Jun", ngos: 24, documents: 68, trend: "up", change: 8 },
];

export default function NGOAnalytics() {
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [data, setData] = useState<SuperAdminStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [ngoPerformance, setNgoPerformance] = useState(defaultNgoPerformance);
  const [ngoCategories, setNgoCategories] = useState<any[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      const token = localStorage.getItem("accessToken")!;
      try {
        const statisticsResponse = await getSuperAdminStatistics(token);
        if (statisticsResponse.status === "success") {
          setData(statisticsResponse.data);

          // Transform monthly growth data
          const monthlyData = statisticsResponse.data.ngos.monthlyGrowth.map(
            (item: MonthlyGrowth) => {
              return {
                month: item.month.substring(0, 3), // First 3 letters of month name
                ngos: item.registeredNgos,
                documents: Math.round(item.registeredNgos * 2.5), // Estimated documents based on NGOs
                trend:
                  parseFloat(item.growthRatePercentage) >= 0 ? "up" : "down",
                change: parseFloat(item.growthRatePercentage),
              };
            },
          );

          if (monthlyData.length > 0) {
            setNgoPerformance(monthlyData);
          }

          // Transform area of focus data
          const categoriesData = statisticsResponse.data.ngos.areaOfFocus.map(
            (item: AreaOfFocus) => {
              return {
                name: `${item.name} NGOs`,
                count: item.totalNgos,
                percentage: Math.round(
                  (item.totalNgos / statisticsResponse.data.ngos.total) * 100,
                ),
                color: getRandomColor(item.name),
                growth: parseFloat(item.growthRatePercentage),
              };
            },
          );

          if (categoriesData.length > 0) {
            setNgoCategories(categoriesData);
          } else {
            // Default categories if none returned from API
            setNgoCategories([
              {
                name: "Health NGOs",
                count: 8,
                percentage: 33,
                color: "bg-blue-500",
                growth: 15,
              },
              {
                name: "Education NGOs",
                count: 6,
                percentage: 25,
                color: "bg-green-500",
                growth: 8,
              },
              {
                name: "Agriculture NGOs",
                count: 5,
                percentage: 21,
                color: "bg-blue-500",
                growth: -2,
              },
              {
                name: "Other NGOs",
                count: 5,
                percentage: 21,
                color: "bg-orange-500",
                growth: 12,
              },
            ]);
          }
        }
      } catch (error) {
        console.error("Error fetching region analytics data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Helper function to get a consistent color based on category name
  const getRandomColor = (name: string): string => {
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-orange-500",
      "bg-purple-500",
      "bg-red-500",
      "bg-indigo-500",
    ];
    // Simple hash function to get consistent color
    const hash = name
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
          <div className="mb-4 flex items-center justify-between">
            <div className="h-6 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-5 w-5 animate-pulse rounded-full bg-gray-200 dark:bg-gray-700"></div>
          </div>
          <div className="space-y-3">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse rounded-lg p-3">
                <div className="flex items-center space-x-4">
                  <div className="h-4 w-12 rounded bg-gray-300 dark:bg-gray-600"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="h-4 w-20 rounded bg-gray-300 dark:bg-gray-600"></div>
                      <div className="h-4 w-20 rounded bg-gray-300 dark:bg-gray-600"></div>
                      <div className="h-4 w-16 rounded bg-gray-300 dark:bg-gray-600"></div>
                    </div>
                    <div className="mt-1 h-2 rounded-full bg-gray-300 dark:bg-gray-600"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
          <div className="mb-4 flex items-center justify-between">
            <div className="h-6 w-32 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="h-4 w-24 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
          </div>
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="animate-pulse rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div className="h-4 w-32 rounded bg-gray-300 dark:bg-gray-600"></div>
                  <div className="h-4 w-24 rounded bg-gray-300 dark:bg-gray-600"></div>
                </div>
                <div className="mt-2 h-1 rounded-full bg-gray-300 dark:bg-gray-600"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* NGO Performance Chart */}
      <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            NGO Growth
          </h3>
          <div className="relative">
            <button
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <HiOutlineInformationCircle className="size-5" />
            </button>
            {showTooltip && (
              <div className="absolute right-0 top-8 z-10 w-64 rounded-lg bg-gray-900 p-3 text-sm text-white shadow-lg">
                <p>
                  Monthly NGO registration and document submission trends. Click
                  on any month for detailed insights.
                </p>
              </div>
            )}
          </div>
        </div>
        <div className="space-y-3">
          {ngoPerformance.map((data, index) => (
            <div
              key={data.month}
              className={`cursor-pointer rounded-lg p-3 transition-all hover:bg-gray-50 dark:hover:bg-gray-700 ${
                selectedMonth === data.month
                  ? "border border-primary/20 bg-primary/10"
                  : ""
              }`}
              onClick={() =>
                setSelectedMonth(
                  selectedMonth === data.month ? null : data.month,
                )
              }
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 text-sm font-medium text-gray-500 dark:text-gray-400">
                  {data.month}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-300">
                      NGOs: {data.ngos}
                    </span>
                    <span className="text-gray-600 dark:text-gray-300">
                      Documents: {data.documents}
                    </span>
                    <div className="flex items-center gap-1">
                      {data.trend === "up" ? (
                        <HiOutlineArrowTrendingUp className="size-4 text-green-500" />
                      ) : (
                        <HiOutlineArrowTrendingDown className="size-4 text-red-500" />
                      )}
                      <span
                        className={`text-xs font-medium ${
                          data.trend === "up"
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {data.change > 0 ? "+" : ""}
                        {data.change}%
                      </span>
                    </div>
                  </div>
                  <div className="mt-1 h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                    <div
                      className="h-2 rounded-full bg-primary transition-all"
                      style={{
                        width: `${(data.ngos / 24) * 100}%`,
                      }}
                    />
                  </div>
                </div>
              </div>
              {selectedMonth === data.month && (
                <div className="mt-3 rounded-lg bg-gray-50 p-3 dark:bg-gray-700">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        Growth Rate
                      </div>
                      <div className="text-gray-600 dark:text-gray-300">
                        {data.change}% from previous month
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        Document Ratio
                      </div>
                      <div className="text-gray-600 dark:text-gray-300">
                        {(data.documents / data.ngos).toFixed(1)} docs/NGO
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* NGO Distribution */}
      <div className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            NGO Categories
          </h3>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Total: {data?.ngos.total || 0} NGOs
            {data && (
              <span className="ml-2 text-xs">
                ({parseFloat(data.ngos.growthRatePercentage) >= 0 ? "+" : ""}
                {data.ngos.growthRatePercentage}%)
              </span>
            )}
          </div>
        </div>
        <div className="space-y-3">
          {ngoCategories.map((category, index) => (
            <div
              key={category.name}
              className="group cursor-pointer rounded-lg p-3 transition-all hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className={`size-3 rounded-full ${category.color}`}
                  ></div>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {category.name}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {category.count} ({category.percentage}%)
                  </span>
                  <div className="flex items-center gap-1">
                    {category.growth > 0 ? (
                      <HiOutlineArrowTrendingUp className="size-3 text-green-500" />
                    ) : (
                      <HiOutlineArrowTrendingDown className="size-3 text-red-500" />
                    )}
                    <span
                      className={`text-xs ${
                        category.growth > 0 ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {category.growth > 0 ? "+" : ""}
                      {category.growth}%
                    </span>
                  </div>
                </div>
              </div>
              <div className="mt-2 h-1 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className={`h-1 rounded-full ${category.color.replace("bg-", "bg-")}`}
                  style={{ width: `${category.percentage}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
