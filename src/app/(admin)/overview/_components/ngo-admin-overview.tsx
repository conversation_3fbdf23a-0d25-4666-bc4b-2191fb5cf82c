"use client";

import { useState, useEffect } from "react";
import { getCurrentUser } from "@/utils/auth.utils";
import {
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineDocumentText,
  HiOutlineBell,
  HiOutlineFolderOpen,
  HiOutlineArrowDownTray,
  HiOutlineChatBubbleLeftRight,
  HiOutlineCalendar,
  HiOutlineUserGroup,
  HiOutlineExclamationTriangle,
  HiOutlineClipboardDocumentList,
  HiOutlineCurrencyDollar,
  HiOutlineUser,
  HiOutlineEye,
} from "react-icons/hi2";

interface NGOStatus {
  registrationStatus: "Submitted" | "Pending Review" | "Approved" | "Rejected";
  membershipValidity: {
    expirationDate: string;
    daysRemaining: number;
  };
  processingFeeStatus: "Paid" | "Unpaid";
  annualMembershipFeeStatus: "Paid" | "Unpaid";
  pendingActions: string[];
  notificationsCount: number;
  projectsShared: number;
  downloads: {
    certificates: number;
    receipts: number;
  };
  messagesFromCongoma: number;
  upcomingReminders: Array<{
    title: string;
    date: string;
    type: "renewal" | "profile_update" | "document";
  }>;
  groupParticipation: {
    sectorNetwork: boolean;
    districtNetwork: boolean;
  };
  requestStatuses: Array<{
    type: string;
    status: "pending" | "approved" | "rejected";
  }>;
  penaltyWarnings: string[];
  activityLog: {
    lastUpdate: string;
    lastLogin: string;
  };
  linkedBoardMembers: {
    count: number;
    completedSignup: number;
  };
}

export function NGOAdminOverview() {
  const [status, setStatus] = useState<NGOStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching NGO admin data
    const fetchNGOData = async () => {
      try {
        // In a real app, this would be an API call
        const mockData: NGOStatus = {
          registrationStatus: "Approved",
          membershipValidity: {
            expirationDate: "2024-12-31",
            daysRemaining: 45,
          },
          processingFeeStatus: "Paid",
          annualMembershipFeeStatus: "Paid",
          pendingActions: [
            "Submit updated constitution",
            "Upload board member photos",
            "Complete annual report",
          ],
          notificationsCount: 3,
          projectsShared: 12,
          downloads: {
            certificates: 2,
            receipts: 5,
          },
          messagesFromCongoma: 1,
          upcomingReminders: [
            {
              title: "Membership Renewal",
              date: "2024-12-31",
              type: "renewal",
            },
            {
              title: "Annual Report Due",
              date: "2024-11-15",
              type: "document",
            },
          ],
          groupParticipation: {
            sectorNetwork: true,
            districtNetwork: true,
          },
          requestStatuses: [
            { type: "Join CSO", status: "approved" },
            { type: "Name Change", status: "pending" },
            { type: "Profile Edit", status: "approved" },
          ],
          penaltyWarnings: [],
          activityLog: {
            lastUpdate: "2024-10-15",
            lastLogin: "2024-10-20",
          },
          linkedBoardMembers: {
            count: 5,
            completedSignup: 3,
          },
        };

        setStatus(mockData);
      } catch (error) {
        console.error("Error fetching NGO data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNGOData();
  }, []);

  if (isLoading || !status) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
        {Array.from({ length: 15 }).map((_, i) => (
          <div
            key={i}
            className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800"
          >
            <div className="animate-pulse">
              <div className="h-4 w-24 rounded bg-gray-300 dark:bg-gray-600"></div>
              <div className="mt-2 h-8 w-16 rounded bg-gray-300 dark:bg-gray-600"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Approved":
      case "Paid":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "Pending Review":
      case "pending":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "Rejected":
      case "Unpaid":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Approved":
      case "Paid":
        return <HiOutlineCheckCircle className="h-5 w-5 text-green-600" />;
      case "Rejected":
      case "Unpaid":
        return <HiOutlineXCircle className="h-5 w-5 text-red-600" />;
      case "Pending Review":
      case "pending":
        return <HiOutlineClock className="h-5 w-5 text-yellow-600" />;
      default:
        return <HiOutlineClock className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Status Cards */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* Registration Status */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Registration Status
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {status.registrationStatus}
              </p>
            </div>
            {getStatusIcon(status.registrationStatus)}
          </div>
        </div>

        {/* Membership Validity */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Membership Validity
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {status.membershipValidity.daysRemaining} days
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Expires: {status.membershipValidity.expirationDate}
              </p>
            </div>
            <HiOutlineCalendar className="h-5 w-5 text-blue-600" />
          </div>
        </div>

        {/* Processing Fee Status */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Processing Fee
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {status.processingFeeStatus}
              </p>
            </div>
            {getStatusIcon(status.processingFeeStatus)}
          </div>
        </div>

        {/* Annual Membership Fee */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Annual Fee
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {status.annualMembershipFeeStatus}
              </p>
            </div>
            {getStatusIcon(status.annualMembershipFeeStatus)}
          </div>
        </div>
      </div>

      {/* Secondary Information */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {/* Pending Actions */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineDocumentText className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Pending Actions
            </h3>
          </div>
          <div className="space-y-2">
            {status.pendingActions.map((action, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                <span className="text-gray-600 dark:text-gray-400">
                  {action}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Notifications */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <HiOutlineBell className="h-5 w-5 text-blue-600" />
              <h3 className="font-medium text-gray-900 dark:text-white">
                Notifications
              </h3>
            </div>
            <span className="rounded-full bg-red-500 px-2 py-1 text-xs text-white">
              {status.notificationsCount}
            </span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Unread messages and alerts
          </p>
        </div>

        {/* Projects Shared */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineFolderOpen className="h-5 w-5 text-green-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Projects Shared
            </h3>
          </div>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {status.projectsShared}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Submitted projects
          </p>
        </div>

        {/* Downloads */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineArrowDownTray className="h-5 w-5 text-purple-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Downloads
            </h3>
          </div>
          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                Certificates:
              </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {status.downloads.certificates}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                Receipts:
              </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {status.downloads.receipts}
              </span>
            </div>
          </div>
        </div>

        {/* Messages from CONGOMA */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <HiOutlineChatBubbleLeftRight className="h-5 w-5 text-blue-600" />
              <h3 className="font-medium text-gray-900 dark:text-white">
                Messages
              </h3>
            </div>
            {status.messagesFromCongoma > 0 && (
              <span className="rounded-full bg-blue-500 px-2 py-1 text-xs text-white">
                {status.messagesFromCongoma}
              </span>
            )}
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            From CONGOMA
          </p>
        </div>

        {/* Group Participation */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineUserGroup className="h-5 w-5 text-green-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Group Participation
            </h3>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-sm">
              <div
                className={`h-2 w-2 rounded-full ${status.groupParticipation.sectorNetwork ? "bg-green-500" : "bg-gray-300"}`}
              ></div>
              <span className="text-gray-600 dark:text-gray-400">
                Sector Network
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div
                className={`h-2 w-2 rounded-full ${status.groupParticipation.districtNetwork ? "bg-green-500" : "bg-gray-300"}`}
              ></div>
              <span className="text-gray-600 dark:text-gray-400">
                District Network
              </span>
            </div>
          </div>
        </div>

        {/* Request Statuses */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineClipboardDocumentList className="h-5 w-5 text-orange-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Request Statuses
            </h3>
          </div>
          <div className="space-y-2">
            {status.requestStatuses.map((request, index) => (
              <div
                key={index}
                className="flex items-center justify-between text-sm"
              >
                <span className="text-gray-600 dark:text-gray-400">
                  {request.type}
                </span>
                <span
                  className={`rounded-full px-2 py-1 text-xs ${getStatusColor(request.status)}`}
                >
                  {request.status}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Penalty Warnings */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineExclamationTriangle className="h-5 w-5 text-red-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Penalty Warnings
            </h3>
          </div>
          {status.penaltyWarnings.length > 0 ? (
            <div className="space-y-1">
              {status.penaltyWarnings.map((warning, index) => (
                <p
                  key={index}
                  className="text-sm text-red-600 dark:text-red-400"
                >
                  {warning}
                </p>
              ))}
            </div>
          ) : (
            <p className="text-sm text-green-600 dark:text-green-400">
              No warnings
            </p>
          )}
        </div>

        {/* Activity Log */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineEye className="h-5 w-5 text-gray-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Activity Log
            </h3>
          </div>
          <div className="space-y-1 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                Last Update:{" "}
              </span>
              <span className="text-gray-900 dark:text-white">
                {status.activityLog.lastUpdate}
              </span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">
                Last Login:{" "}
              </span>
              <span className="text-gray-900 dark:text-white">
                {status.activityLog.lastLogin}
              </span>
            </div>
          </div>
        </div>

        {/* Linked Board Members */}
        <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
          <div className="mb-3 flex items-center gap-2">
            <HiOutlineUser className="h-5 w-5 text-blue-600" />
            <h3 className="font-medium text-gray-900 dark:text-white">
              Board Members
            </h3>
          </div>
          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Total:</span>
              <span className="font-medium text-gray-900 dark:text-white">
                {status.linkedBoardMembers.count}
              </span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                Completed:
              </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {status.linkedBoardMembers.completedSignup}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Reminders */}
      <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800">
        <div className="mb-4 flex items-center gap-2">
          <HiOutlineCalendar className="h-5 w-5 text-blue-600" />
          <h3 className="font-medium text-gray-900 dark:text-white">
            Upcoming Reminders
          </h3>
        </div>
        <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
          {status.upcomingReminders.map((reminder, index) => (
            <div
              key={index}
              className="flex items-center justify-between rounded-lg border border-gray-200 p-3 dark:border-gray-700"
            >
              <div>
                <p className="font-medium text-gray-900 dark:text-white">
                  {reminder.title}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {reminder.date}
                </p>
              </div>
              <span
                className={`rounded-full px-2 py-1 text-xs ${
                  reminder.type === "renewal"
                    ? "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400"
                    : reminder.type === "profile_update"
                      ? "bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400"
                      : "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400"
                }`}
              >
                {reminder.type.replace("_", " ")}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
