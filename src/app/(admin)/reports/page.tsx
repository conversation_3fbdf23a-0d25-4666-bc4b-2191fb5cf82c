"use client";
import React from "react";

const reports = [
  {
    id: 1,
    title: "Annual Financial Report",
    type: "Finance",
    date: "2024-01-15",
    status: "Approved",
  },
  {
    id: 2,
    title: "Impact Assessment",
    type: "Operations",
    date: "2024-03-10",
    status: "Pending",
  },
  {
    id: 3,
    title: "Compliance Review",
    type: "Legal",
    date: "2024-05-05",
    status: "Rejected",
  },
  {
    id: 4,
    title: "Quarterly Progress Report",
    type: "Operations",
    date: "2024-02-20",
    status: "Approved",
  },
  {
    id: 5,
    title: "Donor Feedback Summary",
    type: "Finance",
    date: "2024-04-12",
    status: "Pending",
  },
  {
    id: 6,
    title: "Risk Assessment Update",
    type: "Legal",
    date: "2024-05-25",
    status: "Approved",
  },
];

const statusStyles: Record<string, string> = {
  Approved: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  Pending:
    "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  Rejected: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
};

export default function ReportsPage() {
  return (
    <div className="mt-4 w-full">
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {reports.map((report) => (
          <div
            key={report.id}
            className="flex flex-col justify-between rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark"
          >
            <div>
              <h2 className="mb-2 text-xl font-bold text-dark dark:text-white">
                {report.title}
              </h2>
              <div className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                Type: {report.type}
              </div>
              <div className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                Date: {report.date}
              </div>
              <span
                className={`inline-block rounded px-3 py-1 text-xs font-semibold ${statusStyles[report.status]}`}
              >
                {report.status}
              </span>
            </div>
            <button className="mt-6 rounded bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
              View
            </button>
          </div>
        ))}
        {reports.length === 0 && (
          <div className="col-span-full rounded bg-gray-100 p-8 text-center text-gray-500 dark:bg-dark-2 dark:text-gray-400">
            No reports found.
          </div>
        )}
      </div>
    </div>
  );
}
