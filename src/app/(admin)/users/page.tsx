"use client";
import React, { useState, useEffect } from "react";
import {
  FaSearch,
  FaEye,
  FaEdit,
  FaTrash,
  Fa<PERSON>ser,
  Fa<PERSON>sers,
  FaUserCheck,
  FaUserClock,
  FaPlus,
  FaFilter,
  FaTimes as XI<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaUserSlash,
} from "react-icons/fa";
import { FaEllipsisVertical } from "react-icons/fa6";
import { format } from "date-fns";
import {
  User,
  PaginatedResponse,
  getUsers,
  searchUsers,
  activateUsers,
  deactivateUsers,
  createStaffMember,
  createNgoAccount,
} from "@/services/users.services";
import { getRoles, Role } from "@/services/role.services";

// Map for converting backend status to UI status
const statusMap: Record<string, string> = {
  active: "Active",
  inactive: "Inactive",
  pending: "Pending",
};

// Map for converting backend roles to UI roles
const roleMap: Record<string, string> = {
  admin: "Admin",
  moderator: "Moderator",
  staff: "Staff",
  user: "User",
  ngo: "NGO",
};

const statusStyles: Record<string, { bg: string; text: string; dot: string }> =
  {
    Active: {
      bg: "bg-green-100 dark:bg-green-900/30",
      text: "text-green-800 dark:text-green-200",
      dot: "bg-green-500",
    },
    Inactive: {
      bg: "bg-red-100 dark:bg-red-900/30",
      text: "text-red-800 dark:text-red-200",
      dot: "bg-red-500",
    },
    Pending: {
      bg: "bg-yellow-100 dark:bg-yellow-900/30",
      text: "text-yellow-800 dark:text-yellow-200",
      dot: "bg-yellow-500",
    },
  };

const roleStyles: Record<string, string> = {
  Admin: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200",
  Moderator: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200",
  User: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",
};

const statusOptions = ["Active", "Inactive", "Pending Organization"];

export default function UsersPage() {
  const token = localStorage.getItem("accessToken") || "";
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [roles, setRoles] = useState<Role[]>([]);
  const [filters, setFilters] = useState({
    role: [] as string[],
    status: [] as string[],
  });
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
  });
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [newUserData, setNewUserData] = useState({
    fullname: "",
    email: "",
    role: "user",
    ngoId: "",
  });

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await getRoles(token);
        if (response.status === "success") {
          setRoles(response.data);
        }
      } catch (error) {
        console.error("Failed to fetch roles:", error);
      }
    };
    fetchRoles();
  }, [token]);

  const toggleFilter = (filterType: keyof typeof filters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter((item) => item !== value)
        : [...prev[filterType], value],
    }));
  };

  const clearFilters = () => {
    setFilters({
      role: [],
      status: [],
    });
  };

  // Filtering is now handled by the API

  // Fetch users when component mounts or filters change
  useEffect(() => {
    fetchUsers();
  }, [filters, pagination.page, pagination.limit]);

  // Search users when search term changes
  useEffect(() => {
    if (searchTerm.trim()) {
      handleSearch();
    } else {
      fetchUsers();
    }
  }, [searchTerm]);

  const fetchUsers = async () => {
    try {
      setLoading(true);

      const roleParam =
        filters.role.length > 0 ? filters.role.join(",") : undefined;
      const statusParam =
        filters.status.length > 0
          ? filters.status.map((s) => s.toLowerCase()).join(",")
          : undefined;

      const response = await getUsers(
        {
          page: pagination.page,
          limit: pagination.limit,
          role: roleParam,
          status: statusParam,
        },
        token,
      );

      if (response.status === "success" && response.data) {
        setUsers(
          Array.isArray(response.data) ? response.data : [response.data],
        );

        // Handle pagination from response
        const paginatedResponse =
          response as unknown as PaginatedResponse<User>;
        if (paginatedResponse.pagination) {
          setPagination({
            page: paginatedResponse.pagination.page,
            limit: paginatedResponse.pagination.limit,
            total: paginatedResponse.pagination.total,
          });
        }
      } else {
        setError("Failed to fetch users");
      }
    } catch (err) {
      console.error("Error fetching users:", err);
      setError("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) return;

    try {
      setLoading(true);
      const response = await searchUsers(searchTerm, token);

      if (response.status === "success" && response.data) {
        setUsers(
          Array.isArray(response.data) ? response.data : [response.data],
        );
      } else {
        setError("Search failed");
      }
    } catch (err) {
      console.error("Error searching users:", err);
      setError("Search failed");
    } finally {
      setLoading(false);
    }
  };

  const toggleMenu = (userId: string) => {
    setOpenMenuId(openMenuId === userId ? null : userId);
  };

  const handleView = (userId: string) => {
    console.log("View user:", userId);
    setOpenMenuId(null);
    // Implement view user functionality
  };

  const handleResetPassword = (userId: string) => {
    console.log("Reset password for user:", userId);
    setOpenMenuId(null);
    // Implement reset password functionality
  };

  const handleActivateDeactivate = async (
    userId: string,
    currentStatus: string,
  ) => {
    try {
      setLoading(true);

      if (currentStatus === "Active") {
        await deactivateUsers([userId], token);
      } else {
        await activateUsers([userId], token);
      }

      await fetchUsers();
      setOpenMenuId(null);
    } catch (err) {
      console.error(
        `Error ${currentStatus === "Active" ? "deactivating" : "activating"} user:`,
        err,
      );
      setError(
        `Failed to ${currentStatus === "Active" ? "deactivate" : "activate"} user`,
      );
    } finally {
      setLoading(false);
    }
  };

  // Calculate statistics
  const totalUsers = pagination.total || users.length;
  const activeUsers = users.filter((user) => user.status === "active").length;
  const recentUsers = users.length > 0 ? Math.floor(totalUsers * 0.2) : 0; // Approximate recent users as 20% of total

  return (
    <div className="container mx-auto w-full rounded-xl bg-white px-4 py-8 shadow-sm dark:border-gray-700 dark:bg-gray-800">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          User Directory
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Manage all registered users in the system
        </p>
      </div>

      {/* Stats Cards */}
      <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-3">
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center">
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900/30">
              <FaUsers className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Users
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {totalUsers}
              </p>
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center">
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900/30">
              <FaUserCheck className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Active Users
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {activeUsers}
              </p>
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center">
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900/30">
              <FaUserClock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Recent Users
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {recentUsers}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="relative flex-1">
          <FaSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search users..."
            className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          />
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            <FaFilter className="h-4 w-4" />
            Filters
            {(filters.role.length > 0 || filters.status.length > 0) && (
              <span className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {filters.role.length + filters.status.length}
              </span>
            )}
          </button>
          <button
            onClick={() => setShowAddUserModal(true)}
            className="flex items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <FaPlus className="h-4 w-4" />
            Add User
          </button>
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Filters
            </h3>
            <div className="flex gap-2">
              <button
                onClick={clearFilters}
                className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
              >
                Clear all
              </button>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
              >
                <XIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Role
              </h4>
              <div className="space-y-2">
                {roles.map((role) => (
                  <div key={role._id} className="flex items-center">
                    <input
                      id={`role-${role._id}`}
                      type="checkbox"
                      checked={filters.role.includes(role._id)}
                      onChange={() => toggleFilter("role", role._id)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`role-${role.name}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {role.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                Status
              </h4>
              <div className="space-y-2">
                {statusOptions.map((status) => (
                  <div key={status} className="flex items-center">
                    <input
                      id={`status-${status}`}
                      type="checkbox"
                      checked={filters.status.includes(status)}
                      onChange={() => toggleFilter("status", status)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    />
                    <label
                      htmlFor={`status-${status}`}
                      className="ml-2 text-sm text-gray-700 dark:text-gray-300"
                    >
                      {status}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {(filters.role.length > 0 || filters.status.length > 0) && (
        <div className="mb-4 flex flex-wrap gap-2">
          {filters.role.map((roleId) => {
            const role = roles.find((r) => r._id === roleId);
            return (
              <span
                key={`role-${roleId}`}
                className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200"
              >
                Role: {role ? role.name : "Unknown"}
                <button
                  onClick={() => toggleFilter("role", roleId)}
                  className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-blue-800 hover:bg-blue-200 dark:text-blue-200 dark:hover:bg-blue-800"
                >
                  <XIcon className="h-3 w-3" />
                </button>
              </span>
            );
          })}
          {filters.status.map((status) => (
            <span
              key={`status-${status}`}
              className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              Status: {status}
              <button
                onClick={() => toggleFilter("status", status)}
                className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full text-green-800 hover:bg-green-200 dark:text-green-200 dark:hover:bg-green-800"
              >
                <XIcon className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Users Table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Date Joined
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {loading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                    </div>
                  </td>
                </tr>
              ) : (
                users.map((user) => {
                  const displayStatus = statusMap[user.status] || user.status;
                  const roleName =
                    typeof user.role === "object" && user.role !== null
                      ? user.role.name
                      : user.role;
                  const displayRole = roleMap[roleName] || roleName;
                  const statusStyle = statusStyles[displayStatus] || {
                    bg: "bg-gray-100 dark:bg-gray-700",
                    text: "text-gray-800 dark:text-gray-200",
                    dot: "bg-gray-400",
                  };

                  return (
                    <tr
                      key={user._id}
                      className="transition-colors hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="flex items-center">
                          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-600">
                            <FaUser className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {user.fullname}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {user.email}
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <span
                          className={`inline-flex rounded-full px-2.5 py-0.5 text-xs font-medium ${roleStyles[displayRole]}`}
                        >
                          {displayRole}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="flex items-center">
                          <span
                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${statusStyle.bg} ${statusStyle.text}`}
                          >
                            <span
                              className={`mr-1.5 h-1.5 w-1.5 rounded-full ${statusStyle.dot}`}
                            ></span>
                            {displayStatus}
                          </span>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                        {/* Using a placeholder for the date since it's not provided in the API */}
                        {formatReadableDate(user.createdAt)}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right">
                        <div className="flex justify-end">
                          <div className="relative">
                            <button
                              onClick={() => toggleMenu(user._id)}
                              className="inline-flex items-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:hover:bg-gray-700 dark:hover:text-gray-300"
                            >
                              <FaEllipsisVertical className="h-4 w-4" />
                            </button>

                            {/* Dropdown menu */}
                            {openMenuId === user._id && (
                              <div className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700">
                                <button
                                  onClick={() => handleView(user._id)}
                                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                >
                                  <FaEye className="mr-3 h-4 w-4 text-gray-500" />
                                  View
                                </button>
                                <button
                                  onClick={() => handleResetPassword(user._id)}
                                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                >
                                  <FaKey className="mr-3 h-4 w-4 text-gray-500" />
                                  Reset Password
                                </button>
                                <button
                                  onClick={() =>
                                    handleActivateDeactivate(
                                      user._id,
                                      displayStatus,
                                    )
                                  }
                                  className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                                >
                                  <FaUserSlash className="mr-3 h-4 w-4 text-gray-500" />
                                  {displayStatus === "Active"
                                    ? "Deactivate"
                                    : "Activate"}
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {!loading && users.length === 0 && (
          <div className="px-6 py-12 text-center">
            <div className="flex flex-col items-center justify-center gap-2">
              <FaSearch className="h-8 w-8 text-gray-400" />
              <p className="text-lg font-medium">No users found</p>
              <p className="text-sm text-gray-500">
                Try adjusting your search or filters
              </p>
            </div>
          </div>
        )}

        {error && (
          <div className="px-6 py-4 text-center text-red-500">
            <p>{error}</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.total > 0 && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{users.length}</span> of{" "}
            <span className="font-medium">{pagination.total}</span> users
          </div>

          <div className="flex space-x-1">
            <button
              onClick={() =>
                setPagination((prev) => ({
                  ...prev,
                  page: Math.max(1, prev.page - 1),
                }))
              }
              disabled={pagination.page === 1}
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              Previous
            </button>

            <button
              onClick={() =>
                setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
              }
              disabled={pagination.page * pagination.limit >= pagination.total}
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Add User Modal */}
      {showAddUserModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="w-full max-w-md rounded-lg bg-white shadow-xl dark:bg-gray-800">
            <div className="p-6">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Add New User
                </h3>
                <button
                  onClick={() => setShowAddUserModal(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <XIcon className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="fullname"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="fullname"
                    value={newUserData.fullname}
                    onChange={(e) =>
                      setNewUserData({
                        ...newUserData,
                        fullname: e.target.value,
                      })
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={newUserData.email}
                    onChange={(e) =>
                      setNewUserData({ ...newUserData, email: e.target.value })
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label
                    htmlFor="role"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Role
                  </label>
                  <select
                    id="role"
                    value={newUserData.role}
                    onChange={(e) =>
                      setNewUserData({ ...newUserData, role: e.target.value })
                    }
                    className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  >
                    {roles.map((role) => (
                      <option key={role._id} value={role._id}>
                        {role.name}
                      </option>
                    ))}
                  </select>
                </div>
                {newUserData.role === "ngo" && (
                  <div>
                    <label
                      htmlFor="ngoId"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      NGO ID
                    </label>
                    <input
                      type="text"
                      id="ngoId"
                      value={newUserData.ngoId}
                      onChange={(e) =>
                        setNewUserData({
                          ...newUserData,
                          ngoId: e.target.value,
                        })
                      }
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                )}
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <button
                  onClick={() => setShowAddUserModal(false)}
                  className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddUser}
                  className="rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Add User
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  async function handleAddUser() {
    if (!newUserData.fullname || !newUserData.email) {
      setError("Name and email are required");
      return;
    }

    try {
      setLoading(true);
      let response;

      if (newUserData.role === "ngo") {
        if (!newUserData.ngoId) {
          setError("NGO ID is required for NGO accounts");
          setLoading(false);
          return;
        }
        // For NGO accounts, we'd typically need a password generation mechanism
        // For now, using a placeholder password that would be changed
        response = await createNgoAccount(
          {
            fullname: newUserData.fullname,
            email: newUserData.email,
            password: "TemporaryPassword123!",
            ngoId: newUserData.ngoId,
          },
          token,
        );
      } else {
        // For staff members
        response = await createStaffMember(
          {
            fullname: newUserData.fullname,
            email: newUserData.email,
            role: newUserData.role,
            ngoId: newUserData.ngoId || undefined,
          },
          token,
        );
      }

      if (response.status === "success") {
        // Reset form and close modal
        setNewUserData({
          fullname: "",
          email: "",
          role: "user",
          ngoId: "",
        });
        setShowAddUserModal(false);

        // Refresh the user list
        await fetchUsers();
      } else {
        setError(response.message || "Failed to create user");
      }
    } catch (err) {
      console.error("Error creating user:", err);
      setError("Failed to create user");
    } finally {
      setLoading(false);
    }
  }

  function formatReadableDate(isoDate: string): string {
    const date = new Date(isoDate);

    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };

    return date.toLocaleDateString("en-US", options);
  }
}
