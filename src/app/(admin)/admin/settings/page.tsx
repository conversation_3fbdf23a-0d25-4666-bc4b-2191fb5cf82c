"use client";
import React, { useState } from "react";
import {
  HiO<PERSON>lineCog,
  HiOutlineShieldCheck,
  HiOutlineEnvelope,
  HiOutlineCloudArrowUp,
  HiOutlineWrenchScrewdriver,
  HiOutlineCheck,
} from "react-icons/hi2";

export default function SystemSettingsPage() {
  const [settings, setSettings] = useState({
    siteName: "CONGOMA Platform",
    maintenanceMode: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    requireTwoFactor: false,
    enableAuditLog: true,
    smtpHost: "smtp.gmail.com",
    smtpPort: 587,
    autoBackup: true,
    backupFrequency: "daily",
    logLevel: "info",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState<
    "idle" | "saving" | "success" | "error"
  >("idle");

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    setSaveStatus("saving");

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setSaveStatus("success");
      setTimeout(() => setSaveStatus("idle"), 3000);
    } catch (error) {
      setSaveStatus("error");
      setTimeout(() => setSaveStatus("idle"), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mt-4 w-full">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-extrabold tracking-tight text-primary">
            System Settings
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Configure system-wide settings and preferences
          </p>
        </div>

        <button
          onClick={handleSave}
          disabled={isLoading}
          className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/90 disabled:opacity-50"
        >
          <HiOutlineCheck className="size-5" />
          {saveStatus === "saving" ? "Saving..." : "Save Settings"}
        </button>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* General Settings */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
              <HiOutlineCog className="size-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              General Settings
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Site Name
              </label>
              <input
                type="text"
                value={settings.siteName}
                onChange={(e) =>
                  handleSettingChange("siteName", e.target.value)
                }
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Maintenance Mode
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Temporarily disable access for maintenance
                </p>
              </div>
              <button
                onClick={() =>
                  handleSettingChange(
                    "maintenanceMode",
                    !settings.maintenanceMode,
                  )
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.maintenanceMode
                    ? "bg-red-500"
                    : "bg-gray-300 dark:bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.maintenanceMode ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
              <HiOutlineShieldCheck className="size-6 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              Security Settings
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Session Timeout (minutes)
              </label>
              <input
                type="number"
                value={settings.sessionTimeout}
                onChange={(e) =>
                  handleSettingChange(
                    "sessionTimeout",
                    parseInt(e.target.value),
                  )
                }
                min="5"
                max="480"
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              />
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Max Login Attempts
              </label>
              <input
                type="number"
                value={settings.maxLoginAttempts}
                onChange={(e) =>
                  handleSettingChange(
                    "maxLoginAttempts",
                    parseInt(e.target.value),
                  )
                }
                min="3"
                max="10"
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Require Two-Factor Auth
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Force 2FA for all users
                </p>
              </div>
              <button
                onClick={() =>
                  handleSettingChange(
                    "requireTwoFactor",
                    !settings.requireTwoFactor,
                  )
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.requireTwoFactor
                    ? "bg-green-500"
                    : "bg-gray-300 dark:bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.requireTwoFactor
                      ? "translate-x-6"
                      : "translate-x-1"
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Enable Audit Log
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Track all system activities
                </p>
              </div>
              <button
                onClick={() =>
                  handleSettingChange(
                    "enableAuditLog",
                    !settings.enableAuditLog,
                  )
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.enableAuditLog
                    ? "bg-green-500"
                    : "bg-gray-300 dark:bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.enableAuditLog ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        {/* Email Configuration */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
              <HiOutlineEnvelope className="size-6 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              Email Configuration
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                SMTP Host
              </label>
              <input
                type="text"
                value={settings.smtpHost}
                onChange={(e) =>
                  handleSettingChange("smtpHost", e.target.value)
                }
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              />
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                SMTP Port
              </label>
              <input
                type="number"
                value={settings.smtpPort}
                onChange={(e) =>
                  handleSettingChange("smtpPort", parseInt(e.target.value))
                }
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              />
            </div>

            <button className="w-full rounded bg-green-500 px-4 py-2 text-white transition hover:bg-green-600">
              Test Email Configuration
            </button>
          </div>
        </div>

        {/* Backup Settings */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/20">
              <HiOutlineCloudArrowUp className="size-6 text-purple-600 dark:text-purple-400" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              Backup Settings
            </h2>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Auto Backup
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Enable automatic backups
                </p>
              </div>
              <button
                onClick={() =>
                  handleSettingChange("autoBackup", !settings.autoBackup)
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.autoBackup
                    ? "bg-green-500"
                    : "bg-gray-300 dark:bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.autoBackup ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Backup Frequency
              </label>
              <select
                value={settings.backupFrequency}
                onChange={(e) =>
                  handleSettingChange("backupFrequency", e.target.value)
                }
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>

            <button className="w-full rounded bg-purple-500 px-4 py-2 text-white transition hover:bg-purple-600">
              Create Backup Now
            </button>
          </div>
        </div>

        {/* System Maintenance */}
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex size-10 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
              <HiOutlineWrenchScrewdriver className="size-6 text-orange-600 dark:text-orange-400" />
            </div>
            <h2 className="text-xl font-bold text-dark dark:text-white">
              System Maintenance
            </h2>
          </div>

          <div className="space-y-4">
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Log Level
              </label>
              <select
                value={settings.logLevel}
                onChange={(e) =>
                  handleSettingChange("logLevel", e.target.value)
                }
                className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
              >
                <option value="error">Error</option>
                <option value="warn">Warning</option>
                <option value="info">Info</option>
                <option value="debug">Debug</option>
              </select>
            </div>

            <button className="w-full rounded bg-orange-500 px-4 py-2 text-white transition hover:bg-orange-600">
              Clear System Cache
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
