"use client";

import React, { useState } from "react";
import {
  HiOutlineQuestionMarkCircle,
  HiOutlineChevronDown,
  HiOutlineChevronRight,
} from "react-icons/hi2";

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

export default function AdminFAQPage() {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const faqData: FAQItem[] = [
    {
      id: "1",
      question: "How do I manage user roles and permissions?",
      answer:
        "User roles and permissions can be managed through the System Settings page. Navigate to Admin > System Settings > User Management. Here you can create new roles, assign permissions, and manage existing user accounts.",
      category: "User Management",
    },
    {
      id: "2",
      question: "What are the different organization types supported?",
      answer:
        "The platform supports two main organization types: Local Organizations (based in Malawi) and International Organizations (based outside Malawi). Each type has different registration requirements and fee structures.",
      category: "Organizations",
    },
    {
      id: "3",
      question: "How do I generate financial reports?",
      answer:
        "Financial reports can be generated from the Financial Management section. Navigate to Financial > Reports to access various report types including payment summaries, revenue reports, and organization payment status.",
      category: "Financial",
    },
    {
      id: "4",
      question: "What is the document approval process?",
      answer:
        "Documents uploaded by organizations go through a three-stage approval process: 1) Initial review by staff, 2) Technical verification, 3) Final approval by admin. The process typically takes 3-5 business days.",
      category: "Documents",
    },
    {
      id: "5",
      question: "How do I create and manage networks?",
      answer:
        "Networks can be created and managed through the Content Management section. Navigate to Networks > Sector Network or District Network. You can create new networks, add members, and manage network activities.",
      category: "Networks",
    },
  ];

  const toggleItem = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Frequently Asked Questions
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Find answers to common questions about the platform
          </p>
        </div>
      </div>

      {/* FAQ List */}
      <div className="space-y-4">
        {faqData.map((item) => (
          <div
            key={item.id}
            className="rounded-lg bg-white shadow-sm dark:bg-gray-800"
          >
            <button
              onClick={() => toggleItem(item.id)}
              className="flex w-full items-center justify-between p-6 text-left"
            >
              <div className="flex-1">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {item.question}
                </h3>
                <div className="mt-2">
                  <span className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    {item.category}
                  </span>
                </div>
              </div>
              <div className="ml-4">
                {expandedItems.has(item.id) ? (
                  <HiOutlineChevronDown className="h-5 w-5 text-gray-500" />
                ) : (
                  <HiOutlineChevronRight className="h-5 w-5 text-gray-500" />
                )}
              </div>
            </button>

            {expandedItems.has(item.id) && (
              <div className="border-t border-gray-200 px-6 pb-6 pt-4 dark:border-gray-700">
                <p className="leading-relaxed text-gray-600 dark:text-gray-400">
                  {item.answer}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Quick Links */}
      <div className="rounded-lg bg-blue-50 p-6 dark:bg-blue-900/20">
        <h3 className="mb-4 text-lg font-semibold text-blue-900 dark:text-blue-300">
          Need More Help?
        </h3>
        <p className="text-sm text-blue-700 dark:text-blue-400">
          Contact the system administrator or refer to the system documentation
          for additional support.
        </p>
      </div>
    </div>
  );
}
