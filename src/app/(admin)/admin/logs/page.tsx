"use client";
import React, { useState, useEffect } from "react";
import {
  HiOutlineDocumentText,
  HiOutlineMagnifyingGlass,
  HiOutlineFunnel,
  HiOutlineArrowDownTray,
  HiOutlineTrash,
  HiOutlineEye,
  HiOutlineExclamationTriangle,
  HiOutlineInformationCircle,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
} from "react-icons/hi2";

interface LogEntry {
  id: string;
  timestamp: string;
  level: "error" | "warn" | "info" | "debug";
  message: string;
  user: string;
  ip: string;
  action: string;
  details?: string;
}

export default function SystemLogsPage() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<LogEntry[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLevel, setSelectedLevel] = useState<string>("all");
  const [selectedDate, setSelectedDate] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);

  // Mock data for demonstration
  useEffect(() => {
    const mockLogs: LogEntry[] = [
      {
        id: "1",
        timestamp: "2024-01-15 10:30:25",
        level: "error",
        message: "Database connection failed",
        user: "<EMAIL>",
        ip: "*************",
        action: "LOGIN_ATTEMPT",
        details: "Connection timeout after 30 seconds",
      },
      {
        id: "2",
        timestamp: "2024-01-15 10:28:15",
        level: "warn",
        message: "High memory usage detected",
        user: "system",
        ip: "127.0.0.1",
        action: "SYSTEM_MONITOR",
        details: "Memory usage at 85%",
      },
      {
        id: "3",
        timestamp: "2024-01-15 10:25:42",
        level: "info",
        message: "User login successful",
        user: "<EMAIL>",
        ip: "************",
        action: "USER_LOGIN",
        details: "Login from Chrome browser",
      },
      {
        id: "4",
        timestamp: "2024-01-15 10:20:18",
        level: "debug",
        message: "API request processed",
        user: "api_user",
        ip: "********",
        action: "API_CALL",
        details: "GET /api/organizations",
      },
      {
        id: "5",
        timestamp: "2024-01-15 10:15:33",
        level: "error",
        message: "File upload failed",
        user: "<EMAIL>",
        ip: "*************",
        action: "FILE_UPLOAD",
        details: "File size exceeds limit",
      },
      {
        id: "6",
        timestamp: "2024-01-15 10:10:55",
        level: "info",
        message: "Backup completed",
        user: "system",
        ip: "127.0.0.1",
        action: "SYSTEM_BACKUP",
        details: "Daily backup to cloud storage",
      },
      {
        id: "7",
        timestamp: "2024-01-15 10:05:12",
        level: "warn",
        message: "Slow query detected",
        user: "system",
        ip: "127.0.0.1",
        action: "DATABASE_QUERY",
        details: "Query took 2.5 seconds",
      },
      {
        id: "8",
        timestamp: "2024-01-15 10:00:00",
        level: "info",
        message: "System startup",
        user: "system",
        ip: "127.0.0.1",
        action: "SYSTEM_STARTUP",
        details: "All services started successfully",
      },
    ];

    setLogs(mockLogs);
    setFilteredLogs(mockLogs);
    setIsLoading(false);
  }, []);

  // Filter logs based on search term and level
  useEffect(() => {
    let filtered = logs;

    if (searchTerm) {
      filtered = filtered.filter(
        (log) =>
          log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
          log.action.toLowerCase().includes(searchTerm.toLowerCase()),
      );
    }

    if (selectedLevel !== "all") {
      filtered = filtered.filter((log) => log.level === selectedLevel);
    }

    if (selectedDate) {
      filtered = filtered.filter((log) =>
        log.timestamp.startsWith(selectedDate),
      );
    }

    setFilteredLogs(filtered);
  }, [logs, searchTerm, selectedLevel, selectedDate]);

  const getLevelIcon = (level: string) => {
    switch (level) {
      case "error":
        return <HiOutlineXCircle className="size-4 text-red-500" />;
      case "warn":
        return (
          <HiOutlineExclamationTriangle className="size-4 text-yellow-500" />
        );
      case "info":
        return <HiOutlineInformationCircle className="size-4 text-blue-500" />;
      case "debug":
        return <HiOutlineCheckCircle className="size-4 text-gray-500" />;
      default:
        return <HiOutlineInformationCircle className="size-4 text-gray-500" />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "warn":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      case "info":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "debug":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const handleExport = () => {
    const csvContent =
      "data:text/csv;charset=utf-8," +
      "Timestamp,Level,Message,User,IP,Action,Details\n" +
      filteredLogs
        .map(
          (log) =>
            `"${log.timestamp}","${log.level}","${log.message}","${log.user}","${log.ip}","${log.action}","${log.details || ""}"`,
        )
        .join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute(
      "download",
      `system-logs-${new Date().toISOString().split("T")[0]}.csv`,
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleClearLogs = () => {
    if (
      confirm(
        "Are you sure you want to clear all logs? This action cannot be undone.",
      )
    ) {
      setLogs([]);
      setFilteredLogs([]);
    }
  };

  if (isLoading) {
    return (
      <div className="mt-4 w-full">
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Loading logs...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-4 w-full">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-extrabold tracking-tight text-primary">
            System Logs
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Monitor system activities and troubleshoot issues
          </p>
        </div>

        <div className="flex gap-3">
          <button
            onClick={handleExport}
            className="flex items-center gap-2 rounded-lg bg-green-500 px-4 py-2 text-white transition hover:bg-green-600"
          >
            <HiOutlineArrowDownTray className="size-5" />
            Export Logs
          </button>
          <button
            onClick={handleClearLogs}
            className="flex items-center gap-2 rounded-lg bg-red-500 px-4 py-2 text-white transition hover:bg-red-600"
          >
            <HiOutlineTrash className="size-5" />
            Clear Logs
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <div className="grid gap-4 md:grid-cols-4">
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Search
            </label>
            <div className="relative">
              <HiOutlineMagnifyingGlass className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full rounded border border-gray-300 bg-white py-2 pl-10 pr-3 text-dark placeholder-gray-400 focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white dark:placeholder-gray-500"
              />
            </div>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Log Level
            </label>
            <select
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
              className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
            >
              <option value="all">All Levels</option>
              <option value="error">Error</option>
              <option value="warn">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Date
            </label>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-dark focus:border-primary focus:outline-none dark:bg-dark-2 dark:text-white"
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={() => {
                setSearchTerm("");
                setSelectedLevel("all");
                setSelectedDate("");
              }}
              className="w-full rounded bg-gray-500 px-4 py-2 text-white transition hover:bg-gray-600"
            >
              <HiOutlineFunnel className="mr-2 inline size-4" />
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Logs Table */}
      <div className="rounded-xl bg-white shadow-lg dark:bg-gray-dark">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Message
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  IP Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-dark">
              {filteredLogs.map((log) => (
                <tr
                  key={log.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {log.timestamp}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4">
                    <span
                      className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${getLevelColor(log.level)}`}
                    >
                      {getLevelIcon(log.level)}
                      {log.level.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {log.message}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {log.user}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {log.ip}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {log.action}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <button
                      onClick={() => setSelectedLog(log)}
                      className="text-primary hover:text-primary/80"
                    >
                      <HiOutlineEye className="size-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredLogs.length === 0 && (
          <div className="p-8 text-center">
            <HiOutlineDocumentText className="mx-auto size-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No logs found
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Try adjusting your search or filter criteria.
            </p>
          </div>
        )}
      </div>

      {/* Log Details Modal */}
      {selectedLog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="w-full max-w-2xl rounded-lg bg-white p-6 shadow-xl dark:bg-gray-dark">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Log Details
              </h3>
              <button
                onClick={() => setSelectedLog(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <HiOutlineXCircle className="size-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Timestamp
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {selectedLog.timestamp}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Level
                </label>
                <span
                  className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium ${getLevelColor(selectedLog.level)}`}
                >
                  {getLevelIcon(selectedLog.level)}
                  {selectedLog.level.toUpperCase()}
                </span>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Message
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {selectedLog.message}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  User
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {selectedLog.user}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  IP Address
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {selectedLog.ip}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Action
                </label>
                <p className="text-sm text-gray-900 dark:text-white">
                  {selectedLog.action}
                </p>
              </div>

              {selectedLog.details && (
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Details
                  </label>
                  <p className="text-sm text-gray-900 dark:text-white">
                    {selectedLog.details}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
