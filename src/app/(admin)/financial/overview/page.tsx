'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { ArrowUp, ArrowDown } from 'lucide-react';

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

const FinancialOverview: React.FC = () => {
  // Data for charts
  const pieChartOptions = {
    chart: {
      type: 'pie' as const,
    },
    labels: ['Local NGOs', 'International NGOs'],
    colors: ['#4361ee', '#3f37c9'],
    legend: {
      position: 'bottom' as const,
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          width: 200
        },
        legend: {
          position: 'bottom' as const
        }
      }
    }]
  };

  const pieChartSeries = [1200000, 650000];

  const columnChartOptions = {
    chart: {
      type: 'bar' as const,
    },
    xaxis: {
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    },
    colors: ['#4cc9f0'],
    plotOptions: {
      bar: {
        borderRadius: 4,
        horizontal: false,
      }
    },
    dataLabels: {
      enabled: false
    }
  };

  const columnChartSeries = [{
    name: 'Revenue',
    data: [3200, 2800, 4000, 3800, 4200, 4523]
  }];

  // Table data
  const invoicesData = [
    { invoice: 'INV-001', amount: 1200, status: 'Paid', dueDate: '2023-05-15' },
    { invoice: 'INV-002', amount: 800, status: 'Pending', dueDate: '2023-06-20' },
    { invoice: 'INV-003', amount: 1500, status: 'Overdue', dueDate: '2023-04-30' },
    { invoice: 'INV-004', amount: 950, status: 'Paid', dueDate: '2023-05-10' },
  ];

  // Helper component for trend indicators
  const TrendIndicator: React.FC<{ value: number }> = ({ value }) => {
    const isPositive = value >= 0;
    const colorClass = isPositive ? 'text-green-500' : 'text-red-500';
    const Icon = isPositive ? ArrowUp : ArrowDown;
    
    return (
      <span className={`${colorClass} ml-2 flex items-center`}>
        {value}% <Icon className="w-3 h-3 ml-1" />
      </span>
    );
  };

  // Card component
  const StatCard: React.FC<{ title: string; value: string; trend: number; description: string }> = ({ title, value, trend, description }) => (
    <div className="bg-white dark:bg-boxdark rounded-lg p-6 shadow-sm border border-stroke dark:border-strokedark">
      <h3 className="text-sm font-medium text-black dark:text-white mb-2">{title}</h3>
      <h2 className="text-2xl font-bold text-black dark:text-white mb-2">{value}</h2>
      <div className="flex items-center">
        <TrendIndicator value={trend} />
      </div>
      <div className="text-xs text-bodydark2 mt-1">{description}</div>
    </div>
  );

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold text-black dark:text-white mb-6">Financial Overview</h2>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatCard title="Total Networks" value="24" trend={12.5} description="Total Networks Increased by 12.5%" />
        <StatCard title="Total Organizations" value="156" trend={8.2} description="Total Organizations Increased by 8.2%" />
        <StatCard title="Total Users" value="892" trend={15.3} description="Total Users Increased by 15.3%" />
        <StatCard title="Total Documents" value="342" trend={5.7} description="Total Documents Increased by 5.7%" />
      </div>
      
      {/* Second Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatCard title="Total Revenue This Year" value="MWK 45,230" trend={12.5} description="Total membership revenue collected" />
        <StatCard title="Outstanding Invoices" value="MWK 8,450" trend={-2.3} description="Invoices awaiting payment" />
        <StatCard title="Payments This Month" value="MWK 12,340" trend={8.1} description="Payments received this month" />
        <StatCard title="Overdue Payments" value="MWK 3,120" trend={15.2} description="Payments past due date" />
      </div>
      
      {/* Third Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatCard title="Invoices Generated" value="156" trend={5.7} description="Total invoices created" />
        <StatCard title="Receipts Issued" value="142" trend={3.2} description="Receipts generated for payments" />
        <StatCard title="Penalties Collected" value="MWK 1,850" trend={22.1} description="Late payment penalties" />
        <StatCard title="NGOs Awaiting Invoice" value="23" trend={-8.5} description="NGOs pending invoice generation" />
      </div>
      
      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
        <div className="bg-white dark:bg-boxdark rounded-lg p-6 shadow-sm border border-stroke dark:border-strokedark">
          <h3 className="text-lg font-semibold text-black dark:text-white mb-4">Revenue by NGO Category</h3>
          <Chart
            options={pieChartOptions}
            series={pieChartSeries}
            type="pie"
            height={300}
          />
          <div className="mt-4 text-center">
            <div className="font-semibold text-black dark:text-white">Total: MWK 1,850,000</div>
            <div className="text-sm text-bodydark2">Local: MWK 1,200,000 | International: MWK 650,000</div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-boxdark rounded-lg p-6 shadow-sm border border-stroke dark:border-strokedark">
          <h3 className="text-lg font-semibold text-black dark:text-white mb-4">Monthly Revenue Trend</h3>
          <Chart
            options={columnChartOptions}
            series={columnChartSeries}
            type="bar"
            height={300}
          />
        </div>
      </div>
      
      {/* Table */}
      <div className="bg-white dark:bg-boxdark rounded-lg p-6 shadow-sm border border-stroke dark:border-strokedark">
        <h3 className="text-lg font-semibold text-black dark:text-white mb-4">Recent Invoices</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left">
            <thead className="text-xs uppercase bg-gray-50 dark:bg-meta-4">
              <tr>
                <th className="px-6 py-3 text-black dark:text-white">Invoice</th>
                <th className="px-6 py-3 text-black dark:text-white">Amount (MWK)</th>
                <th className="px-6 py-3 text-black dark:text-white">Status</th>
                <th className="px-6 py-3 text-black dark:text-white">Due Date</th>
              </tr>
            </thead>
            <tbody>
              {invoicesData.map((invoice, index) => (
                <tr key={index} className="bg-white border-b dark:bg-boxdark dark:border-strokedark">
                  <td className="px-6 py-4 text-black dark:text-white">{invoice.invoice}</td>
                  <td className="px-6 py-4 text-black dark:text-white">{invoice.amount.toLocaleString()}</td>
                  <td className="px-6 py-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      invoice.status === 'Paid' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                      invoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                    }`}>
                      {invoice.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-black dark:text-white">{invoice.dueDate}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default FinancialOverview;