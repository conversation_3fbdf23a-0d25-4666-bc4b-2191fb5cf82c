"use client";

import React, { useState } from "react";
import {
  HiOutlineCog,
  HiOutlineCreditCard,
  HiOutlineCurrencyDollar,
  HiOutlineDocumentText,
  HiOutlineShieldCheck,
  HiOutlineArrowDownTray,
  HiOutlineArrowPath,
} from "react-icons/hi2";

export default function FinancialSettingsPage() {
  const [settings, setSettings] = useState({
    currency: "MWK",
    taxRate: 16.5,
    lateFeeRate: 5.0,
    autoInvoiceGeneration: true,
    paymentReminders: true,
    refundProcessing: true,
    invoicePrefix: "INV",
    receiptPrefix: "RCP",
    defaultPaymentTerms: 30,
    maxRefundPeriod: 90,
  });

  const [loading, setLoading] = useState(false);

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      // Show success message
    }, 1000);
  };

  const handleReset = () => {
    setSettings({
      currency: "MWK",
      taxRate: 16.5,
      lateFeeRate: 5.0,
      autoInvoiceGeneration: true,
      paymentReminders: true,
      refundProcessing: true,
      invoicePrefix: "INV",
      receiptPrefix: "RCP",
      defaultPaymentTerms: 30,
      maxRefundPeriod: 90,
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Financial Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Configure financial system preferences and defaults
          </p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={handleReset}
            className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700"
          >
            <HiOutlineArrowPath className="h-4 w-4" />
            Reset to Defaults
          </button>
          <button
            onClick={handleSave}
            disabled={loading}
            className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/80 disabled:opacity-50"
          >
            <HiOutlineArrowDownTray className="h-4 w-4" />
            {loading ? "Saving..." : "Save Settings"}
          </button>
        </div>
      </div>

      {/* Settings Sections */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Currency and Tax Settings */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineCurrencyDollar className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Currency & Tax Settings
            </h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Default Currency
              </label>
              <select
                value={settings.currency}
                onChange={(e) =>
                  handleSettingChange("currency", e.target.value)
                }
                className="mt-1 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              >
                <option value="MWK">Malawi Kwacha (MWK)</option>
                <option value="USD">US Dollar (USD)</option>
                <option value="EUR">Euro (EUR)</option>
                <option value="GBP">British Pound (GBP)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Tax Rate (%)
              </label>
              <input
                type="number"
                step="0.1"
                value={settings.taxRate}
                onChange={(e) =>
                  handleSettingChange("taxRate", parseFloat(e.target.value))
                }
                className="mt-1 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Late Fee Rate (%)
              </label>
              <input
                type="number"
                step="0.1"
                value={settings.lateFeeRate}
                onChange={(e) =>
                  handleSettingChange("lateFeeRate", parseFloat(e.target.value))
                }
                className="mt-1 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>
          </div>
        </div>

        {/* Invoice and Receipt Settings */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineDocumentText className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Invoice & Receipt Settings
            </h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Invoice Number Prefix
              </label>
              <input
                type="text"
                value={settings.invoicePrefix}
                onChange={(e) =>
                  handleSettingChange("invoicePrefix", e.target.value)
                }
                className="mt-1 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Receipt Number Prefix
              </label>
              <input
                type="text"
                value={settings.receiptPrefix}
                onChange={(e) =>
                  handleSettingChange("receiptPrefix", e.target.value)
                }
                className="mt-1 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Default Payment Terms (days)
              </label>
              <input
                type="number"
                value={settings.defaultPaymentTerms}
                onChange={(e) =>
                  handleSettingChange(
                    "defaultPaymentTerms",
                    parseInt(e.target.value),
                  )
                }
                className="mt-1 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>
          </div>
        </div>

        {/* Automation Settings */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineCog className="h-5 w-5 text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Automation Settings
            </h3>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Auto Invoice Generation
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Automatically generate invoices for recurring payments
                </p>
              </div>
              <label className="relative inline-flex cursor-pointer items-center">
                <input
                  type="checkbox"
                  checked={settings.autoInvoiceGeneration}
                  onChange={(e) =>
                    handleSettingChange(
                      "autoInvoiceGeneration",
                      e.target.checked,
                    )
                  }
                  className="sr-only"
                />
                <div
                  className={`h-6 w-11 rounded-full transition ${
                    settings.autoInvoiceGeneration
                      ? "bg-primary"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}
                >
                  <div
                    className={`h-4 w-4 transform rounded-full bg-white transition ${
                      settings.autoInvoiceGeneration
                        ? "translate-x-6"
                        : "translate-x-1"
                    }`}
                  />
                </div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Payment Reminders
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Send automatic reminders for overdue payments
                </p>
              </div>
              <label className="relative inline-flex cursor-pointer items-center">
                <input
                  type="checkbox"
                  checked={settings.paymentReminders}
                  onChange={(e) =>
                    handleSettingChange("paymentReminders", e.target.checked)
                  }
                  className="sr-only"
                />
                <div
                  className={`h-6 w-11 rounded-full transition ${
                    settings.paymentReminders
                      ? "bg-primary"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}
                >
                  <div
                    className={`h-4 w-4 transform rounded-full bg-white transition ${
                      settings.paymentReminders
                        ? "translate-x-6"
                        : "translate-x-1"
                    }`}
                  />
                </div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Refund Processing
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Enable automatic refund processing
                </p>
              </div>
              <label className="relative inline-flex cursor-pointer items-center">
                <input
                  type="checkbox"
                  checked={settings.refundProcessing}
                  onChange={(e) =>
                    handleSettingChange("refundProcessing", e.target.checked)
                  }
                  className="sr-only"
                />
                <div
                  className={`h-6 w-11 rounded-full transition ${
                    settings.refundProcessing
                      ? "bg-primary"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}
                >
                  <div
                    className={`h-4 w-4 transform rounded-full bg-white transition ${
                      settings.refundProcessing
                        ? "translate-x-6"
                        : "translate-x-1"
                    }`}
                  />
                </div>
              </label>
            </div>
          </div>
        </div>

        {/* Security and Compliance */}
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="mb-4 flex items-center gap-2">
            <HiOutlineShieldCheck className="h-5 w-5 text-red-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Security & Compliance
            </h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Maximum Refund Period (days)
              </label>
              <input
                type="number"
                value={settings.maxRefundPeriod}
                onChange={(e) =>
                  handleSettingChange(
                    "maxRefundPeriod",
                    parseInt(e.target.value),
                  )
                }
                className="mt-1 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>

            <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-300">
                Security Notice
              </h4>
              <p className="mt-1 text-xs text-blue-700 dark:text-blue-400">
                All financial transactions are encrypted and logged for audit
                purposes. Changes to these settings may affect system security.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="rounded-lg bg-gray-50 p-6 dark:bg-gray-800">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Current Settings Summary
        </h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Currency
            </p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {settings.currency}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Tax Rate
            </p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {settings.taxRate}%
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Late Fee Rate
            </p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {settings.lateFeeRate}%
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Payment Terms
            </p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {settings.defaultPaymentTerms} days
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
