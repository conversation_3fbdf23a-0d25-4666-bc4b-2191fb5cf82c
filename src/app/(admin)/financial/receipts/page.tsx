"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Download,
  Eye,
  FileText,
  CheckCircle,
  Clock,
} from "lucide-react";
import { getReceipts, IReceipt } from "@/services/receipts.services";

export default function ReceiptsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [receiptTypeFilter, setReceiptTypeFilter] = useState("all");
  const [receipts, setReceipts] = useState<IReceipt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // For tracking total amounts and counts
  const [stats, setStats] = useState({
    totalReceipts: 0,
    issuedReceipts: 0,
    pendingReceipts: 0,
    totalAmount: 0,
  });

  useEffect(() => {
    async function fetchReceipts() {
      try {
        setLoading(true);
        // Get token from localStorage (browser-only code)
        const token =
          typeof window !== "undefined"
            ? localStorage.getItem("accessToken") || ""
            : "";

        if (!token) {
          setError("Authentication token not found. Please log in.");
          setLoading(false);
          return;
        }

        const response = await getReceipts({}, token);

        if (response.status === "success" && response.data) {
          setReceipts(response.data);

          // Calculate statistics
          const issued = response.data.filter((r) => r.receiptUrl).length; // Assuming receipts with URLs are issued
          const pending = response.data.length - issued;
          const totalAmount = response.data.reduce(
            (sum, receipt) => sum + receipt.amount,
            0,
          );

          setStats({
            totalReceipts: response.data.length,
            issuedReceipts: issued,
            pendingReceipts: pending,
            totalAmount: totalAmount,
          });
        } else {
          setError("Failed to fetch receipts");
        }
      } catch (err) {
        setError("An error occurred while fetching receipts");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchReceipts();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleReceiptTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setReceiptTypeFilter(e.target.value);
  };

  // Filter receipts based on search term and status
  const filteredReceipts = receipts.filter((receipt) => {
    // Status filter
    if (
      receiptTypeFilter !== "all" &&
      (receiptTypeFilter === "processing fee" ||
        receiptTypeFilter === "registration fee" ||
        receiptTypeFilter === "subscription fee")
    ) {
      return false;
    }

    // Search term filter (search in receipt number or ngo name)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        receipt.receiptNumber.toLowerCase().includes(searchLower) ||
        receipt.ngo.name.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Receipt Management
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Generate and manage payment receipts
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Receipts
              </p>
              <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
                {stats.totalReceipts}
              </p>
            </div>
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
              <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Issued Receipts
              </p>
              <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
                {stats.issuedReceipts}
              </p>
            </div>
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Amount
              </p>
              <p className="mt-2 text-3xl font-bold text-gray-900 dark:text-white">
                MWK {stats.totalAmount.toLocaleString()}
              </p>
            </div>
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
              <FileText className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and filter bar */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="relative w-full md:w-64">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full rounded-lg border border-gray-300 bg-white p-2 pl-10 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
            placeholder="Search receipts..."
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="w-full md:w-48">
          <select
            className="block w-full rounded-lg border border-gray-300 bg-white p-2 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
            value={receiptTypeFilter}
            onChange={handleReceiptTypeChange}
          >
            <option value="all">All Receipts Types</option>
            <option value="subscription fee">Subscription Fees</option>
            <option value="processing fee">Processing Fees</option>
            <option value="registration fee">Registration Fees</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className="flex h-40 items-center justify-center">
          <div className="text-center">
            <div
              className="spinner-border inline-block h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"
              role="status"
            ></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Loading receipts...
            </p>
          </div>
        </div>
      ) : error ? (
        <div
          className="mb-4 rounded-lg bg-red-100 p-4 text-sm text-red-700 dark:bg-red-200 dark:text-red-800"
          role="alert"
        >
          {error}
        </div>
      ) : (
        <div className="rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Receipt #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    NGO Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Issue Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                {filteredReceipts.length > 0 ? (
                  filteredReceipts.map((receipt) => (
                    <tr
                      key={receipt._id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                        {receipt.receiptNumber}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                        {receipt.ngo.name}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                        MWK {receipt.amount.toLocaleString()}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                        {receipt.type}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                        {new Date(receipt.issueDate).toLocaleDateString()}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                            title="View Receipt Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          {receipt.receiptUrl && (
                            <a
                              href={receipt.receiptUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              title="Download Receipt"
                            >
                              <Download className="h-4 w-4" />
                            </a>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={6}
                      className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                    >
                      No receipts found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
