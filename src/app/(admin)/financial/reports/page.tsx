"use client";

import React, { useState } from "react";
import {
  Download,
  BarChart3,
  TrendingUp,
  DollarSign,
  Calendar,
} from "lucide-react";

export default function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState("revenue");
  const [selectedPeriod, setSelectedPeriod] = useState("month");

  const reports = [
    {
      id: "revenue",
      title: "Revenue Report",
      description: "Monthly revenue breakdown by NGO category",
      icon: DollarSign,
      data: {
        totalRevenue: 45230,
        localNGOs: 28000,
        internationalNGOs: 12000,
        csos: 5230,
      },
    },
    {
      id: "payments",
      title: "Payment Analysis",
      description: "Payment methods and success rates",
      icon: TrendingUp,
      data: {
        totalPayments: 156,
        bankTransfer: 89,
        mobileMoney: 45,
        creditCard: 22,
      },
    },
    {
      id: "invoices",
      title: "Invoice Status",
      description: "Invoice generation and collection rates",
      icon: BarChart3,
      data: {
        totalInvoices: 180,
        paid: 142,
        pending: 23,
        overdue: 15,
      },
    },
  ];

  const currentReport = reports.find((r) => r.id === selectedReport);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Financial Reports
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Generate and view comprehensive financial reports
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
          <button className="flex items-center space-x-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
            <Download className="h-4 w-4" />
            <span>Export Report</span>
          </button>
        </div>
      </div>

      {/* Report Type Selector */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        {reports.map((report) => (
          <button
            key={report.id}
            onClick={() => setSelectedReport(report.id)}
            className={`rounded-lg border p-6 text-left transition-colors ${
              selectedReport === report.id
                ? "border-blue-500 bg-blue-50 dark:border-blue-400 dark:bg-blue-900/20"
                : "border-gray-200 bg-white hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
            }`}
          >
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                <report.icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  {report.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {report.description}
                </p>
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Report Content */}
      {currentReport && (
        <div className="space-y-6">
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-6 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {currentReport.title}
              </h2>
              <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <Calendar className="h-4 w-4" />
                <span>Period: {selectedPeriod}</span>
              </div>
            </div>

            {selectedReport === "revenue" && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
                  <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          Total Revenue
                        </p>
                        <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                          MWK{" "}
                          {currentReport.data.totalRevenue?.toLocaleString() ||
                            "0"}
                        </p>
                      </div>
                      <DollarSign className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-green-50 p-4 dark:bg-green-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-600 dark:text-green-400">
                          Local NGOs
                        </p>
                        <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                          MWK{" "}
                          {currentReport.data.localNGOs?.toLocaleString() ||
                            "0"}
                        </p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-yellow-50 p-4 dark:bg-yellow-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                          International NGOs
                        </p>
                        <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                          MWK{" "}
                          {currentReport.data.internationalNGOs?.toLocaleString() ||
                            "0"}
                        </p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-purple-50 p-4 dark:bg-purple-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                          CSOs
                        </p>
                        <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                          MWK {currentReport.data.csos?.toLocaleString() || "0"}
                        </p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                    </div>
                  </div>
                </div>

                <div className="rounded-lg border border-gray-200 p-4 dark:border-gray-700">
                  <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                    Revenue Breakdown
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Local NGOs
                      </span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {currentReport.data.totalRevenue &&
                        currentReport.data.localNGOs
                          ? (
                              (currentReport.data.localNGOs /
                                currentReport.data.totalRevenue) *
                              100
                            ).toFixed(1)
                          : "0.0"}
                        %
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        International NGOs
                      </span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {currentReport.data.totalRevenue &&
                        currentReport.data.internationalNGOs
                          ? (
                              (currentReport.data.internationalNGOs /
                                currentReport.data.totalRevenue) *
                              100
                            ).toFixed(1)
                          : "0.0"}
                        %
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        CSOs
                      </span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {currentReport.data.totalRevenue &&
                        currentReport.data.csos
                          ? (
                              (currentReport.data.csos /
                                currentReport.data.totalRevenue) *
                              100
                            ).toFixed(1)
                          : "0.0"}
                        %
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {selectedReport === "payments" && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
                  <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          Total Payments
                        </p>
                        <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                          {currentReport.data.totalPayments}
                        </p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-green-50 p-4 dark:bg-green-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-600 dark:text-green-400">
                          Bank Transfer
                        </p>
                        <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                          {currentReport.data.bankTransfer}
                        </p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-yellow-50 p-4 dark:bg-yellow-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                          Mobile Money
                        </p>
                        <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                          {currentReport.data.mobileMoney}
                        </p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-purple-50 p-4 dark:bg-purple-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-purple-600 dark:text-purple-400">
                          Credit Card
                        </p>
                        <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                          {currentReport.data.creditCard}
                        </p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {selectedReport === "invoices" && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
                  <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                          Total Invoices
                        </p>
                        <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                          {currentReport.data.totalInvoices}
                        </p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-green-50 p-4 dark:bg-green-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-green-600 dark:text-green-400">
                          Paid
                        </p>
                        <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                          {currentReport.data.paid}
                        </p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-yellow-50 p-4 dark:bg-yellow-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                          Pending
                        </p>
                        <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">
                          {currentReport.data.pending}
                        </p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                    </div>
                  </div>

                  <div className="rounded-lg bg-red-50 p-4 dark:bg-red-900/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-red-600 dark:text-red-400">
                          Overdue
                        </p>
                        <p className="text-2xl font-bold text-red-900 dark:text-red-100">
                          {currentReport.data.overdue}
                        </p>
                      </div>
                      <BarChart3 className="h-8 w-8 text-red-600 dark:text-red-400" />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
