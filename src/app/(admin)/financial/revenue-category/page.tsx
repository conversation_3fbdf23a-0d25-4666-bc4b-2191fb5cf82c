"use client";

import React, { useState } from "react";
import {
  HiOutlineBuildingOffice,
  HiOutlineGlobeAlt,
  HiOutlineCurrencyDollar,
  HiOutlineChartBar,
  HiOutlineCalendar,
  HiOutlineArrowUp,
  HiOutlineArrowDown,
} from "react-icons/hi2";

const RevenueCategoryPage = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("this_year");

  const revenueData = [
    {
      category: "Local NGOs",
      amount: 1200000,
      percentage: 64.9,
      change: 12.5,
      changeType: "positive" as const,
      ngoCount: 45,
    },
    {
      category: "International NGOs",
      amount: 650000,
      percentage: 35.1,
      change: 8.7,
      changeType: "positive" as const,
      ngoCount: 12,
    },
  ];

  const totalRevenue = revenueData.reduce((sum, item) => sum + item.amount, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Revenue by NGO Category
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track revenue distribution between Local and International NGOs
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            <option value="this_month">This Month</option>
            <option value="this_quarter">This Quarter</option>
            <option value="this_year">This Year</option>
            <option value="last_year">Last Year</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Revenue
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                MWK {totalRevenue.toLocaleString()}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <HiOutlineCurrencyDollar className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Local NGOs
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                MWK {revenueData[0].amount.toLocaleString()}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <HiOutlineBuildingOffice className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                International NGOs
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                MWK {revenueData[1].amount.toLocaleString()}
              </p>
            </div>
            <div className="rounded-full bg-purple-100 p-3 dark:bg-purple-900">
              <HiOutlineGlobeAlt className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total NGOs
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {revenueData.reduce((sum, item) => sum + item.ngoCount, 0)}
              </p>
            </div>
            <div className="rounded-full bg-indigo-100 p-3 dark:bg-indigo-900">
              <HiOutlineChartBar className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Revenue Distribution
        </h3>
        <div className="space-y-4">
          {revenueData.map((item, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div
                    className={`h-3 w-3 rounded-full ${
                      index === 0 ? "bg-blue-500" : "bg-purple-500"
                    }`}
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {item.category}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    MWK {item.amount.toLocaleString()}
                  </span>
                  <div className="flex items-center">
                    {item.changeType === "positive" ? (
                      <HiOutlineArrowUp className="h-4 w-4 text-green-500" />
                    ) : (
                      <HiOutlineArrowDown className="h-4 w-4 text-red-500" />
                    )}
                    <span
                      className={`ml-1 text-sm ${
                        item.changeType === "positive"
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {item.change}%
                    </span>
                  </div>
                </div>
              </div>
              <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className={`h-2 rounded-full ${
                    index === 0 ? "bg-blue-500" : "bg-purple-500"
                  }`}
                  style={{ width: `${item.percentage}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>{item.percentage}% of total revenue</span>
                <span>{item.ngoCount} NGOs</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RevenueCategoryPage;
