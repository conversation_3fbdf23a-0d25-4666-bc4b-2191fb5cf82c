"use client";

import React, { useState } from "react";
import {
  HiOutlineDocumentText,
  HiOutlineClock,
  HiOutlineExclamationTriangle,
  HiOutlineCurrencyDollar,
  HiOutlineEye,
  HiOutlineCheckCircle,
  HiOutlineCalendar,
  HiOutlineBuildingOffice,
} from "react-icons/hi2";

const PendingUploadsPage = () => {
  const [selectedStatus, setSelectedStatus] = useState("all");

  const pendingUploads = [
    {
      id: "PU001",
      ngoName: "Malawi Health Initiative",
      ngoType: "local",
      invoiceNumber: "INV-2024-001",
      amount: 85000,
      dueDate: "2024-01-15",
      daysOverdue: 5,
      status: "overdue",
      contactPerson: "<PERSON>",
      contactEmail: "<EMAIL>",
    },
    {
      id: "PU002",
      ngoName: "Community Development Trust",
      ngoType: "local",
      invoiceNumber: "INV-2024-002",
      amount: 65000,
      dueDate: "2024-01-20",
      daysOverdue: 0,
      status: "pending",
      contactPerson: "<PERSON>",
      contactEmail: "<EMAIL>",
    },
    {
      id: "PU003",
      ngoName: "International Development Corp",
      ngoType: "international",
      invoiceNumber: "INV-2024-003",
      amount: 120000,
      dueDate: "2024-01-10",
      daysOverdue: 10,
      status: "escalated",
      contactPerson: "Sarah <PERSON>",
      contactEmail: "<EMAIL>",
    },
  ];

  const filteredUploads = pendingUploads.filter(
    (upload) => selectedStatus === "all" || upload.status === selectedStatus,
  );

  const totalAmount = filteredUploads.reduce(
    (sum, upload) => sum + upload.amount,
    0,
  );
  const overdueCount = filteredUploads.filter(
    (upload) => upload.daysOverdue > 0,
  ).length;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <span className="inline-flex items-center rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
            Pending
          </span>
        );
      case "overdue":
        return (
          <span className="inline-flex items-center rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-200">
            Overdue
          </span>
        );
      case "escalated":
        return (
          <span className="inline-flex items-center rounded-full bg-purple-100 px-2 py-1 text-xs font-medium text-purple-800 dark:bg-purple-900 dark:text-purple-200">
            Escalated
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Pending Proof of Payment Uploads
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Track and manage pending payment proof uploads from NGOs
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <button className="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
            Send Bulk Reminders
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Pending
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {filteredUploads.length}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
              <HiOutlineDocumentText className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Amount
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                MWK {totalAmount.toLocaleString()}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <HiOutlineCurrencyDollar className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Overdue
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {overdueCount}
              </p>
            </div>
            <div className="rounded-full bg-red-100 p-3 dark:bg-red-900">
              <HiOutlineExclamationTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </div>
          </div>
        </div>

        <div className="rounded-xl bg-white p-6 shadow-lg dark:bg-gray-dark">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Pending
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {filteredUploads.filter((u) => u.status === "pending").length}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <HiOutlineClock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Status
          </label>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="mt-1 rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="overdue">Overdue</option>
            <option value="escalated">Escalated</option>
          </select>
        </div>
      </div>

      <div className="rounded-xl bg-white shadow-lg dark:bg-gray-dark">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                  NGO Details
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                  Invoice
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                  Due Date
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredUploads.map((upload) => (
                <tr
                  key={upload.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <HiOutlineBuildingOffice className="h-8 w-8 text-gray-400" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {upload.ngoName}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {upload.contactPerson}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {upload.contactEmail}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {upload.invoiceNumber}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      MWK {upload.amount.toLocaleString()}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <HiOutlineCalendar className="h-4 w-4 text-gray-400" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {new Date(upload.dueDate).toLocaleDateString()}
                        </div>
                        {upload.daysOverdue > 0 && (
                          <div className="text-xs text-red-600 dark:text-red-400">
                            {upload.daysOverdue} days overdue
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">{getStatusBadge(upload.status)}</td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <button className="rounded-md bg-blue-100 p-2 text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-400 dark:hover:bg-blue-800">
                        <HiOutlineEye className="h-4 w-4" />
                      </button>
                      <button className="rounded-md bg-green-100 p-2 text-green-600 hover:bg-green-200 dark:bg-green-900 dark:text-green-400 dark:hover:bg-green-800">
                        <HiOutlineCheckCircle className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PendingUploadsPage;
