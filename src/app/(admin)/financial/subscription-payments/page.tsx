"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AlertCircle, CheckCircle, Clock, XCircle, Eye, ThumbsUp, ThumbsDown } from "lucide-react";
import {
  getAllSubscriptions,
  approveSubscription,
  rejectSubscription,
  Subscription,
} from "@/services/subscription.services";

export default function SubscriptionPaymentsPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingId, setProcessingId] = useState<string | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);
  const [rejectReason, setRejectReason] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  useEffect(() => {
    const fetchSubscriptions = async () => {
      try {
        const token = localStorage.getItem("accessToken");
        if (token) {
          const response = await getAllSubscriptions(token);
          if (response.status === "success") {
            setSubscriptions(response.data?.subscriptions || []);
          } else {
            setError(response.message);
          }
        } else {
          setError("Authentication token not found.");
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptions();
  }, []);

  const onViewCertificate = (certificateId: string) => {
    // Handle certificate view logic, e.g., navigate to a certificate page
    console.log("Viewing certificate:", certificateId);
  };

  const handleApprove = async (subscriptionId: string) => {
    const token = localStorage.getItem("accessToken");
    if (!token) {
      setError("Authentication token not found.");
      return;
    }

    setProcessingId(subscriptionId);
    try {
      const response = await approveSubscription(subscriptionId, token);
      if (response.status === "success") {
        // Refresh the subscriptions list
        const updatedResponse = await getAllSubscriptions(token);
        if (updatedResponse.status === "success") {
          setSubscriptions(updatedResponse.data?.subscriptions || []);
        }
        setError(null);
      } else {
        setError(response.message || "Failed to approve subscription");
      }
    } catch (err: any) {
      setError(err.message || "Error approving subscription");
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectClick = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setShowRejectModal(true);
    setRejectReason("");
  };

  const handleRejectSubmit = async () => {
    if (!selectedSubscription || !rejectReason.trim()) {
      setError("Please provide a reason for rejection");
      return;
    }

    const token = localStorage.getItem("accessToken");
    if (!token) {
      setError("Authentication token not found.");
      return;
    }

    setProcessingId(selectedSubscription.id);
    try {
      const response = await rejectSubscription(selectedSubscription.id, rejectReason.trim(), token);
      if (response.status === "success") {
        // Refresh the subscriptions list
        const updatedResponse = await getAllSubscriptions(token);
        if (updatedResponse.status === "success") {
          setSubscriptions(updatedResponse.data?.subscriptions || []);
        }
        setShowRejectModal(false);
        setSelectedSubscription(null);
        setRejectReason("");
        setError(null);
      } else {
        setError(response.message || "Failed to reject subscription");
      }
    } catch (err: any) {
      setError(err.message || "Error rejecting subscription");
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectCancel = () => {
    setShowRejectModal(false);
    setSelectedSubscription(null);
    setRejectReason("");
  };

  const statusBadge = (status: string) => {
    switch (status) {
      case "approved":
        return (
          <Badge className="flex items-center gap-1 bg-green-600 text-white">
            <CheckCircle className="h-4 w-4" /> Approved
          </Badge>
        );
      case "rejected":
        return (
          <Badge className="flex items-center gap-1 bg-red-600 text-white">
            <XCircle className="h-4 w-4" /> Rejected
          </Badge>
        );
      default:
        return (
          <Badge className="flex items-center gap-1 bg-yellow-500 text-white">
            <Clock className="h-4 w-4" /> Pending
          </Badge>
        );
    }
  };

  // Filter subscriptions based on status
  const filteredSubscriptions = subscriptions.filter(sub => {
    if (statusFilter === "all") return true;
    return sub.approvedStatus === statusFilter;
  });

  const pendingCount = subscriptions.filter(sub => sub.approvedStatus === "pending").length;
  const approvedCount = subscriptions.filter(sub => sub.approvedStatus === "approved").length;
  const rejectedCount = subscriptions.filter(sub => sub.approvedStatus === "rejected").length;

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Subscription Payments Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Review and approve NGO annual subscription payments
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{subscriptions.length}</p>
            </div>
            <div className="h-8 w-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <Eye className="h-4 w-4 text-blue-600" />
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
            </div>
            <div className="h-8 w-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
              <Clock className="h-4 w-4 text-yellow-600" />
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Approved</p>
              <p className="text-2xl font-bold text-green-600">{approvedCount}</p>
            </div>
            <div className="h-8 w-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Rejected</p>
              <p className="text-2xl font-bold text-red-600">{rejectedCount}</p>
            </div>
            <div className="h-8 w-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <XCircle className="h-4 w-4 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg w-fit">
        {[
          { key: "all", label: "All", count: subscriptions.length },
          { key: "pending", label: "Pending", count: pendingCount },
          { key: "approved", label: "Approved", count: approvedCount },
          { key: "rejected", label: "Rejected", count: rejectedCount },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setStatusFilter(tab.key)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              statusFilter === tab.key
                ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm"
                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            }`}
          >
            {tab.label} ({tab.count})
          </button>
        ))}
      </div>

      {/* Subscriptions Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredSubscriptions.length === 0 && (
          <div className="col-span-full text-center py-8">
            <div className="text-gray-400 text-6xl mb-4">📄</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No subscriptions found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {statusFilter === "all" 
                ? "No subscription payments have been submitted yet."
                : `No ${statusFilter} subscriptions found.`}
            </p>
          </div>
        )}
        {filteredSubscriptions.map((sub) => (
        <Card key={sub.id} className="rounded-2xl shadow-md">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{sub.ngo.name}</span>
              {statusBadge(sub.approvedStatus)}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm">
            <p>
              <strong>Submitted:</strong>{" "}
              {new Date(sub.submittedAt).toLocaleDateString()}
            </p>
            <p>
              <strong>Expiry:</strong>{" "}
              {new Date(sub.expiryDate).toLocaleDateString()}
            </p>
            <p>
              <strong>Proof of Payment:</strong>{" "}
              <a
                href={sub.proofOfPaymentUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline"
              >
                View
              </a>
            </p>

            {sub.approvedStatus === "rejected" && sub.rejectedReason && (
              <div className="flex items-start gap-2 text-red-600">
                <AlertCircle className="mt-1 h-4 w-4" />
                <span>
                  <strong>Reason:</strong> {sub.rejectedReason}
                </span>
              </div>
            )}

            {sub.certificate && (
              <div className="rounded-lg border bg-gray-50 p-2 dark:bg-gray-800">
                <p>
                  <strong>Certificate #:</strong>{" "}
                  {sub.certificate.certificateNumber}
                </p>
                <p>
                  <strong>Expires:</strong>{" "}
                  {new Date(sub.certificate.expiryDate).toLocaleDateString()}
                </p>
                <div className="mt-2 flex items-center justify-between">
                  <img
                    src={sub.certificate.qrCodeUrl}
                    alt="QR Code"
                    className="h-16 w-16"
                  />
                  <Button
                    variant="outline"
                    onClick={() => onViewCertificate(sub.certificate!.id)}
                  >
                    View Certificate
                  </Button>
                </div>
              </div>
            )}

            {/* Action Buttons for Financial Officers */}
            {sub.approvedStatus === "pending" && (
              <div className="flex gap-2 mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                <Button
                  onClick={() => handleApprove(sub.id)}
                  disabled={processingId === sub.id}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                  size="sm"
                >
                  {processingId === sub.id ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Processing...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <ThumbsUp className="h-4 w-4" />
                      <span>Approve</span>
                    </div>
                  )}
                </Button>
                <Button
                  onClick={() => handleRejectClick(sub)}
                  disabled={processingId === sub.id}
                  variant="outline"
                  className="flex-1 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
                  size="sm"
                >
                  <div className="flex items-center gap-2">
                    <ThumbsDown className="h-4 w-4" />
                    <span>Reject</span>
                  </div>
                </Button>
              </div>
            )}

            {/* View Proof of Payment Button */}
            <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-600">
              <Button
                onClick={() => window.open(sub.proofOfPaymentUrl, '_blank')}
                variant="outline"
                className="w-full"
                size="sm"
              >
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  <span>View Payment Proof</span>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Reject Modal */}
      {showRejectModal && selectedSubscription && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Reject Subscription
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              You are about to reject the subscription from{" "}
              <strong>{selectedSubscription.ngo.name}</strong>. Please provide a reason:
            </p>
            <textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="Enter reason for rejection..."
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none h-24 text-sm"
              maxLength={500}
            />
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {rejectReason.length}/500 characters
            </div>
            <div className="flex gap-3 mt-6">
              <Button
                onClick={handleRejectCancel}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleRejectSubmit}
                disabled={!rejectReason.trim() || processingId === selectedSubscription.id}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              >
                {processingId === selectedSubscription.id ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Rejecting...</span>
                  </div>
                ) : (
                  "Confirm Reject"
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}
