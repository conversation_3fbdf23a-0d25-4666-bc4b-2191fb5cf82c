"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  TrendingUp,
  Users,
  Target,
  Eye,
  Edit,
  Download,
  Plus,
  BarChart3,
  Calendar,
  MapPin,
} from "lucide-react";

interface ImpactAssessment {
  id: string;
  title: string;
  programme: string;
  organization: string;
  status: "completed" | "in_progress" | "planned";
  assessmentDate: string;
  beneficiaries: number;
  impactScore: number;
  location: string;
  description: string;
  metrics: {
    name: string;
    value: number;
    target: number;
    unit: string;
  }[];
}

const mockImpactAssessments: ImpactAssessment[] = [
  {
    id: "1",
    title: "Health Programme Impact Assessment 2024",
    programme: "Health and Nutrition",
    organization: "Community Development Initiative",
    status: "completed",
    assessmentDate: "2024-01-15",
    beneficiaries: 2500,
    impactScore: 85,
    location: "Central Region",
    description:
      "Comprehensive assessment of health programme outcomes and community impact",
    metrics: [
      { name: "Vaccination Coverage", value: 92, target: 90, unit: "%" },
      { name: "Nutrition Improvement", value: 78, target: 80, unit: "%" },
      { name: "Healthcare Access", value: 85, target: 85, unit: "%" },
    ],
  },
  {
    id: "2",
    title: "Education Skills Development Impact",
    programme: "Education and Skills Development",
    organization: "Global Aid Foundation",
    status: "in_progress",
    assessmentDate: "2024-02-20",
    beneficiaries: 1200,
    impactScore: 72,
    location: "Southern Region",
    description:
      "Assessment of educational outcomes and skills development impact",
    metrics: [
      { name: "Literacy Rate", value: 88, target: 85, unit: "%" },
      { name: "Employment Rate", value: 65, target: 70, unit: "%" },
      { name: "Skills Retention", value: 78, target: 75, unit: "%" },
    ],
  },
  {
    id: "3",
    title: "Environmental Conservation Impact",
    programme: "Environmental Conservation",
    organization: "Local Youth Empowerment",
    status: "planned",
    assessmentDate: "2024-03-10",
    beneficiaries: 800,
    impactScore: 0,
    location: "Northern Region",
    description:
      "Planned assessment of environmental conservation programme impact",
    metrics: [
      { name: "Tree Planting", value: 0, target: 1000, unit: "trees" },
      { name: "Waste Reduction", value: 0, target: 50, unit: "%" },
      { name: "Community Awareness", value: 0, target: 90, unit: "%" },
    ],
  },
];

export default function ImpactAssessmentPage() {
  const [assessments, setAssessments] = useState<ImpactAssessment[]>(
    mockImpactAssessments,
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "completed" | "in_progress" | "planned"
  >("all");

  const filteredAssessments = assessments.filter((assessment) => {
    const matchesSearch =
      assessment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assessment.programme.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assessment.organization.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || assessment.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "planned":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getImpactScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Impact Assessment
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Monitor and evaluate programme impact across different areas
        </p>
      </div>

      {/* Search and Filter Section */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"
                  size={20}
                />
                <Input
                  placeholder="Search impact assessments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="in_progress">In Progress</option>
                <option value="planned">Planned</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Assessments
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {assessments.length}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Completed
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {
                    assessments.filter(
                      (assessment) => assessment.status === "completed",
                    ).length
                  }
                </p>
              </div>
              <Target className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Beneficiaries
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {assessments
                    .reduce(
                      (total, assessment) => total + assessment.beneficiaries,
                      0,
                    )
                    .toLocaleString()}
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Avg Impact Score
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {Math.round(
                    assessments.reduce(
                      (total, assessment) => total + assessment.impactScore,
                      0,
                    ) / assessments.length,
                  )}
                  %
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Impact Assessments Grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {filteredAssessments.map((assessment) => (
          <Card
            key={assessment.id}
            className="transition-shadow hover:shadow-lg"
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{assessment.title}</CardTitle>
                <Badge className={getStatusColor(assessment.status)}>
                  {assessment.status.replace("_", " ").charAt(0).toUpperCase() +
                    assessment.status.replace("_", " ").slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-gray-600 dark:text-gray-400">
                {assessment.description}
              </p>

              <div className="mb-4 space-y-3">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {assessment.location}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(assessment.assessmentDate).toLocaleDateString()}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {assessment.beneficiaries.toLocaleString()} beneficiaries
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-gray-500" />
                  <span
                    className={`text-sm font-medium ${getImpactScoreColor(assessment.impactScore)}`}
                  >
                    Impact Score: {assessment.impactScore}%
                  </span>
                </div>
              </div>

              <div className="mb-4">
                <p className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Key Metrics:
                </p>
                <div className="space-y-2">
                  {assessment.metrics.map((metric, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between"
                    >
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {metric.name}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {metric.value}
                          {metric.unit}
                        </span>
                        <span className="text-xs text-gray-500">
                          / {metric.target}
                          {metric.unit}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="mr-1 h-4 w-4" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Edit className="mr-1 h-4 w-4" />
                  Edit
                </Button>
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAssessments.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              No impact assessments found matching your criteria.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
