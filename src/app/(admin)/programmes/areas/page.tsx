"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Target,
  Users,
  TrendingUp,
  Eye,
  Edit,
  Trash2,
  Plus,
  MapPin,
  Calendar,
} from "lucide-react";

interface ProgrammeArea {
  id: string;
  name: string;
  description: string;
  status: "active" | "inactive" | "planning";
  organizations: number;
  projects: number;
  budget: string;
  location: string;
  startDate: string;
  endDate: string;
  focus: string[];
}

const mockProgrammeAreas: ProgrammeArea[] = [
  {
    id: "1",
    name: "Health and Nutrition",
    description:
      "Programmes focused on improving health outcomes and nutrition across communities",
    status: "active",
    organizations: 15,
    projects: 8,
    budget: "$250,000",
    location: "Central Region",
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    focus: ["Healthcare", "Nutrition", "Sanitation"],
  },
  {
    id: "2",
    name: "Education and Skills Development",
    description:
      "Educational programmes and skills training for youth and adults",
    status: "active",
    organizations: 12,
    projects: 6,
    budget: "$180,000",
    location: "Southern Region",
    startDate: "2024-02-01",
    endDate: "2024-11-30",
    focus: ["Education", "Skills Training", "Youth Development"],
  },
  {
    id: "3",
    name: "Environmental Conservation",
    description: "Environmental protection and conservation initiatives",
    status: "planning",
    organizations: 8,
    projects: 3,
    budget: "$120,000",
    location: "Northern Region",
    startDate: "2024-03-01",
    endDate: "2024-10-31",
    focus: ["Conservation", "Climate Change", "Sustainability"],
  },
];

export default function ProgrammeAreasPage() {
  const [programmeAreas, setProgrammeAreas] =
    useState<ProgrammeArea[]>(mockProgrammeAreas);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "active" | "inactive" | "planning"
  >("all");

  const filteredAreas = programmeAreas.filter((area) => {
    const matchesSearch =
      area.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      area.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || area.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "planning":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Programme Areas
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage and monitor programme focus areas and their activities
        </p>
      </div>

      {/* Search and Filter Section */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"
                  size={20}
                />
                <Input
                  placeholder="Search programme areas..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="planning">Planning</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Areas
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {programmeAreas.length}
                </p>
              </div>
              <Target className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Active Programmes
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {
                    programmeAreas.filter((area) => area.status === "active")
                      .length
                  }
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Organizations
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {programmeAreas.reduce(
                    (total, area) => total + area.organizations,
                    0,
                  )}
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Projects
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {programmeAreas.reduce(
                    (total, area) => total + area.projects,
                    0,
                  )}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Programme Areas Grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {filteredAreas.map((area) => (
          <Card key={area.id} className="transition-shadow hover:shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{area.name}</CardTitle>
                <Badge className={getStatusColor(area.status)}>
                  {area.status.charAt(0).toUpperCase() + area.status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-gray-600 dark:text-gray-400">
                {area.description}
              </p>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {area.location}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(area.startDate).toLocaleDateString()} -{" "}
                    {new Date(area.endDate).toLocaleDateString()}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {area.organizations} organizations, {area.projects} projects
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Budget: {area.budget}
                  </span>
                </div>
              </div>

              <div className="mt-4">
                <p className="mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                  Focus Areas:
                </p>
                <div className="flex flex-wrap gap-1">
                  {area.focus.map((focus, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {focus}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="mt-4 flex gap-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="mr-1 h-4 w-4" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Edit className="mr-1 h-4 w-4" />
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredAreas.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              No programme areas found matching your criteria.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
