"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Calendar,
  Users,
  MapPin,
  Eye,
  Edit,
  Plus,
  Clock,
  Target,
  TrendingUp,
  MessageSquare,
} from "lucide-react";

interface NetworkActivity {
  id: string;
  title: string;
  description: string;
  type: "meeting" | "workshop" | "training" | "conference" | "outreach";
  status: "upcoming" | "ongoing" | "completed" | "cancelled";
  startDate: string;
  endDate: string;
  location: string;
  participants: number;
  maxParticipants: number;
  organizer: string;
  budget: string;
}

const mockNetworkActivities: NetworkActivity[] = [
  {
    id: "1",
    title: "Quarterly Network Meeting",
    description:
      "Regular quarterly meeting to discuss network activities and plan future initiatives",
    type: "meeting",
    status: "upcoming",
    startDate: "2024-03-15",
    endDate: "2024-03-15",
    location: "Lilongwe Conference Center",
    participants: 25,
    maxParticipants: 30,
    organizer: "John Doe",
    budget: "$2,500",
  },
  {
    id: "2",
    title: "Capacity Building Workshop",
    description:
      "Training session on project management and fundraising for network members",
    type: "workshop",
    status: "ongoing",
    startDate: "2024-03-10",
    endDate: "2024-03-12",
    location: "Blantyre Training Center",
    participants: 18,
    maxParticipants: 20,
    organizer: "Jane Smith",
    budget: "$5,000",
  },
  {
    id: "3",
    title: "Annual Network Conference",
    description:
      "Annual conference bringing together all network members and stakeholders",
    type: "conference",
    status: "completed",
    startDate: "2024-02-20",
    endDate: "2024-02-22",
    location: "Mzuzu Grand Hotel",
    participants: 45,
    maxParticipants: 50,
    organizer: "Mike Johnson",
    budget: "$15,000",
  },
  {
    id: "4",
    title: "Community Outreach Program",
    description:
      "Outreach activities to engage with local communities and raise awareness",
    type: "outreach",
    status: "upcoming",
    startDate: "2024-04-05",
    endDate: "2024-04-07",
    location: "Various Communities",
    participants: 12,
    maxParticipants: 15,
    organizer: "Sarah Wilson",
    budget: "$3,000",
  },
];

export default function NetworkActivitiesPage() {
  const [activities, setActivities] = useState<NetworkActivity[]>(
    mockNetworkActivities,
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<
    "all" | "meeting" | "workshop" | "training" | "conference" | "outreach"
  >("all");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "upcoming" | "ongoing" | "completed" | "cancelled"
  >("all");

  const filteredActivities = activities.filter((activity) => {
    const matchesSearch =
      activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.organizer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || activity.type === filterType;
    const matchesStatus =
      filterStatus === "all" || activity.status === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "upcoming":
        return "bg-blue-100 text-blue-800";
      case "ongoing":
        return "bg-green-100 text-green-800";
      case "completed":
        return "bg-gray-100 text-gray-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "meeting":
        return "bg-purple-100 text-purple-800";
      case "workshop":
        return "bg-blue-100 text-blue-800";
      case "training":
        return "bg-green-100 text-green-800";
      case "conference":
        return "bg-orange-100 text-orange-800";
      case "outreach":
        return "bg-pink-100 text-pink-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "meeting":
        return "👥";
      case "workshop":
        return "🔧";
      case "training":
        return "📚";
      case "conference":
        return "🏢";
      case "outreach":
        return "🤝";
      default:
        return "📅";
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Network Activities
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage and track network activities and events
        </p>
      </div>

      {/* Search and Filter Section */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"
                  size={20}
                />
                <Input
                  placeholder="Search activities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="meeting">Meeting</option>
                <option value="workshop">Workshop</option>
                <option value="training">Training</option>
                <option value="conference">Conference</option>
                <option value="outreach">Outreach</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="upcoming">Upcoming</option>
                <option value="ongoing">Ongoing</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Activities
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {activities.length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Upcoming
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {
                    activities.filter(
                      (activity) => activity.status === "upcoming",
                    ).length
                  }
                </p>
              </div>
              <Clock className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Participants
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {activities.reduce(
                    (total, activity) => total + activity.participants,
                    0,
                  )}
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Budget
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  $
                  {activities
                    .reduce((total, activity) => {
                      const budget = parseInt(
                        activity.budget.replace(/[^0-9]/g, ""),
                      );
                      return total + budget;
                    }, 0)
                    .toLocaleString()}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activities Grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {filteredActivities.map((activity) => (
          <Card key={activity.id} className="transition-shadow hover:shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{getTypeIcon(activity.type)}</span>
                  <div>
                    <CardTitle className="text-lg">{activity.title}</CardTitle>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {activity.organizer}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Badge className={getTypeColor(activity.type)}>
                    {activity.type.charAt(0).toUpperCase() +
                      activity.type.slice(1)}
                  </Badge>
                  <Badge className={getStatusColor(activity.status)}>
                    {activity.status.charAt(0).toUpperCase() +
                      activity.status.slice(1)}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="mb-4 text-gray-600 dark:text-gray-400">
                {activity.description}
              </p>

              <div className="mb-4 space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(activity.startDate).toLocaleDateString()} -{" "}
                    {new Date(activity.endDate).toLocaleDateString()}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {activity.location}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {activity.participants}/{activity.maxParticipants}{" "}
                    participants
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Budget: {activity.budget}
                  </span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="mr-1 h-4 w-4" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Edit className="mr-1 h-4 w-4" />
                  Edit
                </Button>
                <Button size="sm" variant="outline">
                  <MessageSquare className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredActivities.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              No activities found matching your criteria.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
