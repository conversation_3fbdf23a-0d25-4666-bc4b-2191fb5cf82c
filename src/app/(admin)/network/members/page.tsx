"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Users,
  Building2,
  MapPin,
  Eye,
  Edit,
  MessageSquare,
  Plus,
  Calendar,
  User,
  Phone,
  Mail,
} from "lucide-react";

interface NetworkMember {
  id: string;
  name: string;
  organization: string;
  role: "chair" | "vice_chair" | "secretary" | "treasurer" | "member";
  status: "active" | "inactive" | "pending";
  joinDate: string;
  location: string;
  email: string;
  phone: string;
  avatar: string;
}

const mockNetworkMembers: NetworkMember[] = [
  {
    id: "1",
    name: "<PERSON>",
    organization: "Community Development Initiative",
    role: "chair",
    status: "active",
    joinDate: "2023-01-15",
    location: "Lilongwe",
    email: "<EMAIL>",
    phone: "+265 123 456 789",
    avatar: "/images/user/user-01.png",
  },
  {
    id: "2",
    name: "<PERSON>",
    organization: "Global Aid Foundation",
    role: "vice_chair",
    status: "active",
    joinDate: "2023-02-20",
    location: "Blantyre",
    email: "<EMAIL>",
    phone: "+265 987 654 321",
    avatar: "/images/user/user-02.png",
  },
  {
    id: "3",
    name: "Mike Johnson",
    organization: "Local Youth Empowerment",
    role: "secretary",
    status: "active",
    joinDate: "2023-03-10",
    location: "Mzuzu",
    email: "<EMAIL>",
    phone: "+265 456 789 123",
    avatar: "/images/user/user-03.png",
  },
  {
    id: "4",
    name: "Sarah Wilson",
    organization: "Health First Initiative",
    role: "treasurer",
    status: "pending",
    joinDate: "2024-01-05",
    location: "Zomba",
    email: "<EMAIL>",
    phone: "+265 789 123 456",
    avatar: "/images/user/user-04.png",
  },
];

export default function NetworkMembersPage() {
  const [members, setMembers] = useState<NetworkMember[]>(mockNetworkMembers);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterRole, setFilterRole] = useState<
    "all" | "chair" | "vice_chair" | "secretary" | "treasurer" | "member"
  >("all");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "active" | "inactive" | "pending"
  >("all");

  const filteredMembers = members.filter((member) => {
    const matchesSearch =
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.organization.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === "all" || member.role === filterRole;
    const matchesStatus =
      filterStatus === "all" || member.status === filterStatus;

    return matchesSearch && matchesRole && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "chair":
        return "bg-purple-100 text-purple-800";
      case "vice_chair":
        return "bg-blue-100 text-blue-800";
      case "secretary":
        return "bg-green-100 text-green-800";
      case "treasurer":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleDisplayName = (role: string) => {
    return (
      role.replace("_", " ").charAt(0).toUpperCase() +
      role.replace("_", " ").slice(1)
    );
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Network Members
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage and communicate with network members
        </p>
      </div>

      {/* Search and Filter Section */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 lg:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"
                  size={20}
                />
                <Input
                  placeholder="Search members..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex gap-2">
              <select
                value={filterRole}
                onChange={(e) => setFilterRole(e.target.value as any)}
                className="rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Roles</option>
                <option value="chair">Chair</option>
                <option value="vice_chair">Vice Chair</option>
                <option value="secretary">Secretary</option>
                <option value="treasurer">Treasurer</option>
                <option value="member">Member</option>
              </select>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Members
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {members.length}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Active Members
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {
                    members.filter((member) => member.status === "active")
                      .length
                  }
                </p>
              </div>
              <User className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Organizations
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {new Set(members.map((member) => member.organization)).size}
                </p>
              </div>
              <Building2 className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Pending Approval
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {
                    members.filter((member) => member.status === "pending")
                      .length
                  }
                </p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Members Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredMembers.map((member) => (
          <Card key={member.id} className="transition-shadow hover:shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-200">
                  <User className="h-6 w-6 text-gray-600" />
                </div>
                <div className="flex-1">
                  <CardTitle className="text-lg">{member.name}</CardTitle>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {member.organization}
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-4 space-y-3">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {member.location}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Joined: {new Date(member.joinDate).toLocaleDateString()}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {member.email}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {member.phone}
                  </span>
                </div>
              </div>

              <div className="mb-4 flex gap-2">
                <Badge className={getRoleColor(member.role)}>
                  {getRoleDisplayName(member.role)}
                </Badge>
                <Badge className={getStatusColor(member.status)}>
                  {member.status.charAt(0).toUpperCase() +
                    member.status.slice(1)}
                </Badge>
              </div>

              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="mr-1 h-4 w-4" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <MessageSquare className="mr-1 h-4 w-4" />
                  Message
                </Button>
                <Button size="sm" variant="outline">
                  <Edit className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredMembers.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              No members found matching your criteria.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
