"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineDocumentText,
  HiOutlineChartBar,
  HiOutlineUsers,
  HiOutlineCalendar,
  HiOutlineArrowDownTray,
  HiOutlineEye,
  HiOutlinePencil,
  HiOutlineTrash,
  HiOutlineFunnel,
  HiOutlineMagnifyingGlass,
} from "react-icons/hi2";

interface NetworkReport {
  id: string;
  title: string;
  type: "activity" | "membership" | "financial" | "performance";
  status: "draft" | "published" | "archived";
  createdDate: string;
  lastUpdated: string;
  author: string;
  description: string;
  fileSize?: string;
}

export default function NetworkReportsPage() {
  const [reports, setReports] = useState<NetworkReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");

  useEffect(() => {
    // Simulate API call
    const fetchReports = async () => {
      setLoading(true);
      // Mock data
      const mockReports: NetworkReport[] = [
        {
          id: "1",
          title: "Q4 2023 Network Activity Report",
          type: "activity",
          status: "published",
          createdDate: "2024-01-15",
          lastUpdated: "2024-01-20",
          author: "John Banda",
          description:
            "Comprehensive report on network activities and engagement for Q4 2023",
          fileSize: "2.3 MB",
        },
        {
          id: "2",
          title: "Network Membership Analysis 2023",
          type: "membership",
          status: "published",
          createdDate: "2024-01-10",
          lastUpdated: "2024-01-15",
          author: "Sarah Johnson",
          description:
            "Analysis of network membership growth and retention rates",
          fileSize: "1.8 MB",
        },
        {
          id: "3",
          title: "Network Financial Performance Q4",
          type: "financial",
          status: "draft",
          createdDate: "2024-01-25",
          lastUpdated: "2024-01-25",
          author: "Michael Chen",
          description: "Financial performance and budget utilization report",
          fileSize: "3.1 MB",
        },
        {
          id: "4",
          title: "Network Impact Assessment 2023",
          type: "performance",
          status: "published",
          createdDate: "2024-01-05",
          lastUpdated: "2024-01-12",
          author: "Mary Phiri",
          description: "Assessment of network impact and outcomes for 2023",
          fileSize: "4.2 MB",
        },
        {
          id: "5",
          title: "Network Strategy Review 2024",
          type: "performance",
          status: "draft",
          createdDate: "2024-01-30",
          lastUpdated: "2024-01-30",
          author: "David Mhango",
          description:
            "Strategic review and planning for 2024 network activities",
          fileSize: "2.8 MB",
        },
      ];

      setTimeout(() => {
        setReports(mockReports);
        setLoading(false);
      }, 1000);
    };

    fetchReports();
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case "activity":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "membership":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "financial":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case "performance":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "draft":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case "archived":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "activity":
        return <HiOutlineCalendar className="h-5 w-5 text-blue-600" />;
      case "membership":
        return <HiOutlineUsers className="h-5 w-5 text-green-600" />;
      case "financial":
        return <HiOutlineChartBar className="h-5 w-5 text-purple-600" />;
      case "performance":
        return <HiOutlineDocumentText className="h-5 w-5 text-orange-600" />;
      default:
        return <HiOutlineDocumentText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "activity":
        return "Activity Report";
      case "membership":
        return "Membership Report";
      case "financial":
        return "Financial Report";
      case "performance":
        return "Performance Report";
      default:
        return "Report";
    }
  };

  const filteredReports = reports.filter((report) => {
    const matchesSearch =
      report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.author.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || report.type === filterType;
    const matchesStatus =
      filterStatus === "all" || report.status === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading reports...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Network Reports
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage and view network reports and analytics
          </p>
        </div>
        <div className="flex gap-2">
          <button className="flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm transition hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:hover:bg-gray-700">
            <HiOutlineArrowDownTray className="h-4 w-4" />
            Export All
          </button>
          <button className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-primary/80">
            <HiOutlineDocumentText className="h-4 w-4" />
            Create Report
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Reports
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {reports.length}
              </p>
            </div>
            <div className="rounded-full bg-blue-100 p-3 dark:bg-blue-900">
              <HiOutlineDocumentText className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Published
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {reports.filter((r) => r.status === "published").length}
              </p>
            </div>
            <div className="rounded-full bg-green-100 p-3 dark:bg-green-900">
              <HiOutlineDocumentText className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Drafts
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {reports.filter((r) => r.status === "draft").length}
              </p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3 dark:bg-yellow-900">
              <HiOutlineDocumentText className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                This Month
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {
                  reports.filter(
                    (r) =>
                      new Date(r.createdDate).getMonth() ===
                      new Date().getMonth(),
                  ).length
                }
              </p>
            </div>
            <div className="rounded-full bg-purple-100 p-3 dark:bg-purple-900">
              <HiOutlineCalendar className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="relative max-w-md flex-1">
          <HiOutlineMagnifyingGlass className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search reports..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          />
        </div>

        <div className="flex gap-2">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            <option value="all">All Types</option>
            <option value="activity">Activity Reports</option>
            <option value="membership">Membership Reports</option>
            <option value="financial">Financial Reports</option>
            <option value="performance">Performance Reports</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
          >
            <option value="all">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>
      </div>

      {/* Reports Table */}
      <div className="rounded-lg bg-white shadow-sm dark:bg-gray-800">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Network Reports
          </h2>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Report
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Type
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Author
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Created
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredReports.map((report) => (
                  <tr
                    key={report.id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
                          {getTypeIcon(report.type)}
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {report.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {report.description}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span
                        className={`rounded-full px-2 py-1 text-xs ${getTypeColor(report.type)}`}
                      >
                        {getTypeLabel(report.type)}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span
                        className={`rounded-full px-2 py-1 text-xs ${getStatusColor(report.status)}`}
                      >
                        {report.status}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-gray-900 dark:text-white">
                        {report.author}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div>
                        <p className="text-sm text-gray-900 dark:text-white">
                          {report.createdDate}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Updated: {report.lastUpdated}
                        </p>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center gap-2">
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlineEye className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlineArrowDownTray className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-300">
                          <HiOutlinePencil className="h-4 w-4" />
                        </button>
                        <button className="rounded p-1 text-red-400 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-300">
                          <HiOutlineTrash className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredReports.length === 0 && (
            <div className="py-8 text-center">
              <HiOutlineDocumentText className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                No reports found matching your criteria.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
