"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineCalendar,
  HiOutlineDocumentText,
  HiOutlineEye,
  HiOutlineArrowDownTray,
  HiOutlineInformationCircle,
} from "react-icons/hi2";
import { format, isAfter, isBefore, addDays } from "date-fns";
import {
  getSubscriptionStatus,
  Subscription,
} from "@/services/subscription.services";

export default function SubscriptionStatusPage() {
  const token = localStorage.getItem("accessToken") || "";
  const [subscriptions, setSubscriptions] = useState<Record<number, Subscription>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Get current user's NGO ID from localStorage or context
  const userStr = localStorage.getItem("user");
  const user = userStr ? JSON.parse(userStr) : null;
  const ngoId = user?.ngoId;

  // Generate year options (current year and 2 years back)
  const currentYear = new Date().getFullYear();
  const yearOptions = [currentYear, currentYear - 1, currentYear - 2];

  useEffect(() => {
    if (ngoId) {
      fetchSubscriptionHistory();
    }
  }, [ngoId]);

  const fetchSubscriptionHistory = async () => {
    try {
      setLoading(true);
      const subscriptionData: Record<number, Subscription> = {};

      // Fetch subscriptions for each year
      for (const year of yearOptions) {
        try {
          const response = await getSubscriptionStatus(ngoId, token, year);
          if (response.status === "success" && response.data?.hasSubscription) {
            subscriptionData[year] = response.data;
          }
        } catch (err) {
          console.error(`Error fetching subscription for ${year}:`, err);
        }
      }

      setSubscriptions(subscriptionData);
    } catch (err: any) {
      setError("Failed to fetch subscription history");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";
      case "rejected":
        return "text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <HiOutlineCheckCircle className="h-5 w-5 text-green-600" />;
      case "rejected":
        return <HiOutlineXCircle className="h-5 w-5 text-red-600" />;
      default:
        return <HiOutlineClock className="h-5 w-5 text-yellow-600" />;
    }
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const thirtyDaysFromNow = addDays(new Date(), 30);
    return isAfter(thirtyDaysFromNow, expiry) && isBefore(new Date(), expiry);
  };

  const isExpired = (expiryDate: string) => {
    return isBefore(new Date(expiryDate), new Date());
  };

  const handleViewCertificate = (certificateId: string) => {
    // Navigate to certificate view or open in new tab
    console.log("Viewing certificate:", certificateId);
  };

  const handleDownloadCertificate = (certificateId: string) => {
    // Download certificate
    console.log("Downloading certificate:", certificateId);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Subscription Status & History
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          View your annual subscription status and download certificates
        </p>
      </div>

      {/* Current Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Current Year Status */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {currentYear} Status
            </h3>
            {subscriptions[currentYear] && getStatusIcon(subscriptions[currentYear].approvedStatus)}
          </div>
          
          {subscriptions[currentYear] ? (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span
                  className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                    subscriptions[currentYear].approvedStatus
                  )}`}
                >
                  {subscriptions[currentYear].approvedStatus.charAt(0).toUpperCase() + 
                   subscriptions[currentYear].approvedStatus.slice(1)}
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Expires: {format(new Date(subscriptions[currentYear].expiryDate), "MMM dd, yyyy")}
              </p>
            </div>
          ) : (
            <div className="text-center py-4">
              <HiOutlineXCircle className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No subscription for {currentYear}
              </p>
            </div>
          )}
        </div>

        {/* Expiry Warning */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Expiry Status
            </h3>
            <HiOutlineCalendar className="h-5 w-5 text-gray-400" />
          </div>
          
          {subscriptions[currentYear] ? (
            <div className="space-y-2">
              {isExpired(subscriptions[currentYear].expiryDate) ? (
                <div className="flex items-center space-x-2 text-red-600">
                  <HiOutlineXCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">Expired</span>
                </div>
              ) : isExpiringSoon(subscriptions[currentYear].expiryDate) ? (
                <div className="flex items-center space-x-2 text-yellow-600">
                  <HiOutlineClock className="h-4 w-4" />
                  <span className="text-sm font-medium">Expiring Soon</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2 text-green-600">
                  <HiOutlineCheckCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">Active</span>
                </div>
              )}
            </div>
          ) : (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              No active subscription
            </p>
          )}
        </div>

        {/* Certificate Status */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Certificate
            </h3>
            <HiOutlineDocumentText className="h-5 w-5 text-gray-400" />
          </div>
          
          {subscriptions[currentYear]?.certificate ? (
            <div className="space-y-2">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                #{subscriptions[currentYear].certificate!.certificateNumber}
              </p>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleViewCertificate(subscriptions[currentYear].certificate!.id)}
                  className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors text-sm"
                >
                  <HiOutlineEye className="h-4 w-4" />
                  <span>View</span>
                </button>
                <button
                  onClick={() => handleDownloadCertificate(subscriptions[currentYear].certificate!.id)}
                  className="flex items-center space-x-1 px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors text-sm"
                >
                  <HiOutlineArrowDownTray className="h-4 w-4" />
                  <span>Download</span>
                </button>
              </div>
            </div>
          ) : (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              No certificate available
            </p>
          )}
        </div>
      </div>

      {/* Subscription History */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Subscription History
          </h2>
        </div>
        
        <div className="p-6">
          {Object.keys(subscriptions).length === 0 ? (
            <div className="text-center py-8">
              <HiOutlineDocumentText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No Subscription History
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                You haven't submitted any subscription payments yet.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {yearOptions.map((year) => {
                const subscription = subscriptions[year];
                
                return (
                  <div
                    key={year}
                    className="border border-gray-200 dark:border-gray-600 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {year} Subscription
                      </h3>
                      {subscription ? (
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(subscription.approvedStatus)}
                          <span
                            className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                              subscription.approvedStatus
                            )}`}
                          >
                            {subscription.approvedStatus.charAt(0).toUpperCase() + 
                             subscription.approvedStatus.slice(1)}
                          </span>
                        </div>
                      ) : (
                        <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                          Not Submitted
                        </span>
                      )}
                    </div>

                    {subscription ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Submitted</p>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {format(new Date(subscription.submittedAt), "MMM dd, yyyy")}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Expires</p>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {format(new Date(subscription.expiryDate), "MMM dd, yyyy")}
                          </p>
                        </div>
                        {subscription.resolvedBy && (
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Resolved By</p>
                            <p className="font-medium text-gray-900 dark:text-white">
                              {subscription.resolvedBy.name}
                            </p>
                          </div>
                        )}
                        {subscription.certificate && (
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Certificate</p>
                            <div className="flex space-x-2 mt-1">
                              <button
                                onClick={() => handleViewCertificate(subscription.certificate!.id)}
                                className="flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200 transition-colors"
                              >
                                <HiOutlineEye className="h-3 w-3" />
                                <span>View</span>
                              </button>
                              <button
                                onClick={() => handleDownloadCertificate(subscription.certificate!.id)}
                                className="flex items-center space-x-1 px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200 transition-colors"
                              >
                                <HiOutlineArrowDownTray className="h-3 w-3" />
                                <span>Download</span>
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                        <HiOutlineInformationCircle className="h-4 w-4" />
                        <span className="text-sm">No subscription submitted for {year}</span>
                      </div>
                    )}

                    {subscription?.rejectedReason && (
                      <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
                        <div className="flex items-start space-x-2">
                          <HiOutlineInformationCircle className="h-4 w-4 text-red-600 mt-0.5" />
                          <div>
                            <p className="text-sm font-medium text-red-800 dark:text-red-200">
                              Rejection Reason
                            </p>
                            <p className="text-sm text-red-700 dark:text-red-300">
                              {subscription.rejectedReason}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded-lg">
          {error}
        </div>
      )}
    </div>
  );
}
