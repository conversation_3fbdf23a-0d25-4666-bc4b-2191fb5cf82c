"use client";

import React, { useState, useEffect } from "react";
import {
  HiOutlineCreditCard,
  HiOutlineCheckCircle,
  HiOutlineXCircle,
  HiOutlineClock,
  HiOutlineCalendar,
  HiOutlineDocumentText,
  HiOutlineCloudArrowUp,
  HiOutlineInformationCircle,
} from "react-icons/hi2";
import { format } from "date-fns";
import {
  createSubscription,
  getSubscriptionStatus,
  Subscription,
} from "@/services/subscription.services";

interface SubscriptionFormData {
  proofOfPayment: File | null;
}

export default function SubscriptionManagePage() {
  const token = localStorage.getItem("accessToken") || "";
  const [currentSubscription, setCurrentSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [formData, setFormData] = useState<SubscriptionFormData>({
    proofOfPayment: null,
  });
  const [dragActive, setDragActive] = useState(false);

  // Get current user's NGO ID from localStorage or context
  const userStr = localStorage.getItem("user");
  const user = userStr ? JSON.parse(userStr) : null;
  const ngoId = user?.ngoId;

  useEffect(() => {
    if (ngoId) {
      fetchSubscriptionStatus();
    }
  }, [ngoId]);

  const fetchSubscriptionStatus = async () => {
    try {
      setLoading(true);
      const currentYear = new Date().getFullYear();
      const response = await getSubscriptionStatus(ngoId, token, currentYear);
      
      if (response.status === "success" && response.data?.hasSubscription) {
        setCurrentSubscription(response.data);
      } else {
        setCurrentSubscription(null);
      }
    } catch (err: any) {
      setError("Failed to fetch subscription status");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      setError('Please upload a valid file (JPEG, PNG, or PDF)');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB');
      return;
    }

    setFormData({ proofOfPayment: file });
    setError(null);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileChange(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.proofOfPayment) {
      setError("Please upload proof of payment");
      return;
    }

    if (!ngoId) {
      setError("NGO ID not found. Please ensure you're associated with an organization.");
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await createSubscription(ngoId, formData.proofOfPayment, token);
      
      if (response.status === "success") {
        setSuccess("Subscription submitted successfully! Your payment is under review.");
        setFormData({ proofOfPayment: null });
        // Refresh subscription status
        await fetchSubscriptionStatus();
      } else {
        setError(response.message || "Failed to submit subscription");
      }
    } catch (err: any) {
      setError(err.message || "Error submitting subscription");
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";
      case "rejected":
        return "text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <HiOutlineCheckCircle className="h-5 w-5 text-green-600" />;
      case "rejected":
        return <HiOutlineXCircle className="h-5 w-5 text-red-600" />;
      default:
        return <HiOutlineClock className="h-5 w-5 text-yellow-600" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Annual Subscription Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Submit your annual subscription payment for {new Date().getFullYear()}
        </p>
      </div>

      {/* Current Subscription Status */}
      {currentSubscription && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Current Subscription Status
            </h2>
            <div className="flex items-center space-x-2">
              {getStatusIcon(currentSubscription.approvedStatus)}
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                  currentSubscription.approvedStatus
                )}`}
              >
                {currentSubscription.approvedStatus.charAt(0).toUpperCase() + 
                 currentSubscription.approvedStatus.slice(1)}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Submitted Date</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {format(new Date(currentSubscription.submittedAt), "MMM dd, yyyy")}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Expiry Date</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {format(new Date(currentSubscription.expiryDate), "MMM dd, yyyy")}
              </p>
            </div>
            {currentSubscription.resolvedBy && (
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Resolved By</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {currentSubscription.resolvedBy.name}
                </p>
              </div>
            )}
            {currentSubscription.resolvedAt && (
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Resolved Date</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {format(new Date(currentSubscription.resolvedAt), "MMM dd, yyyy")}
                </p>
              </div>
            )}
          </div>

          {currentSubscription.rejectedReason && (
            <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-start space-x-2">
                <HiOutlineInformationCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-red-800 dark:text-red-200">
                    Rejection Reason
                  </p>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    {currentSubscription.rejectedReason}
                  </p>
                </div>
              </div>
            </div>
          )}

          {currentSubscription.certificate && (
            <div className="mt-4 p-4 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    Certificate Generated
                  </p>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Certificate #{currentSubscription.certificate.certificateNumber}
                  </p>
                </div>
                <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                  View Certificate
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Subscription Form */}
      {(!currentSubscription || currentSubscription.approvedStatus === "rejected") && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {currentSubscription?.approvedStatus === "rejected" 
              ? "Resubmit Annual Subscription" 
              : "Submit Annual Subscription"}
          </h2>

          {/* Payment Instructions */}
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-start space-x-2">
              <HiOutlineInformationCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Payment Instructions
                </p>
                <ul className="text-sm text-blue-700 dark:text-blue-300 mt-2 space-y-1">
                  <li>• Annual subscription fee: $500 USD</li>
                  <li>• Payment methods: Bank transfer, Mobile money</li>
                  <li>• Bank: Standard Bank, Account: *********</li>
                  <li>• Mobile Money: +265 999 123 456</li>
                  <li>• Reference: Your organization registration number</li>
                </ul>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Proof of Payment *
              </label>
              <div
                className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
                  dragActive
                    ? "border-blue-400 bg-blue-50 dark:bg-blue-900/30"
                    : "border-gray-300 dark:border-gray-600 hover:border-gray-400"
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={(e) => e.target.files?.[0] && handleFileChange(e.target.files[0])}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                <div className="text-center">
                  <HiOutlineCloudArrowUp className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formData.proofOfPayment ? (
                        <span className="text-green-600 font-medium">
                          Selected: {formData.proofOfPayment.name}
                        </span>
                      ) : (
                        <>
                          <span className="font-medium text-blue-600 hover:text-blue-500">
                            Click to upload
                          </span>{" "}
                          or drag and drop
                        </>
                      )}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      PNG, JPG, PDF up to 5MB
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="p-4 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-200 rounded-lg">
                {error}
              </div>
            )}

            {/* Success Message */}
            {success && (
              <div className="p-4 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-200 rounded-lg">
                {success}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={submitting || !formData.proofOfPayment}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <HiOutlineDocumentText className="h-4 w-4" />
                    <span>Submit Subscription</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Pending Submission Message */}
      {currentSubscription?.approvedStatus === "pending" && (
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
          <div className="flex items-center space-x-2">
            <HiOutlineClock className="h-6 w-6 text-yellow-600" />
            <div>
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200">
                Subscription Under Review
              </h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Your subscription payment is currently being reviewed by our financial team. 
                You will be notified once it's approved.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
