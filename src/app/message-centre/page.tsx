"use client";
import React, { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import {
  HiOutlineBuildingOffice2,
  HiOutlinePaperAirplane,
  HiOutlineUser,
  HiOutlineClock,
} from "react-icons/hi2";

interface Message {
  id: number;
  sender: string;
  content: string;
  timestamp: string;
  isOwn: boolean;
  networkId: number;
  networkType: string;
}

interface Network {
  id: number;
  name: string;
  type: string;
  coordinator: string;
  region?: string;
  district?: string;
}

const mockMessages: Message[] = [
  {
    id: 1,
    sender: "<PERSON>",
    content: "Hello everyone! Welcome to our network meeting.",
    timestamp: "2024-01-15T10:30:00",
    isOwn: false,
    networkId: 1,
    networkType: "sector",
  },
  {
    id: 2,
    sender: "<PERSON>",
    content: "Thank you <PERSON>! Looking forward to our collaboration.",
    timestamp: "2024-01-15T10:32:00",
    isOwn: false,
    networkId: 1,
    networkType: "sector",
  },
  {
    id: 3,
    sender: "You",
    content: "Great to be here! Let's make this network successful.",
    timestamp: "2024-01-15T10:35:00",
    isOwn: true,
    networkId: 1,
    networkType: "sector",
  },
  {
    id: 4,
    sender: "Mike Brown",
    content: "I have some updates on our recent activities.",
    timestamp: "2024-01-15T10:40:00",
    isOwn: false,
    networkId: 1,
    networkType: "sector",
  },
];

const mockNetworks: Network[] = [
  {
    id: 1,
    name: "Sector Network 1",
    type: "Health",
    coordinator: "John Doe",
    region: "North",
  },
  {
    id: 2,
    name: "Sector Network 2",
    type: "Education",
    coordinator: "Jane Smith",
    region: "South",
  },
  {
    id: 3,
    name: "District Network 1",
    type: "Health",
    coordinator: "Anna White",
    district: "District A",
  },
  {
    id: 4,
    name: "District Network 2",
    type: "Education",
    coordinator: "Brian Black",
    district: "District B",
  },
];

function MessageCentreContent() {
  const searchParams = useSearchParams();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [currentNetwork, setCurrentNetwork] = useState<Network | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get network context from URL parameters
  const networkId = searchParams.get("network");
  const networkType = searchParams.get("type");
  const networkName = searchParams.get("name");

  useEffect(() => {
    // Simulate loading network data
    setTimeout(() => {
      if (networkId && networkType && networkName) {
        const network = mockNetworks.find((n) => n.id === parseInt(networkId));
        if (network) {
          setCurrentNetwork(network);
          // Filter messages for this network
          const networkMessages = mockMessages.filter(
            (msg) =>
              msg.networkId === parseInt(networkId) &&
              msg.networkType === networkType,
          );
          setMessages(networkMessages);
        }
      }
      setIsLoading(false);
    }, 500);
  }, [networkId, networkType, networkName]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !currentNetwork) return;

    const message: Message = {
      id: messages.length + 1,
      sender: "You",
      content: newMessage,
      timestamp: new Date().toISOString(),
      isOwn: true,
      networkId: currentNetwork.id,
      networkType: networkType || "sector",
    };

    setMessages([...messages, message]);
    setNewMessage("");
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-4xl">💬</div>
          <div className="text-lg font-semibold text-gray-600">
            Loading Message Centre...
          </div>
        </div>
      </div>
    );
  }

  if (!currentNetwork) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-4xl">❌</div>
          <div className="text-lg font-semibold text-gray-600">
            Network not found
          </div>
          <div className="text-sm text-gray-500">
            Please return to the networks page and try again.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col rounded-lg bg-white shadow-lg dark:bg-gray-800">
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex size-12 items-center justify-center rounded-full bg-primary/10 dark:bg-primary/20">
              <HiOutlineBuildingOffice2 className="size-6 text-primary" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                {currentNetwork.name}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {currentNetwork.type} Network • {currentNetwork.coordinator}
                {currentNetwork.region && ` • ${currentNetwork.region}`}
                {currentNetwork.district && ` • ${currentNetwork.district}`}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
              Online
            </span>
          </div>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="mx-auto max-w-4xl space-y-4">
          {messages.length === 0 ? (
            <div className="py-8 text-center">
              <div className="mb-4 text-4xl">💬</div>
              <div className="text-lg font-semibold text-gray-600 dark:text-gray-400">
                No messages yet
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-500">
                Start the conversation by sending a message below.
              </div>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isOwn ? "justify-end" : "justify-start"}`}
              >
                <div
                  className={`max-w-xs rounded-lg px-4 py-2 ${
                    message.isOwn
                      ? "bg-primary text-white"
                      : "bg-white text-gray-900 shadow-sm dark:bg-gray-700 dark:text-white"
                  }`}
                >
                  <div className="mb-1 flex items-center space-x-2">
                    <HiOutlineUser className="size-4" />
                    <span className="text-xs font-medium">
                      {message.sender}
                    </span>
                  </div>
                  <p className="text-sm">{message.content}</p>
                  <div className="mt-2 flex items-center justify-end space-x-1">
                    <HiOutlineClock className="size-3" />
                    <span className="text-xs opacity-75">
                      {formatTime(message.timestamp)}
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Message Input */}
      <div className="border-t border-gray-200 px-6 py-4 dark:border-gray-700">
        <div className="mx-auto max-w-4xl">
          <form onSubmit={handleSendMessage} className="flex space-x-4">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm focus:border-primary focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
            <button
              type="submit"
              disabled={!newMessage.trim()}
              className="flex items-center space-x-2 rounded-lg bg-primary px-6 py-3 text-white transition hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <HiOutlinePaperAirplane className="size-4" />
              <span>Send</span>
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function MessageCentrePage() {
  return (
    <Suspense
      fallback={
        <div className="flex h-full items-center justify-center">
          <div className="text-center">
            <div className="mb-4 text-4xl">💬</div>
            <div className="text-lg font-semibold text-gray-600">
              Loading Message Centre...
            </div>
          </div>
        </div>
      }
    >
      <MessageCentreContent />
    </Suspense>
  );
}
