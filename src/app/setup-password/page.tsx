'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, Button, Input, Form, message, Spin } from 'antd';
import { EyeInvisibleOutlined, EyeTwoTone, LockOutlined, UserOutlined } from '@ant-design/icons';

export default function SetupPasswordPage() {
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [userInfo, setUserInfo] = useState<{fullname: string, email: string} | null>(null);
  const [error, setError] = useState('');
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get('token');
  const [form] = Form.useForm();

  useEffect(() => {
    if (!token) {
      setError('Invalid invitation link. No token provided.');
      setVerifying(false);
      return;
    }

    verifyToken(token);
  }, [token]);

  const verifyToken = async (verificationToken: string) => {
    try {
      // First, let's decode the token to get user info
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3009'}/api/auth/verify-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken }),
      });

      const data = await response.json();

      if (response.ok) {
        setUserInfo({
          fullname: data.user.fullname,
          email: data.user.email
        });
        setError('');
      } else {
        setError(data.message || 'Invalid or expired invitation link.');
      }
    } catch (err) {
      setError('Network error. Please check your connection and try again.');
    } finally {
      setVerifying(false);
    }
  };

  const handleSetupPassword = async (values: { password: string; confirmPassword: string }) => {
    if (!token) return;

    setLoading(true);
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3009'}/api/auth/verify-email-and-setup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          token: token,
          password: values.password 
        }),
      });

      const data = await response.json();

      if (response.ok) {
        message.success('Password set successfully! You can now sign in.');
        setTimeout(() => {
          router.push('/?showLogin=true');
        }, 2000);
      } else {
        console.error('Password setup error:', data);
        message.error(data.message || 'Failed to set password. Please try again.');
      }
    } catch (err) {
      message.error('Network error. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const validatePassword = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('Password is required'));
    }
    if (value.length < 8) {
      return Promise.reject(new Error('Password must be at least 8 characters long'));
    }
    if (!/(?=.*[a-z])/.test(value)) {
      return Promise.reject(new Error('Password must contain at least one lowercase letter'));
    }
    if (!/(?=.*[A-Z])/.test(value)) {
      return Promise.reject(new Error('Password must contain at least one uppercase letter'));
    }
    if (!/(?=.*\d)/.test(value)) {
      return Promise.reject(new Error('Password must contain at least one number'));
    }
    if (!/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(value)) {
      return Promise.reject(new Error('Password must contain at least one special character'));
    }
    return Promise.resolve();
  };

  const validateConfirmPassword = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('Please confirm your password'));
    }
    if (value !== form.getFieldValue('password')) {
      return Promise.reject(new Error('Passwords do not match'));
    }
    return Promise.resolve();
  };

  if (verifying) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md text-center">
          <Spin size="large" />
          <p className="mt-4 text-gray-600">Verifying invitation...</p>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Invitation</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button type="primary" onClick={() => router.push('/')}>
            Go to Home
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <div className="text-center mb-6">
          <div className="text-blue-500 text-6xl mb-4">🎉</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to CONGOMA!</h2>
          <p className="text-gray-600">
            Hi <strong>{userInfo?.fullname}</strong>, please set your password to complete your account setup.
          </p>
        </div>

        <Form
          form={form}
          name="setup-password"
          onFinish={handleSetupPassword}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            label="Email"
            initialValue={userInfo?.email}
          >
            <Input
              prefix={<UserOutlined />}
              disabled
              value={userInfo?.email}
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[{ validator: validatePassword }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Enter your password"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label="Confirm Password"
            rules={[{ validator: validateConfirmPassword }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Confirm your password"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="w-full"
            >
              {loading ? 'Setting up...' : 'Complete Setup'}
            </Button>
          </Form.Item>
        </Form>

        <div className="text-center text-sm text-gray-500 mt-4">
          <p>Password requirements:</p>
          <ul className="text-xs mt-2 space-y-1">
            <li>• At least 8 characters long</li>
            <li>• Contains uppercase and lowercase letters</li>
            <li>• Contains at least one number</li>
            <li>• Contains at least one special character (!@#$%^&*etc.)</li>
          </ul>
        </div>
      </Card>
    </div>
  );
}
