'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { OrganizationForm } from '@/components/OrganizationForm';
import { getCurrentUser } from '@/utils/auth.utils';
import { Card, Spin, Alert } from 'antd';

export default function CreateOrganizationPage() {
  const [loading, setLoading] = useState(true);
  const [hasOrganization, setHasOrganization] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const router = useRouter();

  useEffect(() => {
    checkUserAndOrganization();
  }, []);

  const checkUserAndOrganization = async () => {
    try {
      const currentUser = await getCurrentUser();
      
      if (!currentUser) {
        router.push('/');
        return;
      }

      setUser(currentUser);

      // Check if user already has an organization
      if (currentUser.ngoId) {
        setHasOrganization(true);
      }
    } catch (err) {
      setError('Failed to load user information');
      console.error('Error checking user:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleOrganizationSubmit = async (response: any) => {
    try {
      // Organization created successfully, response contains updated user data
      console.log('Organization created successfully:', response);
      
      // The OrganizationForm already updated localStorage with new tokens and user data
      // Show success message first
      setShowSuccess(true);
      
      // Redirect to overview after 3 seconds
      setTimeout(() => {
        router.push('/overview');
      }, 3000);
    } catch (err) {
      setError('Failed to create organization');
      console.error('Error creating organization:', err);
    }
  };

  const handleCancel = () => {
    router.push('/overview');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md text-center">
          <Spin size="large" />
          <p className="mt-4 text-gray-600">Loading...</p>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            action={
              <button
                onClick={() => router.push('/dashboard')}
                className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/80"
              >
                Go to Dashboard
              </button>
            }
          />
        </Card>
      </div>
    );
  }

  if (showSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-lg text-center">
          <div className="mb-6">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-800">Organization Created Successfully!</h2>
            <p className="text-gray-600 mt-3 mb-4">
              Your organization has been submitted for review. You will be notified once it has been approved by the CONGOMA team.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <p className="text-blue-800 text-sm">
                <strong>What happens next?</strong><br />
                • Your organization will be reviewed by our team<br />
                • You'll receive an email notification once approved<br />
                • Full access will be granted upon approval
              </p>
            </div>
            <p className="text-sm text-gray-500">
              Redirecting to dashboard in a few seconds...
            </p>
          </div>
        </Card>
      </div>
    );
  }

  if (hasOrganization) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md text-center">
          <div className="mb-4">
            <h2 className="text-xl font-semibold text-gray-800">Organization Already Exists</h2>
            <p className="text-gray-600 mt-2">
              You already belong to an organization. You cannot create another one.
            </p>
          </div>
          <button
            onClick={() => router.push('/overview')}
            className="px-6 py-2 bg-primary text-white rounded hover:bg-primary/80"
          >
            Go to Dashboard
          </button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-6 text-center">
          <h1 className="text-3xl font-bold text-gray-800">Create Your Organization</h1>
          <p className="text-gray-600 mt-2">
            Complete the form below to register your NGO with CONGOMA
          </p>
        </div>
        
        <OrganizationForm
          onSubmit={handleOrganizationSubmit}
          onCancel={handleCancel}
          type="local"
        />
      </div>
    </div>
  );
}
