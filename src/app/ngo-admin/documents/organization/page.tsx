"use client";

import React, { useState, useEffect, useRef } from "react";
import { getCurrentUser } from "@/utils/auth.utils";
import { User } from "@/services/auth.services";
import { 
  getOrganizationDocuments,
  uploadOrganizationDocument,
  getDocumentTypeDisplayName, 
  getDocumentFieldName, 
  isDocumentRequired,
  type DocumentType,
  type OrganizationDocument 
} from "@/services/organization-documents.services";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Upload, 
  Download, 
  Eye, 
  Trash2, 
  FileText, 
  AlertCircle, 
  CheckCircle,
  Clock,
  RefreshCw
} from "lucide-react";
import { NgoAdminOnly } from "@/components/Auth/AuthWrapper";

function OrganizationDocumentsContent() {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [organization, setOrganization] = useState<any>(null);
  const [documents, setDocuments] = useState<OrganizationDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState<string | null>(null);
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  useEffect(() => {
    const initializeData = async () => {
      try {
        const currentUser = await getCurrentUser();
        const accessToken = localStorage.getItem("accessToken");
        
        console.log("=== ORGANIZATION DOCS DEBUG ===");
        console.log("Current user:", currentUser);
        console.log("User ngoId:", currentUser?.ngoId);
        console.log("Access token present:", !!accessToken);
        
        setUser(currentUser);
        setToken(accessToken);
        
        if (currentUser?.ngoId && accessToken) {
          await fetchOrganizationData(currentUser.ngoId, accessToken);
        } else {
          console.error("Missing required data:", { 
            hasUser: !!currentUser, 
            hasNgoId: !!currentUser?.ngoId, 
            hasToken: !!accessToken 
          });
          setLoading(false);
        }
      } catch (error) {
        console.error("Error initializing data:", error);
        setLoading(false);
      }
    };

    initializeData();
  }, []);

  const fetchOrganizationData = async (ngoId: string, accessToken: string) => {
    try {
      setLoading(true);
      console.log("=== API CALL DEBUG ===");
      console.log("NGO ID:", ngoId);
      console.log("Token:", accessToken?.substring(0, 20) + "...");
      
      // Direct API call to test
      const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3009"}/api/ngos/${ngoId}`;
      console.log("API URL:", apiUrl);
      
      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${accessToken}`,
          "Content-Type": "application/json"
        }
      });
      
      console.log("Response status:", response.status);
      
      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
      }
      
      const data = await response.json();
      console.log("Raw API response:", data);
      
      if (data.status === "success" && data.data) {
        const ngo = data.data;
        
        // Map NGO data to documents
        const mappedDocuments: OrganizationDocument[] = [
          {
            id: "constitution",
            name: "Constitution",
            type: "constitution",
            url: ngo.constitutionUrl || "",
            uploadDate: ngo.updatedAt || ngo.createdAt || new Date().toISOString(),
            status: ngo.constitutionUrl ? "uploaded" : "missing",
            required: false,
          },
          {
            id: "minutes_of_first_meeting",
            name: "Minutes of First Meeting",
            type: "minutes_of_first_meeting",
            url: ngo.minutesOfFirstMeetingUrl || "",
            uploadDate: ngo.updatedAt || ngo.createdAt || new Date().toISOString(),
            status: ngo.minutesOfFirstMeetingUrl ? "uploaded" : "missing",
            required: true,
          },
          {
            id: "certificate_from_registrar_general",
            name: "Certificate from Registrar General",
            type: "certificate_from_registrar_general",
            url: ngo.certificateFromRegistrarGeneralUrl || "",
            uploadDate: ngo.updatedAt || ngo.createdAt || new Date().toISOString(),
            status: ngo.certificateFromRegistrarGeneralUrl ? "uploaded" : "missing",
            required: true,
          },
          {
            id: "sworn_in_affidavit",
            name: "Sworn-in Affidavit",
            type: "sworn_in_affidavit",
            url: ngo.swornInAffidavitUrl || "",
            uploadDate: ngo.updatedAt || ngo.createdAt || new Date().toISOString(),
            status: ngo.swornInAffidavitUrl ? "uploaded" : "missing",
            required: false,
          },
          {
            id: "registration_fee",
            name: "Registration Fee Receipt",
            type: "registration_fee",
            url: ngo.registrationFeeUrl || "",
            uploadDate: ngo.updatedAt || ngo.createdAt || new Date().toISOString(),
            status: ngo.registrationFeeUrl ? "uploaded" : "missing",
            required: true,
          },
          {
            id: "processing_fee",
            name: "Processing Fee Receipt",
            type: "processing_fee",
            url: ngo.processingFeeUrl || "",
            uploadDate: ngo.updatedAt || ngo.createdAt || new Date().toISOString(),
            status: ngo.processingFeeUrl ? "uploaded" : "missing",
            required: true,
          },
        ];
        
        console.log("Mapped documents:", mappedDocuments);
        setDocuments(mappedDocuments);
        setOrganization({
          id: ngo._id,
          name: ngo.name,
          registrationNumber: ngo.registrationNumber,
        });
      } else {
        throw new Error("Invalid API response format");
      }
    } catch (error) {
      console.error("Error fetching organization data:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      alert(`Failed to load organization documents: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (documentType: DocumentType, file: File) => {
    if (!user?.ngoId || !token) return;

    try {
      setUploading(documentType);
      
      // Call the upload service
      const response = await uploadOrganizationDocument(user.ngoId, documentType, file, token);
      
      if (response.status === "success") {
        // Update document status
        setDocuments(prev => prev.map(doc => 
          doc.type === documentType 
            ? { ...doc, status: "uploaded", url: response.data?.url || "" }
            : doc
        ));
        
        alert(`${getDocumentTypeDisplayName(documentType)} uploaded successfully`);
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      alert("Failed to upload document");
    } finally {
      setUploading(null);
    }
  };

  const handleFileSelect = (documentType: DocumentType) => {
    const input = fileInputRefs.current[documentType];
    if (input) {
      input.click();
    }
  };

  const handleFileChange = (documentType: DocumentType, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
      if (!allowedTypes.includes(file.type)) {
        alert("Please upload a PDF or image file (JPEG, PNG)");
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        alert("File size must be less than 5MB");
        return;
      }

      handleFileUpload(documentType, file);
    }
  };

  const handleViewDocument = (url: string, documentName: string) => {
    if (url) {
      window.open(url, "_blank");
    } else {
      alert("Document not available");
    }
  };

  const handleDownloadDocument = (url: string, documentName: string) => {
    if (url) {
      const link = document.createElement("a");
      link.href = url;
      link.download = documentName;
      link.click();
    } else {
      alert("Document not available");
    }
  };

  const handleRefresh = () => {
    if (user?.ngoId && token) {
      fetchOrganizationData(user.ngoId, token);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "uploaded":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "missing":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "uploaded":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "missing":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (!user?.ngoId) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-gray-500">
              You need to be associated with an organization to manage documents.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Organization Documents</h1>
          <p className="text-gray-600 mt-2">
            Manage your organization's official documents and certificates
          </p>
          <div className="mt-2 text-sm text-gray-500">
            Debug: User ID: {user?._id}, NGO ID: {user?.ngoId}, Token: {token ? "Present" : "Missing"}
          </div>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={() => {
              console.log("Manual test - User:", user);
              console.log("Manual test - Token:", token);
              if (user?.ngoId && token) {
                fetchOrganizationData(user.ngoId, token);
              }
            }} 
            variant="outline"
          >
            Test API
          </Button>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {organization && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">{organization.name}</h2>
                <p className="text-gray-600">
                  Registration Number: {organization.registrationNumber || "Pending"}
                </p>
              </div>
              <Badge className={organization.approvedStatus === "approved" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}>
                {organization.approvedStatus}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {documents.length}
            </div>
            <p className="text-sm text-gray-600">Total Documents</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {documents.filter(d => d.status === "uploaded").length}
            </div>
            <p className="text-sm text-gray-600">Uploaded</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">
              {documents.filter(d => d.status === "missing" && d.required).length}
            </div>
            <p className="text-sm text-gray-600">Required Missing</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {documents.filter(d => d.status === "pending").length}
            </div>
            <p className="text-sm text-gray-600">Pending Review</p>
          </CardContent>
        </Card>
      </div>

      {/* Documents List */}
      <Card>
        <CardHeader>
          <CardTitle>All Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <DocumentsList 
            documents={documents}
            uploading={uploading}
            fileInputRefs={fileInputRefs}
            onFileSelect={handleFileSelect}
            onFileChange={handleFileChange}
            onViewDocument={handleViewDocument}
            onDownloadDocument={handleDownloadDocument}
            getStatusIcon={getStatusIcon}
            getStatusColor={getStatusColor}
          />
        </CardContent>
      </Card>
    </div>
  );
}

interface DocumentsListProps {
  documents: OrganizationDocument[];
  uploading: string | null;
  fileInputRefs: React.MutableRefObject<{ [key: string]: HTMLInputElement | null }>;
  onFileSelect: (type: DocumentType) => void;
  onFileChange: (type: DocumentType, event: React.ChangeEvent<HTMLInputElement>) => void;
  onViewDocument: (url: string, name: string) => void;
  onDownloadDocument: (url: string, name: string) => void;
  getStatusIcon: (status: string) => React.ReactElement;
  getStatusColor: (status: string) => string;
}

function DocumentsList({
  documents,
  uploading,
  fileInputRefs,
  onFileSelect,
  onFileChange,
  onViewDocument,
  onDownloadDocument,
  getStatusIcon,
  getStatusColor,
}: DocumentsListProps) {
  if (documents.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No documents in this category</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {documents.map((document) => (
        <Card key={document.type} className="border border-gray-200">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  {getStatusIcon(document.status)}
                  <h3 className="font-semibold text-lg">{document.name}</h3>
                  {document.required && (
                    <Badge variant="outline" className="text-xs">Required</Badge>
                  )}
                  <Badge className={getStatusColor(document.status)}>
                    {document.status}
                  </Badge>
                </div>
                <p className="text-gray-600 text-sm">
                  {document.required 
                    ? "This document is required for organization approval" 
                    : "This document is optional but recommended"}
                </p>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {document.status === "uploaded" && document.url ? (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewDocument(document.url!, document.name)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDownloadDocument(document.url!, document.name)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </>
                ) : null}
                
                <Button
                  variant={document.status === "missing" ? "default" : "outline"}
                  size="sm"
                  onClick={() => onFileSelect(document.type)}
                  disabled={uploading === document.type}
                >
                  {uploading === document.type ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      {document.status === "uploaded" ? "Replace" : "Upload"}
                    </>
                  )}
                </Button>
                
                <input
                  ref={(el) => {
                    fileInputRefs.current[document.type] = el;
                  }}
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  className="hidden"
                  onChange={(e) => onFileChange(document.type, e)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
