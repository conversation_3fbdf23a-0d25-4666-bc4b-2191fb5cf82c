"use client";

import React, { useState, useEffect } from "react";
import { getCurrentUser } from "@/utils/auth.utils";
import { User } from "@/services/auth.services";
import { getNgoCertificates, type Certificate, type CertificateFilter } from "@/services/certificates.services";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Download, Search, QrCode, ExternalLink, FileText } from "lucide-react";

export default function CertificatesPage() {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const currentUser = await getCurrentUser();
        const accessToken = localStorage.getItem("accessToken");
        console.log("Current user:", currentUser); // Debug log
        console.log("Access token:", accessToken ? "Present" : "Missing"); // Debug log
        setUser(currentUser);
        setToken(accessToken);
        
        if (currentUser?.ngoId && accessToken) {
          console.log("Fetching certificates for NGO:", currentUser.ngoId); // Debug log
          await fetchCertificates(currentUser.ngoId, accessToken);
        } else {
          console.log("Missing ngoId or token:", { ngoId: currentUser?.ngoId, hasToken: !!accessToken }); // Debug log
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
      }
    };

    initializeAuth();
  }, []);

  useEffect(() => {
    if (user?.ngoId && token) {
      fetchCertificates(user.ngoId, token);
    }
  }, [statusFilter, typeFilter]);

  const fetchCertificates = async (ngoId?: string, accessToken?: string) => {
    const currentNgoId = ngoId || user?.ngoId;
    const currentToken = accessToken || token;
    
    console.log("=== CERTIFICATES API DEBUG ===");
    console.log("NGO ID:", currentNgoId);
    console.log("Token present:", !!currentToken);
    
    if (!currentNgoId || !currentToken) {
      console.error("Missing required data for certificates fetch");
      return;
    }

    try {
      setLoading(true);
      
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (statusFilter !== "all") {
        queryParams.append("validityStatus", statusFilter);
      }
      if (typeFilter !== "all") {
        queryParams.append("certificateType", typeFilter);
      }
      
      // Direct API call to ensure NGO-specific filtering
      const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3009"}/api/certificates/${currentNgoId}?${queryParams.toString()}`;
      console.log("Certificates API URL:", apiUrl);
      
      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${currentToken}`,
          "Content-Type": "application/json"
        }
      });
      
      console.log("Certificates response status:", response.status);
      
      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
      }
      
      const data = await response.json();
      console.log("Certificates raw response:", data);
      
      if (data.status === "success" && data.data && data.data.certificates) {
        console.log("Setting certificates:", data.data.certificates);
        setCertificates(data.data.certificates);
      } else {
        console.warn("No certificates found or invalid response format");
        setCertificates([]);
      }
    } catch (error) {
      console.error("Error fetching certificates:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      alert(`Failed to load certificates: ${errorMessage}`);
      setCertificates([]);
    } finally {
      setLoading(false);
    }
  };

  const filteredCertificates = certificates.filter((cert) =>
    cert.certificateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cert.organizationName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "valid":
        return "bg-green-100 text-green-800 border-green-200";
      case "expired":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "registration certificate":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "subscription certificate":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleDownload = (certificateUrl: string, certificateNumber: string) => {
    if (certificateUrl) {
      const link = document.createElement("a");
      link.href = certificateUrl;
      link.download = `certificate-${certificateNumber}.pdf`;
      link.click();
    } else {
      alert("Certificate file not available");
    }
  };

  const handleViewQR = (qrCodeUrl: string) => {
    if (qrCodeUrl) {
      window.open(qrCodeUrl, "_blank");
    } else {
      alert("QR code not available");
    }
  };

  const handleVerify = (verificationUrl: string) => {
    if (verificationUrl) {
      window.open(verificationUrl, "_blank");
    } else {
      alert("Verification URL not available");
    }
  };

  if (!user?.ngoId) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-gray-500">
              You need to be associated with an organization to view certificates.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Certificates</h1>
          <p className="text-gray-600 mt-2">
            View and manage your organization's certificates
          </p>
          <div className="mt-2 text-sm text-gray-500">
            Debug: User ID: {user?._id}, NGO ID: {user?.ngoId}, Token: {token ? "Present" : "Missing"}
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by certificate number or organization name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full md:w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="valid">Valid</option>
              <option value="expired">Expired</option>
              <option value="pending">Pending</option>
            </select>
            <select 
              value={typeFilter} 
              onChange={(e) => setTypeFilter(e.target.value)}
              className="w-full md:w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="registration certificate">Registration</option>
              <option value="subscription certificate">Subscription</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {certificates.length}
            </div>
            <p className="text-sm text-gray-600">Total Certificates</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {certificates.filter(c => c.status === "valid").length}
            </div>
            <p className="text-sm text-gray-600">Valid Certificates</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">
              {certificates.filter(c => c.status === "expired").length}
            </div>
            <p className="text-sm text-gray-600">Expired Certificates</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {certificates.filter(c => c.status === "pending").length}
            </div>
            <p className="text-sm text-gray-600">Pending Certificates</p>
          </CardContent>
        </Card>
      </div>

      {/* Certificates List */}
      <Card>
        <CardHeader>
          <CardTitle>Certificates</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading certificates...</p>
            </div>
          ) : filteredCertificates.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">
                {searchTerm || statusFilter !== "all" || typeFilter !== "all"
                  ? "No certificates match your search criteria"
                  : "No certificates found for your organization"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCertificates.map((certificate) => (
                <Card key={certificate.id} className="border border-gray-200">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">
                            {certificate.certificateNumber}
                          </h3>
                          <Badge className={getStatusColor(certificate.status)}>
                            {certificate.status}
                          </Badge>
                          <Badge className={getTypeColor(certificate.certificateType)}>
                            {certificate.certificateType}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-1">
                          Organization: {certificate.organizationName}
                        </p>
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-gray-500">
                          <span>
                            Issued: {new Date(certificate.issueDate).toLocaleDateString()}
                          </span>
                          <span className="hidden sm:inline">•</span>
                          <span>
                            Expires: {new Date(certificate.expiryDate).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownload(certificate.certificateUrl, certificate.certificateNumber)}
                          disabled={!certificate.certificateUrl}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewQR(certificate.qrCodeUrl)}
                          disabled={!certificate.qrCodeUrl}
                        >
                          <QrCode className="h-4 w-4 mr-2" />
                          QR Code
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleVerify(certificate.verificationUrl)}
                          disabled={!certificate.verificationUrl}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Verify
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
}
